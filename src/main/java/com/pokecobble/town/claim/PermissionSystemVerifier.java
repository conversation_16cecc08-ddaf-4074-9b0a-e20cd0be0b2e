package com.pokecobble.town.claim;

import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.Pokecobbleclaim;

/**
 * Simple verification class to test the fixed permission system logic.
 * This can be called during mod initialization to verify the fixes work correctly.
 */
public class PermissionSystemVerifier {
    
    /**
     * Runs verification tests for the permission system fixes.
     * @return true if all tests pass, false otherwise
     */
    public static boolean verifyPermissionSystemFixes() {
        try {
            Pokecobbleclaim.LOGGER.info("Starting permission system verification...");
            
            boolean allTestsPassed = true;
            
            // Test 1: Individual rank permissions for build
            allTestsPassed &= testIndividualRankPermissions_Build();
            
            // Test 2: Individual rank permissions for interact
            allTestsPassed &= testIndividualRankPermissions_Interact();
            
            // Test 3: Admin ranks always have permissions
            allTestsPassed &= testAdminRanksAlwaysHavePermissions();
            
            // Test 4: Null rank handling
            allTestsPassed &= testNullRankPermissions();
            
            if (allTestsPassed) {
                Pokecobbleclaim.LOGGER.info("✓ All permission system verification tests PASSED");
            } else {
                Pokecobbleclaim.LOGGER.error("✗ Some permission system verification tests FAILED");
            }
            
            return allTestsPassed;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during permission system verification", e);
            return false;
        }
    }
    
    private static boolean testIndividualRankPermissions_Build() {
        try {
            ClaimTag testTag = new ClaimTag("Test Tag", 0xFF0000);
            testTag.setMinRankBuild(TownPlayerRank.MEMBER);
            
            RankPermissions permissions = testTag.getRankPermissions();
            
            // Only MEMBER should have build permission (plus admin ranks)
            boolean ownerHasPermission = permissions.hasPermission(TownPlayerRank.OWNER, 0);
            boolean adminViewerHasPermission = permissions.hasPermission(TownPlayerRank.ADMIN_VIEWER, 0);
            boolean adminNoPermission = !permissions.hasPermission(TownPlayerRank.ADMIN, 0);
            boolean moderatorNoPermission = !permissions.hasPermission(TownPlayerRank.MODERATOR, 0);
            boolean memberHasPermission = permissions.hasPermission(TownPlayerRank.MEMBER, 0);
            boolean visitorNoPermission = !permissions.hasPermission(TownPlayerRank.VISITOR, 0);
            boolean nonMemberNoPermission = !permissions.hasPermission(null, 0);
            
            boolean testPassed = ownerHasPermission && adminViewerHasPermission && adminNoPermission && 
                                moderatorNoPermission && memberHasPermission && visitorNoPermission && nonMemberNoPermission;
            
            if (testPassed) {
                Pokecobbleclaim.LOGGER.info("✓ Individual rank permissions (build) test PASSED");
            } else {
                Pokecobbleclaim.LOGGER.error("✗ Individual rank permissions (build) test FAILED");
                Pokecobbleclaim.LOGGER.error("  OWNER: {} (should be true)", ownerHasPermission);
                Pokecobbleclaim.LOGGER.error("  ADMIN_VIEWER: {} (should be true)", adminViewerHasPermission);
                Pokecobbleclaim.LOGGER.error("  ADMIN: {} (should be false)", !adminNoPermission);
                Pokecobbleclaim.LOGGER.error("  MODERATOR: {} (should be false)", !moderatorNoPermission);
                Pokecobbleclaim.LOGGER.error("  MEMBER: {} (should be true)", memberHasPermission);
                Pokecobbleclaim.LOGGER.error("  VISITOR: {} (should be false)", !visitorNoPermission);
            }
            
            return testPassed;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error in individual rank permissions (build) test", e);
            return false;
        }
    }
    
    private static boolean testIndividualRankPermissions_Interact() {
        try {
            ClaimTag testTag = new ClaimTag("Test Tag", 0xFF0000);
            testTag.setMinRankInteract(TownPlayerRank.MODERATOR);
            
            RankPermissions permissions = testTag.getRankPermissions();
            
            // Only MODERATOR should have interact permission (plus admin ranks)
            boolean ownerHasPermission = permissions.hasPermission(TownPlayerRank.OWNER, 1);
            boolean adminViewerHasPermission = permissions.hasPermission(TownPlayerRank.ADMIN_VIEWER, 1);
            boolean adminNoPermission = !permissions.hasPermission(TownPlayerRank.ADMIN, 1);
            boolean moderatorHasPermission = permissions.hasPermission(TownPlayerRank.MODERATOR, 1);
            boolean memberNoPermission = !permissions.hasPermission(TownPlayerRank.MEMBER, 1);
            boolean visitorNoPermission = !permissions.hasPermission(TownPlayerRank.VISITOR, 1);
            
            boolean testPassed = ownerHasPermission && adminViewerHasPermission && adminNoPermission && 
                                moderatorHasPermission && memberNoPermission && visitorNoPermission;
            
            if (testPassed) {
                Pokecobbleclaim.LOGGER.info("✓ Individual rank permissions (interact) test PASSED");
            } else {
                Pokecobbleclaim.LOGGER.error("✗ Individual rank permissions (interact) test FAILED");
            }
            
            return testPassed;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error in individual rank permissions (interact) test", e);
            return false;
        }
    }
    
    private static boolean testAdminRanksAlwaysHavePermissions() {
        try {
            ClaimTag testTag = new ClaimTag("Test Tag", 0xFF0000);
            testTag.setMinRankBuild(TownPlayerRank.VISITOR);
            testTag.setMinRankInteract(TownPlayerRank.MEMBER);
            testTag.setMinRankContainers(TownPlayerRank.MODERATOR);
            
            RankPermissions permissions = testTag.getRankPermissions();
            
            // Admin ranks should always have all permissions regardless of settings
            boolean ownerHasAllPermissions = true;
            boolean adminViewerHasAllPermissions = true;
            
            for (int i = 0; i < 8; i++) {
                if (!permissions.hasPermission(TownPlayerRank.OWNER, i)) {
                    ownerHasAllPermissions = false;
                }
                if (!permissions.hasPermission(TownPlayerRank.ADMIN_VIEWER, i)) {
                    adminViewerHasAllPermissions = false;
                }
            }
            
            boolean testPassed = ownerHasAllPermissions && adminViewerHasAllPermissions;
            
            if (testPassed) {
                Pokecobbleclaim.LOGGER.info("✓ Admin ranks always have permissions test PASSED");
            } else {
                Pokecobbleclaim.LOGGER.error("✗ Admin ranks always have permissions test FAILED");
            }
            
            return testPassed;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error in admin ranks test", e);
            return false;
        }
    }
    
    private static boolean testNullRankPermissions() {
        try {
            ClaimTag testTag = new ClaimTag("Test Tag", 0xFF0000);
            testTag.setMinRankBuild(null);
            
            RankPermissions permissions = testTag.getRankPermissions();
            
            // Only admin ranks should have permissions when minimum rank is null
            boolean ownerHasPermission = permissions.hasPermission(TownPlayerRank.OWNER, 0);
            boolean adminViewerHasPermission = permissions.hasPermission(TownPlayerRank.ADMIN_VIEWER, 0);
            boolean adminNoPermission = !permissions.hasPermission(TownPlayerRank.ADMIN, 0);
            boolean moderatorNoPermission = !permissions.hasPermission(TownPlayerRank.MODERATOR, 0);
            boolean memberNoPermission = !permissions.hasPermission(TownPlayerRank.MEMBER, 0);
            boolean visitorNoPermission = !permissions.hasPermission(TownPlayerRank.VISITOR, 0);
            
            boolean testPassed = ownerHasPermission && adminViewerHasPermission && adminNoPermission && 
                                moderatorNoPermission && memberNoPermission && visitorNoPermission;
            
            if (testPassed) {
                Pokecobbleclaim.LOGGER.info("✓ Null rank permissions test PASSED");
            } else {
                Pokecobbleclaim.LOGGER.error("✗ Null rank permissions test FAILED");
            }
            
            return testPassed;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error in null rank permissions test", e);
            return false;
        }
    }
}
