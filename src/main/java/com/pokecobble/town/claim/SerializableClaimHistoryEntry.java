package com.pokecobble.town.claim;

import net.minecraft.util.math.ChunkPos;
import java.util.Date;
import java.util.UUID;

/**
 * Serializable representation of a SimpleClaimHistoryEntry.
 * Used for JSON serialization/deserialization in the file-based persistence system.
 * Follows the same pattern as other serializable classes in the mod.
 */
public class SerializableClaimHistoryEntry {
    public String action;
    public int chunkX;
    public int chunkZ;
    public long timestamp;
    public String playerName;
    
    // Tag information (if present)
    public boolean hasTag;
    public String tagId;
    public String tagName;
    public int tagColor;
    
    // Previous tag information (for MODIFY actions)
    public boolean hasPreviousTag;
    public String previousTagId;
    public String previousTagName;
    public int previousTagColor;
    
    public SerializableClaimHistoryEntry() {
        // Default constructor for JSON deserialization
    }
    
    public SerializableClaimHistoryEntry(SimpleClaimHistoryEntry entry) {
        if (entry != null) {
            this.action = entry.getAction().name();
            this.chunkX = entry.getChunkPos().x;
            this.chunkZ = entry.getChunkPos().z;
            this.timestamp = entry.getTimestamp().getTime();
            this.playerName = entry.getPlayerName();
            
            // Serialize tag information
            ClaimTag tag = entry.getTag();
            if (tag != null) {
                this.hasTag = true;
                this.tagId = tag.getId().toString();
                this.tagName = tag.getName();
                this.tagColor = tag.getColor();
            } else {
                this.hasTag = false;
            }
            
            // Serialize previous tag information (for MODIFY actions)
            ClaimTag previousTag = entry.getPreviousTag();
            if (previousTag != null) {
                this.hasPreviousTag = true;
                this.previousTagId = previousTag.getId().toString();
                this.previousTagName = previousTag.getName();
                this.previousTagColor = previousTag.getColor();
            } else {
                this.hasPreviousTag = false;
            }
        }
    }
    
    public SimpleClaimHistoryEntry toClaimHistoryEntry() {
        try {
            // Parse action type
            SimpleClaimHistoryManager.ActionType actionType = SimpleClaimHistoryManager.ActionType.valueOf(action);
            
            // Create chunk position
            ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);
            
            // Create timestamp
            Date date = new Date(timestamp);
            
            // Create tag if present
            ClaimTag tag = null;
            if (hasTag && tagId != null && tagName != null) {
                try {
                    UUID tagUuid = UUID.fromString(tagId);
                    tag = new ClaimTag(tagUuid, tagName, "", tagColor);
                } catch (IllegalArgumentException e) {
                    // Invalid UUID, create new one
                    tag = new ClaimTag(tagName, "", tagColor);
                }
            }
            
            // Create previous tag if present (for MODIFY actions)
            ClaimTag previousTag = null;
            if (hasPreviousTag && previousTagId != null && previousTagName != null) {
                try {
                    UUID previousTagUuid = UUID.fromString(previousTagId);
                    previousTag = new ClaimTag(previousTagUuid, previousTagName, "", previousTagColor);
                } catch (IllegalArgumentException e) {
                    // Invalid UUID, create new one
                    previousTag = new ClaimTag(previousTagName, "", previousTagColor);
                }
            }
            
            // Create and return the entry
            if (actionType == SimpleClaimHistoryManager.ActionType.MODIFY && previousTag != null) {
                return new SimpleClaimHistoryEntry(actionType, chunkPos, date, playerName, tag, previousTag);
            } else {
                return new SimpleClaimHistoryEntry(actionType, chunkPos, date, playerName, tag);
            }
            
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.warn("Failed to deserialize claim history entry: " + e.getMessage());
            return null;
        }
    }
}
