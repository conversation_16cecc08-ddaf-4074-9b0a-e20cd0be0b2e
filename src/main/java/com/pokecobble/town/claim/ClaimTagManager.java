package com.pokecobble.town.claim;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayerRank;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * File-based claim tag manager that handles all claim tag operations.
 * Stores claim tag data in individual files organized by town ID, following the PermissionManager pattern.
 * 
 * File structure:
 * pokecobbleclaim/
 *   towns/
 *     {townId}/
 *       claimtags/
 *         tags.json - Claim tag definitions with names, descriptions, colors, and rank permissions
 */
public class ClaimTagManager {
    private static final ClaimTagManager INSTANCE = new ClaimTagManager();
    
    // Constants for file paths
    private static final String DATA_FOLDER = "pokecobbleclaim";
    private static final String TOWNS_FOLDER = "towns";
    private static final String CLAIM_TAGS_FOLDER = "claimtags";
    private static final String TAGS_FILE = "tags.json";
    
    // GSON instance for serialization
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    
    // Cache for claim tag data
    private final Map<UUID, List<ClaimTag>> claimTagCache = new ConcurrentHashMap<>();

    // Track active claim tag screen users by town ID
    private final Map<UUID, Set<UUID>> activeClaimTagUsers = new ConcurrentHashMap<>();

    // Track temporary state (complete unsaved state) by town ID
    private final Map<UUID, List<ClaimTag>> temporaryClaimTagState = new ConcurrentHashMap<>();

    // Track the first player (source of truth) for each town's claim tag session - only for initial state
    private final Map<UUID, UUID> claimTagSessionOwner = new ConcurrentHashMap<>();

    // Track whether initial state has been established for each town
    private final Map<UUID, Boolean> initialStateEstablished = new ConcurrentHashMap<>();
    
    // File lock for thread safety
    private final ReadWriteLock fileLock = new ReentrantReadWriteLock();
    
    private ClaimTagManager() {
        // Private constructor for singleton
    }
    
    public static ClaimTagManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * Gets the claim tags file for a specific town.
     */
    private File getClaimTagsFile(UUID townId) {
        File dataDir = new File(DATA_FOLDER);
        File townsDir = new File(dataDir, TOWNS_FOLDER);
        File townDir = new File(townsDir, townId.toString());
        File claimTagsDir = new File(townDir, CLAIM_TAGS_FOLDER);
        
        // Ensure directories exist
        if (!claimTagsDir.exists()) {
            claimTagsDir.mkdirs();
        }
        
        return new File(claimTagsDir, TAGS_FILE);
    }
    
    /**
     * Gets claim tags for a town, loading from file if not cached.
     */
    public List<ClaimTag> getClaimTags(UUID townId) {
        if (townId == null) {
            return new ArrayList<>();
        }
        
        // Check cache first
        List<ClaimTag> cachedTags = claimTagCache.get(townId);
        if (cachedTags != null) {
            // Return a defensive copy
            return new ArrayList<>(cachedTags);
        }
        
        // ALWAYS prioritize file system over Town object to ensure latest permissions are used
        List<ClaimTag> tags = loadClaimTagsFromFile(townId);
        if (tags != null) {
            Pokecobbleclaim.LOGGER.info("CLAIM TAG LOAD DEBUG: Loaded {} claim tags from FILE SYSTEM for town {}", tags.size(), townId);

            // Debug: Log the permissions of the first tag from file
            if (!tags.isEmpty()) {
                ClaimTag firstTag = tags.get(0);
                boolean[] memberPermissions = firstTag.getPermissionsForRank(com.pokecobble.town.TownPlayerRank.MEMBER);
                Pokecobbleclaim.LOGGER.info("CLAIM TAG LOAD DEBUG: File tag '{}' MEMBER permissions: {}",
                    firstTag.getName(), java.util.Arrays.toString(memberPermissions));
            }

            // Update the Town object with the file system data to keep them in sync
            Town town = TownManager.getInstance().getTown(townId);
            if (town != null) {
                town.updateClaimTags(tags);
                Pokecobbleclaim.LOGGER.info("CLAIM TAG LOAD DEBUG: Updated Town object with file system data");
            }
        } else {
            Pokecobbleclaim.LOGGER.info("CLAIM TAG LOAD DEBUG: No file found for town {}, trying Town object as fallback", townId);
            // Fallback to Town object only if no file exists
            Town town = TownManager.getInstance().getTown(townId);
            if (town != null && town.getClaimTags() != null && !town.getClaimTags().isEmpty()) {
                tags = new ArrayList<>(town.getClaimTags());
                Pokecobbleclaim.LOGGER.info("CLAIM TAG LOAD DEBUG: Loaded {} claim tags from Town object as fallback: {}", tags.size(), townId);

                // Debug: Log the permissions of the first tag from Town object
                if (!tags.isEmpty()) {
                    ClaimTag firstTag = tags.get(0);
                    boolean[] memberPermissions = firstTag.getPermissionsForRank(com.pokecobble.town.TownPlayerRank.MEMBER);
                    Pokecobbleclaim.LOGGER.info("CLAIM TAG LOAD DEBUG: Town object tag '{}' MEMBER permissions: {}",
                        firstTag.getName(), java.util.Arrays.toString(memberPermissions));
                }

                // IMMEDIATELY save to file to establish file system as primary source
                saveClaimTagsToFile(townId, tags);
                Pokecobbleclaim.LOGGER.info("CLAIM TAG LOAD DEBUG: Saved Town object data to file system for future use");
            } else {
                Pokecobbleclaim.LOGGER.info("CLAIM TAG LOAD DEBUG: No tags found for town {}, returning empty list", townId);
                tags = new ArrayList<>();
                // Note: Don't create defaults here - they should only be created during town creation
            }
        }
        
        // Cache the tags
        claimTagCache.put(townId, new ArrayList<>(tags));
        
        return new ArrayList<>(tags);
    }

    /**
     * Initializes default claim tags for a newly created town.
     * This method should only be called during town creation.
     */
    public void initializeDefaultTagsForNewTown(UUID townId) {
        if (townId == null) {
            return;
        }

        // Check if tags already exist for this town
        List<ClaimTag> existingTags = getClaimTags(townId);
        if (existingTags != null && !existingTags.isEmpty()) {
            Pokecobbleclaim.LOGGER.debug("Town {} already has claim tags, skipping default initialization", townId);
            return;
        }

        // Create default tags for the new town
        List<ClaimTag> defaultTags = createDefaultClaimTags();

        // Save the default tags for this town
        saveClaimTagsToFile(townId, defaultTags);

        // Cache the tags
        claimTagCache.put(townId, new ArrayList<>(defaultTags));

        // Update the town object with the default tags
        Town town = TownManager.getInstance().getTown(townId);
        if (town != null) {
            town.updateClaimTags(new ArrayList<>(defaultTags));
        }

        Pokecobbleclaim.LOGGER.info("Initialized {} default claim tags for new town {}", defaultTags.size(), townId);
    }

    /**
     * Creates default claim tags for a new town.
     */
    private List<ClaimTag> createDefaultClaimTags() {
        List<ClaimTag> defaultTags = new ArrayList<>();
        
        // Public tag - allows all interactions for everyone
        ClaimTag publicTag = new ClaimTag("Public", "Open to everyone", 0x00FF00);
        // Set all permissions to true for all ranks and non-members
        for (int i = 0; i < 8; i++) {
            publicTag.getRankPermissions().setPermission(null, i, true); // Non-members
            for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
                publicTag.getRankPermissions().setPermission(rank, i, true);
            }
        }
        defaultTags.add(publicTag);
        
        // Private tag - only allows town members
        ClaimTag privateTag = new ClaimTag("Private", "Town members only", 0xFF0000);
        // Set all permissions to false for non-members, true for town members
        for (int i = 0; i < 8; i++) {
            privateTag.getRankPermissions().setPermission(null, i, false); // Non-members
            for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
                privateTag.getRankPermissions().setPermission(rank, i, true);
            }
        }
        defaultTags.add(privateTag);
        
        // Residential tag - limited permissions for non-members
        ClaimTag residentialTag = new ClaimTag("Residential", "Limited access for visitors", 0x0080FF);
        // Allow basic interactions but not building/containers for non-members
        residentialTag.getRankPermissions().setPermission(null, 0, false); // Build
        residentialTag.getRankPermissions().setPermission(null, 1, true);  // Interact
        residentialTag.getRankPermissions().setPermission(null, 2, false); // Containers
        residentialTag.getRankPermissions().setPermission(null, 3, false); // Redstone
        residentialTag.getRankPermissions().setPermission(null, 4, true);  // Doors
        residentialTag.getRankPermissions().setPermission(null, 5, false); // Crops
        residentialTag.getRankPermissions().setPermission(null, 6, true);  // Animals
        residentialTag.getRankPermissions().setPermission(null, 7, true);  // Villagers
        // All permissions for town members
        for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
            for (int i = 0; i < 8; i++) {
                residentialTag.getRankPermissions().setPermission(rank, i, true);
            }
        }
        defaultTags.add(residentialTag);
        
        return defaultTags;
    }
    
    /**
     * Loads claim tags from file.
     */
    private List<ClaimTag> loadClaimTagsFromFile(UUID townId) {
        fileLock.readLock().lock();
        try {
            File tagsFile = getClaimTagsFile(townId);
            if (!tagsFile.exists()) {
                return null;
            }
            
            try (FileReader reader = new FileReader(tagsFile)) {
                SerializableClaimTagData data = GSON.fromJson(reader, SerializableClaimTagData.class);
                if (data != null && data.tags != null) {
                    List<ClaimTag> tags = new ArrayList<>();
                    for (SerializableClaimTag serializable : data.tags) {
                        ClaimTag tag = serializable.toClaimTag();
                        if (tag != null) {
                            tags.add(tag);
                        }
                    }
                    return tags;
                }
                return null;
            } catch (JsonSyntaxException e) {
                Pokecobbleclaim.LOGGER.error("Corrupted claim tags file for town " + townId + ": " + e.getMessage());
                return null;
            } catch (IOException e) {
                Pokecobbleclaim.LOGGER.error("Error reading claim tags file for town " + townId + ": " + e.getMessage());
                return null;
            }
        } finally {
            fileLock.readLock().unlock();
        }
    }
    
    /**
     * Updates claim tags for a town, saves to file, and syncs to clients.
     */
    public void updateClaimTags(UUID townId, List<ClaimTag> tags, MinecraftServer server) {
        // Update cache
        claimTagCache.put(townId, new ArrayList<>(tags));

        // Clear temporary state since changes are now saved
        clearTemporaryState(townId);

        // Save to file
        saveClaimTagsToFile(townId, tags);

        // Sync to clients
        syncClaimTagsToClients(townId, tags, server);
    }
    
    /**
     * Saves claim tags to file.
     */
    private void saveClaimTagsToFile(UUID townId, List<ClaimTag> tags) {
        Pokecobbleclaim.LOGGER.info("SAVE TO FILE DEBUG: Starting to save {} tags to file for town {}", tags.size(), townId);

        fileLock.writeLock().lock();
        try {
            File tagsFile = getClaimTagsFile(townId);
            File tempFile = File.createTempFile("claimtags_", ".tmp", tagsFile.getParentFile());

            Pokecobbleclaim.LOGGER.info("SAVE TO FILE DEBUG: Created temp file {}, target file {}", tempFile.getAbsolutePath(), tagsFile.getAbsolutePath());

            // Create serializable data
            SerializableClaimTagData data = new SerializableClaimTagData();
            data.townId = townId.toString();
            data.lastModified = System.currentTimeMillis();
            data.version = 1;
            data.tags = new ArrayList<>();

            Pokecobbleclaim.LOGGER.info("SAVE TO FILE DEBUG: About to serialize {} tags", tags.size());

            for (ClaimTag tag : tags) {
                Pokecobbleclaim.LOGGER.info("SAVE TO FILE DEBUG: Serializing tag '{}' with ID {}", tag.getName(), tag.getId());
                data.tags.add(new SerializableClaimTag(tag));
            }

            Pokecobbleclaim.LOGGER.info("SAVE TO FILE DEBUG: Serialization complete, writing to file");

            // Write to temporary file first
            try (FileWriter writer = new FileWriter(tempFile)) {
                GSON.toJson(data, writer);
            }

            // Atomic move to final location
            Files.move(tempFile.toPath(), tagsFile.toPath(), StandardCopyOption.REPLACE_EXISTING);

            Pokecobbleclaim.LOGGER.info("SAVE TO FILE DEBUG: Successfully saved claim tags to file for town {} with {} tags", townId, tags.size());
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("SAVE TO FILE ERROR: Error saving claim tags for town " + townId + ": " + e.getMessage(), e);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("SAVE TO FILE ERROR: Unexpected error saving claim tags for town " + townId + ": " + e.getMessage(), e);
        } finally {
            fileLock.writeLock().unlock();
        }
    }
    
    /**
     * Syncs claim tags to all clients in the town.
     */
    private void syncClaimTagsToClients(UUID townId, List<ClaimTag> tags, MinecraftServer server) {
        try {
            // Find the town
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Cannot sync claim tags - town " + townId + " not found");
                return;
            }
            
            // Create packet buffer
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Write town ID
            buf.writeUuid(townId);
            
            // Write number of tags
            buf.writeInt(tags.size());
            
            // Write each tag
            for (ClaimTag tag : tags) {
                writeClaimTagToBuffer(buf, tag);
            }
            
            // Send to all players in the town
            for (UUID townPlayerId : town.getPlayers()) {
                ServerPlayerEntity player = server.getPlayerManager().getPlayer(townPlayerId);
                if (player != null) {
                    ServerPlayNetworking.send(player, ClaimTagNetworkConstants.CLAIM_TAG_DATA_SYNC, buf);
                }
            }
            
            Pokecobbleclaim.LOGGER.debug("Synced claim tags for town " + townId + " to all clients with " + tags.size() + " tags");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing claim tags to clients: " + e.getMessage());
        }
    }
    
    /**
     * Writes a ClaimTag to a packet buffer.
     */
    private void writeClaimTagToBuffer(PacketByteBuf buf, ClaimTag tag) {
        buf.writeUuid(tag.getId());
        buf.writeString(tag.getName(), com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
        buf.writeString(tag.getDescription(), com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
        buf.writeInt(tag.getColor());

        // Write rank permissions
        for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
            boolean[] permissions = tag.getRankPermissions().getPermissions(rank);
            for (boolean permission : permissions) {
                buf.writeBoolean(permission);
            }
        }

        // Write non-member permissions
        boolean[] nonMemberPermissions = tag.getRankPermissions().getPermissions(null);
        for (boolean permission : nonMemberPermissions) {
            buf.writeBoolean(permission);
        }
    }

    /**
     * Syncs all claim tags to a specific client when they connect.
     */
    public void syncAllClaimTagsToClient(ServerPlayerEntity player) {
        try {
            UUID playerId = player.getUuid();

            // Find the town this player belongs to
            Town town = TownManager.getInstance().getPlayerTown(playerId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.debug("Player " + playerId + " is not in a town - no claim tags to sync");
                return;
            }

            // Get claim tags for the town
            List<ClaimTag> tags = getClaimTags(town.getId());
            if (tags.isEmpty()) {
                Pokecobbleclaim.LOGGER.debug("No claim tags found for town " + town.getId() + " - syncing empty tags");
            }

            // Create packet buffer
            PacketByteBuf buf = PacketByteBufs.create();

            // Write town ID
            buf.writeUuid(town.getId());

            // Write number of tags
            buf.writeInt(tags.size());

            // Write each tag
            for (ClaimTag tag : tags) {
                writeClaimTagToBuffer(buf, tag);
            }

            // Send to the specific player
            ServerPlayNetworking.send(player, ClaimTagNetworkConstants.CLAIM_TAG_DATA_SYNC, buf);

            Pokecobbleclaim.LOGGER.debug("Synced claim tags for player " + playerId + " to client on connect with " + tags.size() + " tags");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing claim tags to client on connect: " + e.getMessage());
        }
    }

    /**
     * Adds a player to the active claim tag screen users for a town.
     */
    public void addActiveUser(UUID townId, UUID playerId) {
        activeClaimTagUsers.computeIfAbsent(townId, k -> ConcurrentHashMap.newKeySet()).add(playerId);
        Pokecobbleclaim.LOGGER.debug("Added player {} to active claim tag users for town {}", playerId, townId);
    }

    /**
     * Removes a player from the active claim tag screen users for a town.
     */
    public void removeActiveUser(UUID townId, UUID playerId) {
        Set<UUID> users = activeClaimTagUsers.get(townId);
        if (users != null) {
            users.remove(playerId);

            // Check if the leaving player was the session owner
            UUID sessionOwner = claimTagSessionOwner.get(townId);
            if (playerId.equals(sessionOwner)) {
                if (!users.isEmpty()) {
                    // Transfer ownership to the next active user
                    UUID newOwner = users.iterator().next();
                    claimTagSessionOwner.put(townId, newOwner);
                    Pokecobbleclaim.LOGGER.debug("Session ownership for town {} transferred from {} to {}", townId, playerId, newOwner);
                } else {
                    // No more users - clear session owner
                    claimTagSessionOwner.remove(townId);
                    Pokecobbleclaim.LOGGER.debug("Cleared session owner for town {} - no active users", townId);
                }
            }

            if (users.isEmpty()) {
                activeClaimTagUsers.remove(townId);
                // Clear temporary state when no users are active
                clearTemporaryState(townId);
                // Reset initial state tracking for next session
                initialStateEstablished.remove(townId);
                Pokecobbleclaim.LOGGER.debug("Cleared temporary state and reset session for town {} - no active users", townId);
            }
        }
        Pokecobbleclaim.LOGGER.debug("Removed player {} from active claim tag users for town {}", playerId, townId);
    }

    /**
     * Gets the list of active claim tag screen users for a town.
     */
    public Set<UUID> getActiveUsers(UUID townId) {
        return activeClaimTagUsers.getOrDefault(townId, Collections.emptySet());
    }

    /**
     * Cleans up offline players from all active users lists.
     * This is a safety method to handle edge cases where players might still be in active lists.
     */
    public void cleanupOfflineActiveUsers(MinecraftServer server) {
        try {
            for (Map.Entry<UUID, Set<UUID>> entry : activeClaimTagUsers.entrySet()) {
                UUID townId = entry.getKey();
                Set<UUID> townActiveUsers = entry.getValue();

                // Create a copy to avoid concurrent modification
                Set<UUID> usersToRemove = new HashSet<>();
                for (UUID userId : townActiveUsers) {
                    ServerPlayerEntity player = server.getPlayerManager().getPlayer(userId);
                    if (player == null) {
                        usersToRemove.add(userId);
                    }
                }

                // Remove offline players
                for (UUID userId : usersToRemove) {
                    removeActiveUser(townId, userId);
                    Pokecobbleclaim.LOGGER.debug("Periodic cleanup: Removed offline player {} from claim tag active users for town {}",
                        userId, townId);
                }

                // If any users were removed, sync the updated list
                if (!usersToRemove.isEmpty()) {
                    syncActiveUsersToTown(townId, server);
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during periodic cleanup of claim tag active users: {}", e.getMessage());
        }
    }

    /**
     * Gets the session owner (source of truth) for a town's claim tag session.
     */
    public UUID getSessionOwner(UUID townId) {
        return claimTagSessionOwner.get(townId);
    }

    /**
     * Updates the temporary state with complete current state.
     */
    public void updateTemporaryState(UUID townId, List<ClaimTag> currentTags) {
        // Create a deep copy of the tags to avoid reference issues
        List<ClaimTag> tempState = new ArrayList<>();
        for (ClaimTag tag : currentTags) {
            tempState.add(tag.copy());
        }
        temporaryClaimTagState.put(townId, tempState);
        Pokecobbleclaim.LOGGER.debug("Updated temporary state for town {} with {} tags", townId, tempState.size());
    }

    /**
     * Gets the current temporary state or saved state if no temporary state exists.
     * Returns temporary state if available (has active users), otherwise saved state.
     */
    public List<ClaimTag> getTemporaryOrSavedState(UUID townId) {
        // Check if there are active users and temporary state exists
        Set<UUID> activeUsers = getActiveUsers(townId);
        if (!activeUsers.isEmpty()) {
            List<ClaimTag> tempState = temporaryClaimTagState.get(townId);
            if (tempState != null && !tempState.isEmpty()) {
                Pokecobbleclaim.LOGGER.debug("Returning temporary state for town {} with {} tags", townId, tempState.size());
                return new ArrayList<>(tempState);
            }
        }

        // Fall back to saved state
        List<ClaimTag> savedState = getClaimTags(townId);
        Pokecobbleclaim.LOGGER.debug("Returning saved state for town {} with {} tags", townId, savedState.size());
        return savedState;
    }

    /**
     * Clears the temporary state when changes are saved or no users are active.
     */
    public void clearTemporaryState(UUID townId) {
        temporaryClaimTagState.remove(townId);
        Pokecobbleclaim.LOGGER.debug("Cleared temporary state for town {}", townId);
    }

    /**
     * Checks if there are unsaved changes for a town.
     */
    public boolean hasTemporaryChanges(UUID townId) {
        return temporaryClaimTagState.containsKey(townId) && !getActiveUsers(townId).isEmpty();
    }



    /**
     * Syncs active users list to all town members.
     */
    public void syncActiveUsersToTown(UUID townId, MinecraftServer server) {
        try {
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) {
                return;
            }

            Set<UUID> activeUsers = getActiveUsers(townId);

            // Filter out offline players and clean up the active users set
            Set<UUID> onlineActiveUsers = new HashSet<>();
            for (UUID userId : activeUsers) {
                ServerPlayerEntity player = server.getPlayerManager().getPlayer(userId);
                if (player != null) {
                    onlineActiveUsers.add(userId);
                } else {
                    // Player is offline, remove them from active users
                    removeActiveUser(townId, userId);
                    Pokecobbleclaim.LOGGER.debug("Removed offline player {} from claim tag active users for town {}", userId, townId);
                }
            }

            // Create packet buffer
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(townId);
            buf.writeInt(onlineActiveUsers.size());

            for (UUID userId : onlineActiveUsers) {
                buf.writeUuid(userId);
                // Get player name for display (we know player is online at this point)
                ServerPlayerEntity player = server.getPlayerManager().getPlayer(userId);
                buf.writeString(player.getName().getString());
            }

            // Send to all town members
            for (UUID townPlayerId : town.getPlayers()) {
                ServerPlayerEntity player = server.getPlayerManager().getPlayer(townPlayerId);
                if (player != null) {
                    ServerPlayNetworking.send(player, ClaimTagNetworkConstants.CLAIM_TAG_ACTIVE_USERS_SYNC, buf);
                }
            }

            Pokecobbleclaim.LOGGER.debug("Synced {} active claim tag users for town {}", activeUsers.size(), townId);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing active users for town {}: {}", townId, e.getMessage());
        }
    }

    /**
     * Registers server-side network handlers for claim tag-related packets.
     * This should only be called on the server side.
     */
    public static void registerServerNetworkHandlers() {
        // Handler for claim tag data requests
        ServerPlayNetworking.registerGlobalReceiver(ClaimTagNetworkConstants.CLAIM_TAG_DATA_REQUEST, (server, player, handler, buf, responseSender) -> {
            try {
                // Read packet data
                UUID playerId = buf.readUuid();
                UUID townId = buf.readUuid();

                // Validate player
                if (!player.getUuid().equals(playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Player UUID mismatch in claim tag data request");
                    return;
                }

                // Find the town
                Town town = TownManager.getInstance().getTown(townId);
                if (town == null) {
                    Pokecobbleclaim.LOGGER.warn("Cannot send claim tag data - town " + townId + " not found");
                    return;
                }

                // Verify player is in the town
                if (!town.getPlayers().contains(playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Player " + playerId + " is not in town " + townId + " - denying claim tag data request");
                    return;
                }

                // Get claim tags and send response
                List<ClaimTag> tags = getInstance().getClaimTags(townId);

                Pokecobbleclaim.LOGGER.info("CLAIM TAG REQUEST: Player " + playerId + " requested tags for town " + townId + ", sending " + tags.size() + " tags");
                if (!tags.isEmpty()) {
                    ClaimTag firstTag = tags.get(0);
                    Pokecobbleclaim.LOGGER.info("CLAIM TAG REQUEST: First tag name: '" + firstTag.getName() + "', color: " + firstTag.getColor());
                }

                // Create response packet
                PacketByteBuf responseBuf = PacketByteBufs.create();
                responseBuf.writeUuid(townId);
                responseBuf.writeInt(tags.size());

                for (ClaimTag tag : tags) {
                    getInstance().writeClaimTagToBuffer(responseBuf, tag);
                }

                ServerPlayNetworking.send(player, ClaimTagNetworkConstants.CLAIM_TAG_DATA_RESPONSE, responseBuf);

                Pokecobbleclaim.LOGGER.debug("Sent claim tag data response to player " + playerId + " with " + tags.size() + " tags");

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling claim tag data request: " + e.getMessage(), e);
            }
        });

        // Handler for claim tag updates
        ServerPlayNetworking.registerGlobalReceiver(ClaimTagNetworkConstants.CLAIM_TAG_UPDATE, (server, player, handler, buf, responseSender) -> {
            try {
                // Read packet data
                UUID playerId = buf.readUuid();
                UUID townId = buf.readUuid();
                int tagCount = buf.readInt();

                // Validate player
                if (!player.getUuid().equals(playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Player UUID mismatch in claim tag update");
                    return;
                }

                // Find the town
                Town town = TownManager.getInstance().getTown(townId);
                if (town == null) {
                    Pokecobbleclaim.LOGGER.warn("Cannot update claim tags - town " + townId + " not found");
                    return;
                }

                // Verify player is in the town
                if (!town.getPlayers().contains(playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Player " + playerId + " is not in town " + townId + " - denying claim tag update");
                    return;
                }

                // Read tags from packet
                List<ClaimTag> tags = new ArrayList<>();
                for (int i = 0; i < tagCount; i++) {
                    ClaimTag tag = getInstance().readClaimTagFromBuffer(buf);
                    if (tag != null) {
                        tags.add(tag);
                    }
                }

                // Update claim tags in ClaimTagManager (file system)
                getInstance().updateClaimTags(townId, tags, server);

                // CRITICAL: Also update the Town object's claim tags so they persist in town data
                town.updateClaimTags(tags);

                // Save the town data to ensure persistence
                TownManager.getInstance().saveTown(town);

                Pokecobbleclaim.LOGGER.info("CLAIM TAG SAVE: Updated claim tags for town " + townId + " with " + tags.size() + " tags from player " + playerId);
                Pokecobbleclaim.LOGGER.info("CLAIM TAG SAVE: Town object now has " + town.getClaimTags().size() + " tags");

                // Log the first tag for debugging
                if (!tags.isEmpty()) {
                    ClaimTag firstTag = tags.get(0);
                    Pokecobbleclaim.LOGGER.info("CLAIM TAG SAVE: First tag name: '" + firstTag.getName() + "', color: " + firstTag.getColor());
                }

                // DEBUG: Log detailed permissions for all tags
                for (ClaimTag tag : tags) {
                    Pokecobbleclaim.LOGGER.info("CLAIM TAG SAVE: Tag '{}' permissions:", tag.getName());
                    for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
                        boolean[] permissions = tag.getPermissionsForRank(rank);
                        Pokecobbleclaim.LOGGER.info("CLAIM TAG SAVE:   {} - Build={}, Interact={}, Containers={}, Redstone={}, Doors={}, Crops={}, Animals={}, Villagers={}",
                            rank.getDisplayName(),
                            permissions[0], permissions[1], permissions[2], permissions[3],
                            permissions[4], permissions[5], permissions[6], permissions[7]);
                    }
                    // Also log non-member permissions
                    boolean[] nonMemberPerms = tag.getPermissionsForRank(null);
                    Pokecobbleclaim.LOGGER.info("CLAIM TAG SAVE:   Non-Member - Build={}, Interact={}, Containers={}, Redstone={}, Doors={}, Crops={}, Animals={}, Villagers={}",
                        nonMemberPerms[0], nonMemberPerms[1], nonMemberPerms[2], nonMemberPerms[3],
                        nonMemberPerms[4], nonMemberPerms[5], nonMemberPerms[6], nonMemberPerms[7]);
                }

                // CRITICAL: Clear all permission caches after saving tag data
                if (com.pokecobble.town.claim.v2.PermissionSystemV2.isInitialized()) {
                    try {
                        // Force clear Permission System v2 cache
                        com.pokecobble.town.claim.v2.PermissionSystemV2.getInstance()
                            .getPermissionEngine().clearCache();
                        Pokecobbleclaim.LOGGER.info("CLAIM TAG SAVE: FORCED clear of Permission System v2 cache for town {}", townId);

                        // Force clear ChunkOwnershipManager cache
                        com.pokecobble.town.claim.v2.PermissionSystemV2.getInstance()
                            .getPermissionEngine().getOwnershipManager().clearCache();
                        Pokecobbleclaim.LOGGER.info("CLAIM TAG SAVE: FORCED clear of ChunkOwnershipManager cache for town {}", townId);

                        // Force clear TownChunkDataManager cache
                        com.pokecobble.town.chunk.TownChunkDataManager.getInstance().invalidateTownCache(townId);
                        Pokecobbleclaim.LOGGER.info("CLAIM TAG SAVE: FORCED clear of TownChunkDataManager cache for town {}", townId);

                    } catch (Exception e) {
                        Pokecobbleclaim.LOGGER.error("Error clearing permission caches for town " + townId, e);
                    }
                }

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling claim tag update: " + e.getMessage(), e);
            }
        });

        // Handler for claim tag screen opened
        ServerPlayNetworking.registerGlobalReceiver(ClaimTagNetworkConstants.CLAIM_TAG_SCREEN_OPENED, (server, player, handler, buf, responseSender) -> {
            try {
                UUID playerId = buf.readUuid();
                UUID townId = buf.readUuid();

                // Validate player
                if (!player.getUuid().equals(playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Player UUID mismatch in claim tag screen opened");
                    return;
                }

                // Find the town
                Town town = TownManager.getInstance().getTown(townId);
                if (town == null) {
                    Pokecobbleclaim.LOGGER.warn("Cannot track claim tag screen - town " + townId + " not found");
                    return;
                }

                // Verify player is in the town
                if (!town.getPlayers().contains(playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Player " + playerId + " is not in town " + townId + " - denying claim tag screen tracking");
                    return;
                }

                // Check if there are already active users before adding this player
                Set<UUID> currentActiveUsers = getInstance().getActiveUsers(townId);
                boolean hasOtherActiveUsers = !currentActiveUsers.isEmpty();

                // Add player to active users
                getInstance().addActiveUser(townId, playerId);

                // If there were already active users, immediately send them the current temporary state
                if (hasOtherActiveUsers) {
                    List<ClaimTag> currentState = getInstance().getTemporaryOrSavedState(townId);

                    // Send the current state to the newly joined player
                    PacketByteBuf stateBuf = PacketByteBufs.create();
                    stateBuf.writeUuid(townId);
                    stateBuf.writeInt(currentState.size());

                    for (ClaimTag tag : currentState) {
                        getInstance().writeClaimTagToBuffer(stateBuf, tag);
                    }

                    ServerPlayNetworking.send(player, ClaimTagNetworkConstants.CLAIM_TAG_TEMP_STATE_RESPONSE, stateBuf);

                    Pokecobbleclaim.LOGGER.debug("Sent current state with {} tags to newly joined player {} for town {} (other users were already active)",
                        currentState.size(), playerId, townId);
                } else {
                    // This player is the first to open the screen - they become the session owner (source of truth)
                    getInstance().claimTagSessionOwner.put(townId, playerId);
                    Pokecobbleclaim.LOGGER.debug("Player {} is the first to open claim tag screen for town {} - they are now the session owner", playerId, townId);
                }

                // Sync active users to all town members
                getInstance().syncActiveUsersToTown(townId, server);

                Pokecobbleclaim.LOGGER.debug("Player {} opened claim tag screen for town {} (had other active users: {})", playerId, townId, hasOtherActiveUsers);

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling claim tag screen opened: " + e.getMessage(), e);
            }
        });

        // Handler for claim tag screen closed
        ServerPlayNetworking.registerGlobalReceiver(ClaimTagNetworkConstants.CLAIM_TAG_SCREEN_CLOSED, (server, player, handler, buf, responseSender) -> {
            try {
                UUID playerId = buf.readUuid();
                UUID townId = buf.readUuid();

                // Validate player
                if (!player.getUuid().equals(playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Player UUID mismatch in claim tag screen closed");
                    return;
                }

                // Remove player from active users
                getInstance().removeActiveUser(townId, playerId);

                // Sync active users to all town members
                getInstance().syncActiveUsersToTown(townId, server);

                Pokecobbleclaim.LOGGER.debug("Player {} closed claim tag screen for town {}", playerId, townId);

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling claim tag screen closed: " + e.getMessage(), e);
            }
        });

        // Handler for real-time tag changes
        ServerPlayNetworking.registerGlobalReceiver(ClaimTagNetworkConstants.CLAIM_TAG_REALTIME_CHANGE, (server, player, handler, buf, responseSender) -> {
            try {
                UUID playerId = buf.readUuid();
                UUID townId = buf.readUuid();
                String changeType = buf.readString();

                // Validate player
                if (!player.getUuid().equals(playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Player UUID mismatch in real-time change");
                    return;
                }

                // Find the town
                Town town = TownManager.getInstance().getTown(townId);
                if (town == null) {
                    Pokecobbleclaim.LOGGER.warn("Cannot process real-time change - town " + townId + " not found");
                    return;
                }

                // Verify player is in the town
                if (!town.getPlayers().contains(playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Player " + playerId + " is not in town " + townId + " - denying real-time change");
                    return;
                }

                // Read additional data based on change type
                List<String> additionalData = new ArrayList<>();
                while (buf.readableBytes() > 0) {
                    additionalData.add(buf.readString());
                }

                // Broadcast the change to all other active users
                getInstance().broadcastRealtimeChange(server, townId, playerId, changeType, additionalData);

                Pokecobbleclaim.LOGGER.debug("Processed real-time change: {} from player {} for town {}", changeType, playerId, townId);

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling real-time change: " + e.getMessage(), e);
            }
        });

        // Handler for temporary state updates (complete state from client)
        ServerPlayNetworking.registerGlobalReceiver(ClaimTagNetworkConstants.CLAIM_TAG_TEMP_STATE_UPDATE, (server, player, handler, buf, responseSender) -> {
            try {
                UUID playerId = buf.readUuid();
                UUID townId = buf.readUuid();
                int tagCount = buf.readInt();

                // Validate player
                if (!player.getUuid().equals(playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Player UUID mismatch in temporary state update");
                    return;
                }

                // Find the town
                Town town = TownManager.getInstance().getTown(townId);
                if (town == null) {
                    Pokecobbleclaim.LOGGER.warn("Cannot update temporary state - town " + townId + " not found");
                    return;
                }

                // Verify player is in the town
                if (!town.getPlayers().contains(playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Player " + playerId + " is not in town " + townId + " - denying temporary state update");
                    return;
                }

                // Check if initial state has been established
                boolean initialEstablished = getInstance().initialStateEstablished.getOrDefault(townId, false);

                if (!initialEstablished) {
                    // Initial state not established yet - only session owner can set it
                    UUID sessionOwner = getInstance().claimTagSessionOwner.get(townId);
                    if (sessionOwner == null) {
                        // No session owner yet - this player becomes the owner
                        getInstance().claimTagSessionOwner.put(townId, playerId);
                        Pokecobbleclaim.LOGGER.debug("Player {} became session owner for town {} (no previous owner)", playerId, townId);
                    } else if (!sessionOwner.equals(playerId)) {
                        // This player is not the session owner - ignore their initial update
                        Pokecobbleclaim.LOGGER.debug("Ignoring initial state update from player {} for town {} - session owner is {}", playerId, townId, sessionOwner);
                        return;
                    }

                    // Mark initial state as established after this update
                    getInstance().initialStateEstablished.put(townId, true);
                    Pokecobbleclaim.LOGGER.debug("Initial state established for town {} by session owner {}", townId, playerId);
                } else {
                    // Initial state already established - accept updates from any active user
                    Pokecobbleclaim.LOGGER.debug("Accepting state update from player {} for town {} (initial state already established)", playerId, townId);
                }

                // Read all tags from the packet
                List<ClaimTag> newTempState = new ArrayList<>();
                for (int i = 0; i < tagCount; i++) {
                    ClaimTag tag = getInstance().readClaimTagFromBuffer(buf);
                    if (tag != null) {
                        newTempState.add(tag);
                    }
                }

                // Update temporary state
                getInstance().updateTemporaryState(townId, newTempState);

                // Broadcast the complete state to all other active users
                getInstance().broadcastTemporaryStateToActiveUsers(server, townId, playerId, newTempState);

                Pokecobbleclaim.LOGGER.debug("Updated temporary state with {} tags from session owner {} for town {}", newTempState.size(), playerId, townId);

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling temporary state update: " + e.getMessage(), e);
            }
        });

        // Handler for temporary state requests (when new users open the screen)
        ServerPlayNetworking.registerGlobalReceiver(ClaimTagNetworkConstants.CLAIM_TAG_TEMP_STATE_REQUEST, (server, player, handler, buf, responseSender) -> {
            try {
                UUID playerId = buf.readUuid();
                UUID townId = buf.readUuid();

                // Validate player
                if (!player.getUuid().equals(playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Player UUID mismatch in temporary state request");
                    return;
                }

                // Find the town
                Town town = TownManager.getInstance().getTown(townId);
                if (town == null) {
                    Pokecobbleclaim.LOGGER.warn("Cannot get temporary state - town " + townId + " not found");
                    return;
                }

                // Verify player is in the town
                if (!town.getPlayers().contains(playerId)) {
                    Pokecobbleclaim.LOGGER.warn("Player " + playerId + " is not in town " + townId + " - denying temporary state request");
                    return;
                }

                // Get temporary or saved state
                List<ClaimTag> currentState = getInstance().getTemporaryOrSavedState(townId);
                boolean hasActiveUsers = !getInstance().getActiveUsers(townId).isEmpty();
                boolean hasTemporaryState = getInstance().temporaryClaimTagState.containsKey(townId);

                // Send response with current state
                PacketByteBuf responseBuf = PacketByteBufs.create();
                responseBuf.writeUuid(townId);
                responseBuf.writeInt(currentState.size());

                for (ClaimTag tag : currentState) {
                    getInstance().writeClaimTagToBuffer(responseBuf, tag);
                }

                ServerPlayNetworking.send(player, ClaimTagNetworkConstants.CLAIM_TAG_TEMP_STATE_RESPONSE, responseBuf);

                Pokecobbleclaim.LOGGER.debug("Sent temporary/saved state with {} tags to player {} for town {} (hasActiveUsers: {}, hasTemporaryState: {})",
                    currentState.size(), playerId, townId, hasActiveUsers, hasTemporaryState);

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling temporary state request: " + e.getMessage(), e);
            }
        });
    }

    /**
     * Broadcasts complete temporary state to all active users except the sender.
     */
    private void broadcastTemporaryStateToActiveUsers(MinecraftServer server, UUID townId, UUID senderId, List<ClaimTag> tempState) {
        try {
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) {
                return;
            }

            Set<UUID> activeUsers = getActiveUsers(townId);
            if (activeUsers.size() <= 1) {
                return; // No other users to broadcast to
            }

            // Send to all active users except the sender
            for (UUID userId : activeUsers) {
                if (!userId.equals(senderId)) {
                    ServerPlayerEntity player = server.getPlayerManager().getPlayer(userId);
                    if (player != null) {
                        // Create packet with complete temporary state
                        PacketByteBuf playerBuf = PacketByteBufs.create();
                        playerBuf.writeUuid(townId);
                        playerBuf.writeInt(tempState.size());

                        for (ClaimTag tag : tempState) {
                            writeClaimTagToBuffer(playerBuf, tag);
                        }

                        ServerPlayNetworking.send(player, ClaimTagNetworkConstants.CLAIM_TAG_TEMP_STATE_RESPONSE, playerBuf);
                    }
                }
            }

            Pokecobbleclaim.LOGGER.debug("Broadcasted temporary state with {} tags to {} users for town {}", tempState.size(), activeUsers.size() - 1, townId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error broadcasting temporary state: " + e.getMessage(), e);
        }
    }

    /**
     * Broadcasts a real-time change to all active users in a town except the sender.
     */
    private void broadcastRealtimeChange(MinecraftServer server, UUID townId, UUID senderId, String changeType, List<String> additionalData) {
        try {
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) {
                return;
            }

            Set<UUID> activeUsers = getActiveUsers(townId);
            if (activeUsers.size() <= 1) {
                return; // No other users to broadcast to
            }

            // Create broadcast packet
            PacketByteBuf broadcastBuf = PacketByteBufs.create();
            broadcastBuf.writeUuid(townId);
            broadcastBuf.writeUuid(senderId);
            broadcastBuf.writeString(changeType);

            // Write additional data
            for (String data : additionalData) {
                broadcastBuf.writeString(data);
            }

            // Send to all active users except the sender
            for (UUID userId : activeUsers) {
                if (!userId.equals(senderId)) {
                    ServerPlayerEntity player = server.getPlayerManager().getPlayer(userId);
                    if (player != null) {
                        // Create a copy of the buffer for each player
                        PacketByteBuf playerBuf = PacketByteBufs.create();
                        playerBuf.writeUuid(townId);
                        playerBuf.writeUuid(senderId);
                        playerBuf.writeString(changeType);
                        for (String data : additionalData) {
                            playerBuf.writeString(data);
                        }
                        ServerPlayNetworking.send(player, ClaimTagNetworkConstants.CLAIM_TAG_REALTIME_BROADCAST, playerBuf);
                    }
                }
            }

            Pokecobbleclaim.LOGGER.debug("Broadcasted real-time change {} to {} users for town {}", changeType, activeUsers.size() - 1, townId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error broadcasting real-time change: " + e.getMessage(), e);
        }
    }

    /**
     * Reads a ClaimTag from a packet buffer.
     * Enhanced with buffer underrun protection for backward compatibility.
     */
    private ClaimTag readClaimTagFromBuffer(PacketByteBuf buf) {
        try {
            int initialBytes = buf.readableBytes();
            Pokecobbleclaim.LOGGER.debug("SERVER: ClaimTagManager starting to read claim tag, buffer readable bytes: " + initialBytes);

            // Calculate expected minimum size
            // UUID (16) + 2 strings (variable) + int (4) + permissions (6 ranks * 8 + 8 non-member = 56 booleans)
            int expectedPermissionBytes = (com.pokecobble.town.TownPlayerRank.values().length * 8) + 8;
            Pokecobbleclaim.LOGGER.debug("SERVER: ClaimTagManager expected permission bytes: " + expectedPermissionBytes);

            if (buf.readableBytes() < 20) { // Minimum for UUID + int
                Pokecobbleclaim.LOGGER.error("SERVER: ClaimTagManager buffer too small to contain basic claim tag data. Available: " + buf.readableBytes() + " bytes");
                return null;
            }

            UUID id = buf.readUuid();
            Pokecobbleclaim.LOGGER.debug("SERVER: ClaimTagManager read tag UUID: " + id + ", remaining bytes: " + buf.readableBytes());

            String name = buf.readString(com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
            Pokecobbleclaim.LOGGER.debug("SERVER: ClaimTagManager read tag name: '" + name + "' (length: " + name.length() + "), remaining bytes: " + buf.readableBytes());

            String description = buf.readString(com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
            Pokecobbleclaim.LOGGER.debug("SERVER: ClaimTagManager read tag description: '" + description + "' (length: " + description.length() + "), remaining bytes: " + buf.readableBytes());

            int color = buf.readInt();
            Pokecobbleclaim.LOGGER.debug("SERVER: ClaimTagManager read tag color: " + color + ", remaining bytes: " + buf.readableBytes());

            // Check if we have enough bytes for permissions
            if (buf.readableBytes() < expectedPermissionBytes) {
                Pokecobbleclaim.LOGGER.error("SERVER: ClaimTagManager not enough bytes for permissions. Expected: " + expectedPermissionBytes + ", available: " + buf.readableBytes());
                Pokecobbleclaim.LOGGER.error("SERVER: ClaimTagManager this suggests a format mismatch - data may have been written in v2 efficient format but being read in standard format");
                // Try to create a tag with default permissions
                return new ClaimTag(id, name, description, color);
            }

            // Create tag with preserved UUID
            ClaimTag tag = new ClaimTag(id, name, description, color);

            // Read rank permissions
            int rankCount = com.pokecobble.town.TownPlayerRank.values().length;
            Pokecobbleclaim.LOGGER.debug("SERVER: ClaimTagManager about to read permissions for " + rankCount + " ranks, remaining bytes: " + buf.readableBytes());

            for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
                Pokecobbleclaim.LOGGER.debug("SERVER: ClaimTagManager reading permissions for rank " + rank + ", remaining bytes: " + buf.readableBytes());
                for (int i = 0; i < 8; i++) {
                    if (buf.readableBytes() < 1) {
                        Pokecobbleclaim.LOGGER.warn("SERVER: ClaimTagManager buffer underrun while reading rank permissions for tag: " + name + " at rank " + rank + " permission " + i + ". Using default permissions.");
                        return tag; // Return tag with default permissions
                    }
                    boolean permission = buf.readBoolean();
                    tag.getRankPermissions().setPermission(rank, i, permission);
                }
            }

            // Read non-member permissions (with backward compatibility check)
            Pokecobbleclaim.LOGGER.debug("SERVER: ClaimTagManager about to read non-member permissions, remaining bytes: " + buf.readableBytes());
            for (int i = 0; i < 8; i++) {
                if (buf.readableBytes() < 1) {
                    Pokecobbleclaim.LOGGER.warn("SERVER: ClaimTagManager buffer underrun while reading non-member permissions for tag: " + name + " at permission " + i + ". Using default permissions. Remaining bytes: " + buf.readableBytes());
                    return tag; // Return tag with default non-member permissions
                }
                boolean permission = buf.readBoolean();
                tag.getRankPermissions().setPermission(null, i, permission);
            }

            Pokecobbleclaim.LOGGER.debug("SERVER: ClaimTagManager successfully read claim tag: " + name + ", final remaining bytes: " + buf.readableBytes());
            return tag;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error reading claim tag from buffer: " + e.getMessage() + ", remaining bytes: " + buf.readableBytes(), e);
            // Instead of throwing, return null to prevent crashes
            return null;
        }
    }

    /**
     * Removes claim tag data for a town (when town is deleted).
     */
    public void removeTownClaimTagData(UUID townId) {
        if (townId == null) {
            return;
        }

        // Remove from cache
        claimTagCache.remove(townId);

        // Remove file
        fileLock.writeLock().lock();
        try {
            File tagsFile = getClaimTagsFile(townId);
            if (tagsFile.exists()) {
                tagsFile.delete();
                Pokecobbleclaim.LOGGER.debug("Removed claim tag data for town " + townId);
            }
        } finally {
            fileLock.writeLock().unlock();
        }
    }
}
