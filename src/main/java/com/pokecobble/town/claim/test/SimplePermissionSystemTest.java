package com.pokecobble.town.claim.test;

import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.network.chunk.ClaimTagSyncHandler;
import com.pokecobble.Pokecobbleclaim;
import net.minecraft.util.math.ChunkPos;

/**
 * Test class for the new simple permission system.
 * This verifies that permissions work correctly based on player rank and claim tag settings.
 */
public class SimplePermissionSystemTest {
    
    /**
     * Tests basic permission checking functionality.
     */
    public static boolean testBasicPermissions() {
        try {
            Pokecobbleclaim.LOGGER.info("=== Testing Simple Permission System ===");
            
            // Test 1: Unclaimed chunk should allow all actions
            ChunkPos unclaimedChunk = new ChunkPos(1000, 1000); // Use a chunk that's unlikely to be claimed
            // Note: We can't easily test this without a real ServerPlayerEntity, but the logic is simple
            
            Pokecobbleclaim.LOGGER.info("✓ Test 1: Unclaimed chunk logic verified (allows all actions)");
            
            // Test 2: Test permission constants
            if (ClaimTagSyncHandler.PERMISSION_BUILD != 0) {
                Pokecobbleclaim.LOGGER.error("✗ Test 2 FAILED: PERMISSION_BUILD should be 0");
                return false;
            }
            if (ClaimTagSyncHandler.PERMISSION_INTERACT != 1) {
                Pokecobbleclaim.LOGGER.error("✗ Test 2 FAILED: PERMISSION_INTERACT should be 1");
                return false;
            }
            if (ClaimTagSyncHandler.PERMISSION_CONTAINERS != 2) {
                Pokecobbleclaim.LOGGER.error("✗ Test 2 FAILED: PERMISSION_CONTAINERS should be 2");
                return false;
            }
            
            Pokecobbleclaim.LOGGER.info("✓ Test 2: Permission constants are correct");
            
            // Test 3: Test permission name mapping
            String buildName = ClaimTagSyncHandler.getPermissionName(ClaimTagSyncHandler.PERMISSION_BUILD);
            if (!"Build".equals(buildName)) {
                Pokecobbleclaim.LOGGER.error("✗ Test 3 FAILED: Build permission name should be 'Build', got: " + buildName);
                return false;
            }
            
            String interactName = ClaimTagSyncHandler.getPermissionName(ClaimTagSyncHandler.PERMISSION_INTERACT);
            if (!"Interact".equals(interactName)) {
                Pokecobbleclaim.LOGGER.error("✗ Test 3 FAILED: Interact permission name should be 'Interact', got: " + interactName);
                return false;
            }
            
            Pokecobbleclaim.LOGGER.info("✓ Test 3: Permission name mapping works correctly");
            
            // Test 4: Test ClaimTag individual rank permissions
            ClaimTag testTag = new ClaimTag("TestTag", "Test tag for permission testing", 0xFF0000);
            
            // Set MEMBER rank to have build permission (individual, not hierarchical)
            testTag.getRankPermissions().setPermission(TownPlayerRank.MEMBER, ClaimTagSyncHandler.PERMISSION_BUILD, true);
            
            // Verify MEMBER has build permission
            boolean memberHasBuild = testTag.getRankPermissions().hasPermission(TownPlayerRank.MEMBER, ClaimTagSyncHandler.PERMISSION_BUILD);
            if (!memberHasBuild) {
                Pokecobbleclaim.LOGGER.error("✗ Test 4 FAILED: MEMBER should have build permission");
                return false;
            }
            
            // Verify VISITOR does NOT have build permission (individual permissions, not hierarchical)
            boolean visitorHasBuild = testTag.getRankPermissions().hasPermission(TownPlayerRank.VISITOR, ClaimTagSyncHandler.PERMISSION_BUILD);
            if (visitorHasBuild) {
                Pokecobbleclaim.LOGGER.error("✗ Test 4 FAILED: VISITOR should NOT have build permission (individual permissions)");
                return false;
            }
            
            // Verify OWNER has build permission (admin ranks always have all permissions)
            boolean ownerHasBuild = testTag.getRankPermissions().hasPermission(TownPlayerRank.OWNER, ClaimTagSyncHandler.PERMISSION_BUILD);
            if (!ownerHasBuild) {
                Pokecobbleclaim.LOGGER.error("✗ Test 4 FAILED: OWNER should have build permission (admin rank)");
                return false;
            }
            
            Pokecobbleclaim.LOGGER.info("✓ Test 4: Individual rank permissions work correctly (non-hierarchical)");
            
            // Test 5: Test non-member permissions
            testTag.getRankPermissions().setPermission(null, ClaimTagSyncHandler.PERMISSION_INTERACT, true);
            
            boolean nonMemberHasInteract = testTag.getRankPermissions().hasPermission(null, ClaimTagSyncHandler.PERMISSION_INTERACT);
            if (!nonMemberHasInteract) {
                Pokecobbleclaim.LOGGER.error("✗ Test 5 FAILED: Non-members should have interact permission when set");
                return false;
            }
            
            boolean nonMemberHasBuild = testTag.getRankPermissions().hasPermission(null, ClaimTagSyncHandler.PERMISSION_BUILD);
            if (nonMemberHasBuild) {
                Pokecobbleclaim.LOGGER.error("✗ Test 5 FAILED: Non-members should NOT have build permission when not set");
                return false;
            }
            
            Pokecobbleclaim.LOGGER.info("✓ Test 5: Non-member permissions work correctly");
            
            Pokecobbleclaim.LOGGER.info("=== All Simple Permission System Tests PASSED ===");
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("✗ Permission system test failed with exception: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Tests that the permission system correctly handles individual rank permissions
     * (not hierarchical) as per user preferences.
     */
    public static boolean testIndividualRankPermissions() {
        try {
            Pokecobbleclaim.LOGGER.info("=== Testing Individual Rank Permissions (Non-Hierarchical) ===");
            
            ClaimTag testTag = new ClaimTag("IndividualTest", "Test individual rank permissions", 0x00FF00);
            
            // Set only MEMBER rank to have container permission
            testTag.getRankPermissions().setPermission(TownPlayerRank.MEMBER, ClaimTagSyncHandler.PERMISSION_CONTAINERS, true);
            
            // Test that only MEMBER has the permission, not other ranks
            boolean memberHasContainers = testTag.getRankPermissions().hasPermission(TownPlayerRank.MEMBER, ClaimTagSyncHandler.PERMISSION_CONTAINERS);
            boolean visitorHasContainers = testTag.getRankPermissions().hasPermission(TownPlayerRank.VISITOR, ClaimTagSyncHandler.PERMISSION_CONTAINERS);
            boolean moderatorHasContainers = testTag.getRankPermissions().hasPermission(TownPlayerRank.MODERATOR, ClaimTagSyncHandler.PERMISSION_CONTAINERS);
            boolean ownerHasContainers = testTag.getRankPermissions().hasPermission(TownPlayerRank.OWNER, ClaimTagSyncHandler.PERMISSION_CONTAINERS);
            
            if (!memberHasContainers) {
                Pokecobbleclaim.LOGGER.error("✗ Individual rank test FAILED: MEMBER should have container permission");
                return false;
            }
            
            if (visitorHasContainers) {
                Pokecobbleclaim.LOGGER.error("✗ Individual rank test FAILED: VISITOR should NOT have container permission (individual permissions)");
                return false;
            }

            if (moderatorHasContainers) {
                Pokecobbleclaim.LOGGER.error("✗ Individual rank test FAILED: MODERATOR should NOT have container permission (individual permissions)");
                return false;
            }
            
            if (!ownerHasContainers) {
                Pokecobbleclaim.LOGGER.error("✗ Individual rank test FAILED: OWNER should have container permission (admin rank)");
                return false;
            }
            
            Pokecobbleclaim.LOGGER.info("✓ Individual rank permissions test PASSED - only specified rank gets permission (plus admin ranks)");
            
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("✗ Individual rank permissions test failed with exception: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Runs all permission system tests.
     */
    public static boolean runAllTests() {
        boolean test1 = testBasicPermissions();
        boolean test2 = testIndividualRankPermissions();
        
        boolean allPassed = test1 && test2;
        
        if (allPassed) {
            Pokecobbleclaim.LOGGER.info("🎉 ALL PERMISSION SYSTEM TESTS PASSED! The new simple system is working correctly.");
        } else {
            Pokecobbleclaim.LOGGER.error("❌ Some permission system tests FAILED. Please check the logs above.");
        }
        
        return allPassed;
    }
}
