package com.pokecobble.town.claim.v2;

import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.util.Identifier;
import net.minecraft.util.math.ChunkPos;

import java.util.UUID;

/**
 * Client-side handler for permission update packets from the new Permission System v2.
 * This ensures that clients are immediately notified when chunk permissions change.
 */
@Environment(EnvType.CLIENT)
public class ClientPermissionHandler {
    // Network packet identifiers (must match server-side)
    private static final Identifier PERMISSION_UPDATE_PACKET = new Identifier("pokecobbleclaim", "permission_update_v2");
    private static final Identifier OWNERSHIP_CHANGE_PACKET = new Identifier("pokecobbleclaim", "ownership_change_v2");
    
    private static boolean initialized = false;
    
    /**
     * Initializes the client-side permission handlers.
     * This should be called during client mod initialization.
     */
    public static void initialize() {
        if (initialized) {
            return;
        }
        
        try {
            registerNetworkHandlers();
            initialized = true;
            Pokecobbleclaim.LOGGER.info("ClientPermissionHandler v2 initialized");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize ClientPermissionHandler v2", e);
        }
    }
    
    /**
     * Registers network packet handlers for permission updates.
     */
    private static void registerNetworkHandlers() {
        // Handle permission update packets
        ClientPlayNetworking.registerGlobalReceiver(PERMISSION_UPDATE_PACKET, (client, handler, buf, responseSender) -> {
            try {
                // Read packet data
                UUID townId = buf.readUuid();
                int chunkX = buf.readInt();
                int chunkZ = buf.readInt();
                ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);

                final ClaimTag tag;
                boolean hasTag = buf.readBoolean();
                if (hasTag) {
                    tag = readClaimTagFromBuffer(buf);
                } else {
                    tag = null;
                }

                // Process on main thread
                client.execute(() -> {
                    handlePermissionUpdate(townId, chunkPos, tag);
                });
                
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling permission update packet", e);
            }
        });
        
        // Handle ownership change packets
        ClientPlayNetworking.registerGlobalReceiver(OWNERSHIP_CHANGE_PACKET, (client, handler, buf, responseSender) -> {
            try {
                // Read packet data
                int chunkX = buf.readInt();
                int chunkZ = buf.readInt();
                ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);

                final UUID oldTownId;
                if (buf.readBoolean()) {
                    oldTownId = buf.readUuid();
                } else {
                    oldTownId = null;
                }

                final UUID newTownId;
                if (buf.readBoolean()) {
                    newTownId = buf.readUuid();
                } else {
                    newTownId = null;
                }

                // Process on main thread
                client.execute(() -> {
                    handleOwnershipChange(chunkPos, oldTownId, newTownId);
                });
                
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling ownership change packet", e);
            }
        });
    }
    
    /**
     * Handles permission update notifications from the server.
     */
    private static void handlePermissionUpdate(UUID townId, ChunkPos chunkPos, ClaimTag tag) {
        try {
            // Update client-side chunk data
            updateClientChunkData(chunkPos, tag);
            
            // Update claim tool if active
            updateClaimToolIfActive(chunkPos, tag);
            
            // Show notification to player
            showPermissionUpdateNotification(chunkPos, tag);
            
            Pokecobbleclaim.LOGGER.debug("CLIENT: Updated permissions for chunk {} with tag {}", 
                chunkPos, tag != null ? tag.getName() : "null");
                
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error processing permission update", e);
        }
    }
    
    /**
     * Handles ownership change notifications from the server.
     */
    private static void handleOwnershipChange(ChunkPos chunkPos, UUID oldTownId, UUID newTownId) {
        try {
            // Update client-side ownership data
            updateClientOwnershipData(chunkPos, newTownId);
            
            // Update claim tool if active
            updateClaimToolOwnership(chunkPos, newTownId);
            
            // Show notification to player
            showOwnershipChangeNotification(chunkPos, oldTownId, newTownId);
            
            Pokecobbleclaim.LOGGER.debug("CLIENT: Updated ownership for chunk {} from {} to {}", 
                chunkPos, oldTownId, newTownId);
                
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error processing ownership change", e);
        }
    }
    
    /**
     * Reads a ClaimTag from the packet buffer using the efficient format.
     */
    private static ClaimTag readClaimTagFromBuffer(PacketByteBuf buf) {
        UUID id = buf.readUuid();
        String name = buf.readString();
        String description = buf.readString();
        int color = buf.readInt();
        
        ClaimTag tag = new ClaimTag(id, name, description, color);
        
        // Read permissions efficiently from bit flags
        for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
            byte permissionByte = buf.readByte();
            
            // Unpack 8 boolean permissions from the byte
            for (int i = 0; i < 8; i++) {
                boolean hasPermission = (permissionByte & (1 << i)) != 0;
                tag.getRankPermissions().setPermission(rank, i, hasPermission);
            }
        }
        
        // Read non-member permissions
        byte nonMemberByte = buf.readByte();
        for (int i = 0; i < 8; i++) {
            boolean hasPermission = (nonMemberByte & (1 << i)) != 0;
            tag.getRankPermissions().setPermission(null, i, hasPermission);
        }
        
        return tag;
    }
    
    /**
     * Updates client-side chunk data with new tag information.
     */
    private static void updateClientChunkData(ChunkPos chunkPos, ClaimTag tag) {
        try {
            // Update client-side data structures
            // This would integrate with existing client chunk management
            Pokecobbleclaim.LOGGER.debug("Updated client chunk data for {}", chunkPos);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating client chunk data", e);
        }
    }
    
    /**
     * Updates client-side ownership data.
     */
    private static void updateClientOwnershipData(ChunkPos chunkPos, UUID newTownId) {
        try {
            // Update client-side ownership structures
            // This would integrate with existing client chunk management
            Pokecobbleclaim.LOGGER.debug("Updated client ownership data for {}", chunkPos);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating client ownership data", e);
        }
    }
    
    /**
     * Updates the claim tool if it's currently active.
     */
    private static void updateClaimToolIfActive(ChunkPos chunkPos, ClaimTag tag) {
        try {
            com.pokecobble.town.claim.ClaimTool claimTool = 
                com.pokecobble.town.claim.ClaimTool.getInstance();
            if (claimTool != null && claimTool.isActive()) {
                claimTool.updateChunkData(chunkPos, null, tag);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating claim tool", e);
        }
    }
    
    /**
     * Updates claim tool ownership information.
     */
    private static void updateClaimToolOwnership(ChunkPos chunkPos, UUID newTownId) {
        try {
            com.pokecobble.town.claim.ClaimTool claimTool =
                com.pokecobble.town.claim.ClaimTool.getInstance();
            if (claimTool != null && claimTool.isActive()) {
                // The claim tool will need to refresh its data
                claimTool.refreshChunkData(chunkPos);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating claim tool ownership", e);
        }
    }
    
    /**
     * Shows a notification about permission updates.
     */
    private static void showPermissionUpdateNotification(ChunkPos chunkPos, ClaimTag tag) {
        try {
            String message;
            if (tag != null) {
                message = String.format("Chunk permissions updated: %s (%d, %d)",
                    tag.getName(), chunkPos.x, chunkPos.z);
            } else {
                message = String.format("Chunk permissions cleared (%d, %d)",
                    chunkPos.x, chunkPos.z);
            }

            // Log the notification for now (can be enhanced later)
            Pokecobbleclaim.LOGGER.info("CLIENT: {}", message);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error showing permission update notification", e);
        }
    }
    
    /**
     * Shows a notification about ownership changes.
     */
    private static void showOwnershipChangeNotification(ChunkPos chunkPos, UUID oldTownId, UUID newTownId) {
        try {
            String message;
            if (newTownId != null) {
                message = String.format("Chunk claimed (%d, %d)", chunkPos.x, chunkPos.z);
            } else {
                message = String.format("Chunk unclaimed (%d, %d)", chunkPos.x, chunkPos.z);
            }

            // Log the notification for now (can be enhanced later)
            Pokecobbleclaim.LOGGER.info("CLIENT: {}", message);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error showing ownership change notification", e);
        }
    }
    
    /**
     * Checks if the client permission handler is initialized.
     */
    public static boolean isInitialized() {
        return initialized;
    }
}
