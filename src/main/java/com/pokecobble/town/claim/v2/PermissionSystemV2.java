package com.pokecobble.town.claim.v2;

import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.api.ModInitializer;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Main initialization and management class for the new Permission System v2.
 * 
 * This class:
 * - Initializes all components of the new permission system
 * - Provides a clean API for other parts of the mod to use
 * - Manages the lifecycle of the permission system
 * - Handles integration with the existing systems
 */
public class PermissionSystemV2 {
    private static PermissionSystemV2 instance;
    private static boolean initialized = false;
    
    // Core components
    private ChunkPermissionEngine permissionEngine;
    private PermissionEventHandler eventHandler;
    private ClaimTagIntegration claimTagIntegration;
    
    // Background maintenance
    private ScheduledExecutorService maintenanceExecutor;
    
    private PermissionSystemV2() {
        // Private constructor for singleton
    }
    
    public static PermissionSystemV2 getInstance() {
        if (instance == null) {
            instance = new PermissionSystemV2();
        }
        return instance;
    }
    
    /**
     * Initializes the new permission system.
     * This should be called during mod initialization.
     */
    public static void initialize() {
        if (initialized) {
            Pokecobbleclaim.LOGGER.warn("Permission System v2 already initialized");
            return;
        }
        
        try {
            Pokecobbleclaim.LOGGER.info("Initializing Permission System v2...");
            
            PermissionSystemV2 system = getInstance();
            system.initializeComponents();
            system.startMaintenanceTasks();

            // Verify that the permission system fixes are working correctly
            boolean verificationPassed = com.pokecobble.town.claim.PermissionSystemVerifier.verifyPermissionSystemFixes();
            if (!verificationPassed) {
                throw new RuntimeException("Permission system verification failed - fixes are not working correctly");
            }

            initialized = true;
            Pokecobbleclaim.LOGGER.info("Permission System v2 initialized successfully with verified fixes");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize Permission System v2", e);
            throw new RuntimeException("Permission System v2 initialization failed", e);
        }
    }
    
    /**
     * Initializes all core components.
     */
    private void initializeComponents() {
        // Initialize core permission engine
        this.permissionEngine = ChunkPermissionEngine.getInstance();
        
        // Initialize event handler for intercepting player actions
        this.eventHandler = PermissionEventHandler.getInstance();
        
        // Initialize integration layer
        this.claimTagIntegration = ClaimTagIntegration.getInstance();
        
        Pokecobbleclaim.LOGGER.info("All Permission System v2 components initialized");
    }
    
    /**
     * Starts background maintenance tasks.
     */
    private void startMaintenanceTasks() {
        this.maintenanceExecutor = Executors.newScheduledThreadPool(1, r -> {
            Thread t = new Thread(r, "PermissionSystemMaintenance");
            t.setDaemon(true);
            return t;
        });
        
        // Schedule cache cleanup every 5 minutes
        maintenanceExecutor.scheduleAtFixedRate(
            this::performMaintenance,
            5, 5, TimeUnit.MINUTES
        );
        
        Pokecobbleclaim.LOGGER.info("Permission System v2 maintenance tasks started");
    }
    
    /**
     * Performs periodic maintenance tasks.
     */
    private void performMaintenance() {
        try {
            // Clean up expired cache entries
            if (permissionEngine != null) {
                // The cache cleanup is handled internally by the ChunkPermissionCache
                Pokecobbleclaim.LOGGER.debug("Permission system maintenance completed");
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during permission system maintenance", e);
        }
    }
    
    /**
     * Shuts down the permission system gracefully.
     */
    public static void shutdown() {
        if (!initialized) {
            return;
        }
        
        try {
            PermissionSystemV2 system = getInstance();
            
            if (system.maintenanceExecutor != null) {
                system.maintenanceExecutor.shutdown();
                if (!system.maintenanceExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    system.maintenanceExecutor.shutdownNow();
                }
            }
            
            initialized = false;
            Pokecobbleclaim.LOGGER.info("Permission System v2 shut down successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during Permission System v2 shutdown", e);
        }
    }
    
    /**
     * Checks if the permission system is initialized and ready.
     */
    public static boolean isInitialized() {
        return initialized;
    }
    
    /**
     * Gets the permission engine for direct access.
     * Use this for custom permission checks.
     */
    public ChunkPermissionEngine getPermissionEngine() {
        return permissionEngine;
    }
    
    /**
     * Gets the claim tag integration for triggering updates.
     */
    public ClaimTagIntegration getClaimTagIntegration() {
        return claimTagIntegration;
    }
    
    /**
     * Gets comprehensive system statistics.
     */
    public String getSystemStats() {
        if (!initialized) {
            return "Permission System v2 not initialized";
        }
        
        StringBuilder stats = new StringBuilder();
        stats.append("=== Permission System v2 Status ===\n");
        stats.append("Initialized: ").append(initialized).append("\n");
        
        if (permissionEngine != null) {
            stats.append(permissionEngine.getCacheStats()).append("\n");
        }
        
        if (claimTagIntegration != null) {
            stats.append(claimTagIntegration.getPerformanceStats());
        }
        
        return stats.toString();
    }
    
    /**
     * Performs a health check of the entire system.
     */
    public boolean performHealthCheck() {
        if (!initialized) {
            Pokecobbleclaim.LOGGER.error("Health check failed: System not initialized");
            return false;
        }
        
        try {
            // Check all components
            if (permissionEngine == null) {
                Pokecobbleclaim.LOGGER.error("Health check failed: Permission engine is null");
                return false;
            }
            
            if (eventHandler == null) {
                Pokecobbleclaim.LOGGER.error("Health check failed: Event handler is null");
                return false;
            }
            
            if (claimTagIntegration == null) {
                Pokecobbleclaim.LOGGER.error("Health check failed: Claim tag integration is null");
                return false;
            }
            
            // Validate integration
            if (!claimTagIntegration.validatePermissionSystem()) {
                Pokecobbleclaim.LOGGER.error("Health check failed: Integration validation failed");
                return false;
            }
            
            Pokecobbleclaim.LOGGER.info("Permission System v2 health check passed");
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Health check failed with exception", e);
            return false;
        }
    }
    
    /**
     * Reloads the permission system configuration.
     * This can be used for hot-reloading changes without restarting.
     */
    public void reload() {
        try {
            Pokecobbleclaim.LOGGER.info("Reloading Permission System v2...");
            
            // Clear all caches
            if (permissionEngine != null) {
                permissionEngine.clearCache();
            }
            
            // Perform health check after reload
            if (performHealthCheck()) {
                Pokecobbleclaim.LOGGER.info("Permission System v2 reloaded successfully");
            } else {
                Pokecobbleclaim.LOGGER.error("Permission System v2 reload completed with issues");
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during Permission System v2 reload", e);
        }
    }
    
    /**
     * Enables or disables debug mode for detailed logging.
     */
    public void setDebugMode(boolean enabled) {
        // This could be used to enable more detailed logging
        Pokecobbleclaim.LOGGER.info("Permission System v2 debug mode: {}", enabled ? "enabled" : "disabled");
    }

    /**
     * Called after server startup to load chunk ownership data.
     * This must be called after TownChunkDataManager has loaded all town data.
     */
    public static void onServerDataLoaded() {
        if (!initialized) {
            Pokecobbleclaim.LOGGER.warn("Permission System v2 not initialized when onServerDataLoaded called");
            return;
        }

        try {
            // Now that server data is loaded, ensure chunk ownership data is available
            com.pokecobble.town.chunk.TownChunkDataManager chunkDataManager =
                com.pokecobble.town.chunk.TownChunkDataManager.getInstance();

            // Force a refresh to ensure the chunk ownership mapping is populated
            chunkDataManager.loadAllTownData();

            Pokecobbleclaim.LOGGER.info("Permission System v2: Loaded chunk ownership data after server startup");

            // Clear any cached permissions since we now have proper data
            getInstance().getPermissionEngine().clearCache();
            Pokecobbleclaim.LOGGER.info("Permission System v2: Cleared permission cache after data loading");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load chunk ownership data in Permission System v2", e);
        }
    }
}
