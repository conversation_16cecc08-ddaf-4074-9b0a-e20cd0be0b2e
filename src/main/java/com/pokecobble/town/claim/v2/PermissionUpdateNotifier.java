package com.pokecobble.town.claim.v2;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;
import net.minecraft.util.math.ChunkPos;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Handles real-time notification of permission changes to clients.
 * Ensures that when claim tag settings are updated, all relevant players
 * are immediately notified so their clients can update accordingly.
 */
public class PermissionUpdateNotifier {
    // Network packet identifiers
    private static final Identifier PERMISSION_UPDATE_PACKET = new Identifier("pokecobbleclaim", "permission_update_v2");
    private static final Identifier OWNERSHIP_CHANGE_PACKET = new Identifier("pokecobbleclaim", "ownership_change_v2");
    
    // Async executor for non-blocking notifications
    private final Executor notificationExecutor = Executors.newFixedThreadPool(2, r -> {
        Thread t = new Thread(r, "PermissionNotifier");
        t.setDaemon(true);
        return t;
    });
    
    /**
     * Notifies all relevant players when chunk permissions are updated.
     * This includes town members and any players currently in the affected chunks.
     */
    public void notifyPermissionUpdate(UUID townId, ChunkPos chunkPos, ClaimTag newTag) {
        CompletableFuture.runAsync(() -> {
            try {
                Town town = TownManager.getInstance().getTown(townId);
                if (town == null) {
                    return;
                }
                
                // Create the update packet
                PacketByteBuf buf = PacketByteBufs.create();
                writePermissionUpdatePacket(buf, townId, chunkPos, newTag);
                
                // Send to all town members
                List<ServerPlayerEntity> townMembers = getTownMembersOnline(town);
                for (ServerPlayerEntity player : townMembers) {
                    NetworkManager.sendToPlayer(player, PERMISSION_UPDATE_PACKET, buf);
                }
                
                // Send to any non-members currently in the chunk
                List<ServerPlayerEntity> playersInChunk = getPlayersInChunk(chunkPos);
                for (ServerPlayerEntity player : playersInChunk) {
                    if (!town.getPlayers().contains(player.getUuid())) {
                        NetworkManager.sendToPlayer(player, PERMISSION_UPDATE_PACKET, buf);
                    }
                }
                
                Pokecobbleclaim.LOGGER.debug("Sent permission update for chunk {} to {} players", 
                    chunkPos, townMembers.size() + playersInChunk.size());
                    
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error sending permission update notification", e);
            }
        }, notificationExecutor);
    }
    
    /**
     * Notifies players when chunk ownership changes.
     */
    public void notifyOwnershipChange(ChunkPos chunkPos, UUID oldTownId, UUID newTownId) {
        CompletableFuture.runAsync(() -> {
            try {
                // Create the ownership change packet
                PacketByteBuf buf = PacketByteBufs.create();
                writeOwnershipChangePacket(buf, chunkPos, oldTownId, newTownId);
                
                // Send to players in the affected chunk
                List<ServerPlayerEntity> playersInChunk = getPlayersInChunk(chunkPos);
                for (ServerPlayerEntity player : playersInChunk) {
                    NetworkManager.sendToPlayer(player, OWNERSHIP_CHANGE_PACKET, buf);
                }
                
                // Send to members of both old and new towns
                if (oldTownId != null) {
                    Town oldTown = TownManager.getInstance().getTown(oldTownId);
                    if (oldTown != null) {
                        List<ServerPlayerEntity> oldTownMembers = getTownMembersOnline(oldTown);
                        for (ServerPlayerEntity player : oldTownMembers) {
                            NetworkManager.sendToPlayer(player, OWNERSHIP_CHANGE_PACKET, buf);
                        }
                    }
                }
                
                if (newTownId != null) {
                    Town newTown = TownManager.getInstance().getTown(newTownId);
                    if (newTown != null) {
                        List<ServerPlayerEntity> newTownMembers = getTownMembersOnline(newTown);
                        for (ServerPlayerEntity player : newTownMembers) {
                            NetworkManager.sendToPlayer(player, OWNERSHIP_CHANGE_PACKET, buf);
                        }
                    }
                }
                
                Pokecobbleclaim.LOGGER.debug("Sent ownership change notification for chunk {}", chunkPos);
                
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error sending ownership change notification", e);
            }
        }, notificationExecutor);
    }
    
    /**
     * Writes a permission update packet to the buffer.
     */
    private void writePermissionUpdatePacket(PacketByteBuf buf, UUID townId, ChunkPos chunkPos, ClaimTag tag) {
        buf.writeUuid(townId);
        buf.writeInt(chunkPos.x);
        buf.writeInt(chunkPos.z);
        
        // Write tag data
        if (tag != null) {
            buf.writeBoolean(true);
            buf.writeUuid(tag.getId());
            buf.writeString(tag.getName());
            buf.writeString(tag.getDescription());
            buf.writeInt(tag.getColor());
            
            // Write permissions efficiently
            writePermissionsToBuffer(buf, tag);
        } else {
            buf.writeBoolean(false); // No tag
        }
    }
    
    /**
     * Writes ownership change packet to the buffer.
     */
    private void writeOwnershipChangePacket(PacketByteBuf buf, ChunkPos chunkPos, UUID oldTownId, UUID newTownId) {
        buf.writeInt(chunkPos.x);
        buf.writeInt(chunkPos.z);
        
        // Write old town ID
        if (oldTownId != null) {
            buf.writeBoolean(true);
            buf.writeUuid(oldTownId);
        } else {
            buf.writeBoolean(false);
        }
        
        // Write new town ID
        if (newTownId != null) {
            buf.writeBoolean(true);
            buf.writeUuid(newTownId);
        } else {
            buf.writeBoolean(false);
        }
    }
    
    /**
     * Efficiently writes permission data to the buffer.
     * Uses a compact format to minimize network traffic.
     */
    private void writePermissionsToBuffer(PacketByteBuf buf, ClaimTag tag) {
        // Write rank permissions as bit flags for efficiency
        for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
            boolean[] permissions = tag.getRankPermissions().getPermissions(rank);
            
            // Pack 8 boolean permissions into a single byte
            byte permissionByte = 0;
            for (int i = 0; i < Math.min(8, permissions.length); i++) {
                if (permissions[i]) {
                    permissionByte |= (1 << i);
                }
            }
            buf.writeByte(permissionByte);
        }
        
        // Write non-member permissions
        boolean[] nonMemberPermissions = tag.getRankPermissions().getPermissions(null);
        byte nonMemberByte = 0;
        for (int i = 0; i < Math.min(8, nonMemberPermissions.length); i++) {
            if (nonMemberPermissions[i]) {
                nonMemberByte |= (1 << i);
            }
        }
        buf.writeByte(nonMemberByte);
    }
    
    /**
     * Gets all online members of a town.
     */
    private List<ServerPlayerEntity> getTownMembersOnline(Town town) {
        return town.getPlayers().stream()
            .map(playerId -> {
                try {
                    return com.pokecobble.Pokecobbleclaim.getServer().getPlayerManager().getPlayer(playerId);
                } catch (Exception e) {
                    return null;
                }
            })
            .filter(player -> player != null)
            .map(player -> (ServerPlayerEntity) player)
            .toList();
    }
    
    /**
     * Gets all players currently in a specific chunk.
     */
    private List<ServerPlayerEntity> getPlayersInChunk(ChunkPos chunkPos) {
        try {
            return com.pokecobble.Pokecobbleclaim.getServer().getPlayerManager().getPlayerList().stream()
                .filter(player -> {
                    ChunkPos playerChunk = new ChunkPos(player.getBlockPos());
                    return playerChunk.equals(chunkPos);
                })
                .toList();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error getting players in chunk", e);
            return List.of();
        }
    }
}
