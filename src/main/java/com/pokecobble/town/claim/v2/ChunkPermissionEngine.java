package com.pokecobble.town.claim.v2;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.Pokecobbleclaim;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.world.World;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Core permission engine for the new claim system.
 * This is the single source of truth for all chunk-based permissions.
 * 
 * Features:
 * - Fast permission checking with intelligent caching
 * - Real-time updates when claim tag settings change
 * - Consistent permission logic between client and server
 * - Efficient chunk-to-permission mapping
 */
public class ChunkPermissionEngine {
    private static ChunkPermissionEngine instance;
    
    // Permission types - these map directly to the action indices in ClaimTag
    public static final int PERMISSION_BUILD = 0;      // Building, breaking blocks
    public static final int PERMISSION_INTERACT = 1;   // Doors, buttons, levers
    public static final int PERMISSION_CONTAINERS = 2; // Chests, furnaces, etc.
    public static final int PERMISSION_REDSTONE = 3;   // Redstone components
    public static final int PERMISSION_DOORS = 4;      // Door access
    public static final int PERMISSION_CROPS = 5;      // Farming
    public static final int PERMISSION_ANIMALS = 6;    // Animal interaction
    public static final int PERMISSION_VILLAGERS = 7;  // Villager trading
    
    // Core data structures
    private final ChunkPermissionCache permissionCache;
    private final ChunkOwnershipManager ownershipManager;
    private final PermissionUpdateNotifier updateNotifier;
    
    private ChunkPermissionEngine() {
        this.permissionCache = new ChunkPermissionCache();
        this.ownershipManager = new ChunkOwnershipManager();
        this.updateNotifier = new PermissionUpdateNotifier();
        
        Pokecobbleclaim.LOGGER.info("ChunkPermissionEngine v2 initialized");
    }
    
    public static ChunkPermissionEngine getInstance() {
        if (instance == null) {
            instance = new ChunkPermissionEngine();
        }
        return instance;
    }
    
    /**
     * Main permission check method - this is called by all event handlers.
     * 
     * @param player The player attempting the action
     * @param world The world where the action is taking place
     * @param pos The block position
     * @param permissionType The type of permission required (use PERMISSION_* constants)
     * @return true if the action is allowed, false otherwise
     */
    public boolean hasPermission(ServerPlayerEntity player, World world, BlockPos pos, int permissionType) {
        if (player == null || world == null || pos == null) {
            return false;
        }
        
        ChunkPos chunkPos = new ChunkPos(pos);
        
        // Check cache first for performance
        String cacheKey = generateCacheKey(player.getUuid(), chunkPos, permissionType);
        Boolean cachedResult = permissionCache.getCachedPermission(cacheKey);
        if (cachedResult != null) {
            Pokecobbleclaim.LOGGER.debug("PERMISSION CACHE HIT: Player '{}' at {} for {} = {}",
                player.getName().getString(), chunkPos, getPermissionName(permissionType), cachedResult);
            return cachedResult;
        }
        
        // Get chunk ownership
        UUID owningTownId = ownershipManager.getChunkOwner(chunkPos);

        Pokecobbleclaim.LOGGER.info("CHUNK OWNERSHIP: Chunk {} is owned by town {}", chunkPos, owningTownId);

        boolean hasPermission;
        if (owningTownId == null) {
            // Unclaimed chunk - allow all actions
            Pokecobbleclaim.LOGGER.debug("Unclaimed chunk {} - allowing all actions", chunkPos);
            hasPermission = true;
        } else {
            // Claimed chunk - check permissions
            Pokecobbleclaim.LOGGER.info("Claimed chunk {} - checking permissions for player '{}'", chunkPos, player.getName().getString());
            hasPermission = checkClaimedChunkPermission(player, owningTownId, chunkPos, permissionType);
        }
        
        // Cache the result
        permissionCache.cachePermission(cacheKey, hasPermission);
        
        // Log for debugging - always log permission checks for troubleshooting
        Pokecobbleclaim.LOGGER.info("PERMISSION RESULT: Player '{}' action '{}' at chunk {} = {} (FRESH CHECK)",
            player.getName().getString(), getPermissionName(permissionType), chunkPos, hasPermission ? "ALLOWED" : "DENIED");
        
        return hasPermission;
    }
    
    /**
     * Checks permission for a player in a claimed chunk.
     */
    private boolean checkClaimedChunkPermission(ServerPlayerEntity player, UUID townId, ChunkPos chunkPos, int permissionType) {
        Town town = TownManager.getInstance().getTown(townId);
        if (town == null) {
            Pokecobbleclaim.LOGGER.error("TOWN LOOKUP FAILED: Town {} not found for chunk {}", townId, chunkPos);
            return false; // Town doesn't exist
        }

        // CRITICAL DEBUG: Check town membership detection
        boolean isTownMember = town.getPlayers().contains(player.getUuid());

        Pokecobbleclaim.LOGGER.info("TOWN MEMBERSHIP CHECK: Player '{}' (UUID: {}) in town '{}' (ID: {})",
            player.getName().getString(), player.getUuid(), town.getName(), townId);
        Pokecobbleclaim.LOGGER.info("  - Town has {} players: {}", town.getPlayers().size(), town.getPlayers());
        Pokecobbleclaim.LOGGER.info("  - Player UUID matches any in town: {}", isTownMember);

        if (isTownMember) {
            // Get and log the player's rank
            TownPlayerRank playerRank = town.getPlayerRank(player.getUuid());
            Pokecobbleclaim.LOGGER.info("  - Player rank in town: {}", playerRank);

            return checkTownMemberPermission(player, town, chunkPos, permissionType);
        } else {
            Pokecobbleclaim.LOGGER.info("  - Treating player as NON-MEMBER");
            return checkNonMemberPermission(town, chunkPos, permissionType);
        }
    }
    
    /**
     * Checks permission for a town member based on their rank and the chunk's claim tag.
     */
    private boolean checkTownMemberPermission(ServerPlayerEntity player, Town town, ChunkPos chunkPos, int permissionType) {
        TownPlayerRank playerRank = town.getPlayerRank(player.getUuid());

        if (playerRank == null) {
            Pokecobbleclaim.LOGGER.warn("Player '{}' has no rank in town '{}' but is in player list",
                player.getName().getString(), town.getName());
            return false; // No rank in town
        }

        // Admin ranks always have all permissions
        if (playerRank.hasAdminPermissions()) {
            Pokecobbleclaim.LOGGER.debug("Player '{}' has admin rank '{}' - allowing all permissions",
                player.getName().getString(), playerRank);
            return true;
        }

        // Get the claim tag for this chunk
        ClaimTag chunkTag = ownershipManager.getChunkTag(chunkPos);

        Pokecobbleclaim.LOGGER.info("CHUNK TAG DEBUG: Retrieved tag for chunk {}: {}",
            chunkPos, chunkTag != null ? chunkTag.getName() + " (ID: " + chunkTag.getId() + ")" : "null");

        if (chunkTag == null) {
            // No tag set - use default behavior (allow all for town members)
            Pokecobbleclaim.LOGGER.debug("No tag set for chunk {} - allowing town member '{}' (rank: {})",
                chunkPos, player.getName().getString(), playerRank);
            return true;
        }

        // Check the specific permission for this rank
        Pokecobbleclaim.LOGGER.info("PERMISSION CHECK DEBUG: About to check permission for rank {} (display: '{}') on tag '{}' for action {} ({})",
            playerRank, playerRank.getDisplayName(), chunkTag.getName(), permissionType, getPermissionName(permissionType));

        boolean hasPermission = chunkTag.getRankPermissions().hasPermission(playerRank, permissionType);

        Pokecobbleclaim.LOGGER.info("PERMISSION CHECK DEBUG: Raw permission result = {}", hasPermission);

        // DEBUG: Log detailed information about the tag being checked
        Pokecobbleclaim.LOGGER.info("RANK PERMISSION CHECK: Player '{}' (rank: {}) for action '{}' on tag '{}' = {}",
            player.getName().getString(), playerRank, getPermissionName(permissionType), chunkTag.getName(), hasPermission);

        try {
            boolean[] allPermissions = chunkTag.getPermissionsForRank(playerRank);
            if (allPermissions != null) {
                Pokecobbleclaim.LOGGER.info("RANK PERMISSION DEBUG: Tag '{}' ID: {}, All {} permissions: Build={}, Interact={}, Containers={}, Redstone={}, Doors={}, Crops={}, Animals={}, Villagers={}",
                    chunkTag.getName(), chunkTag.getId(), playerRank.getDisplayName(),
                    allPermissions[0], allPermissions[1], allPermissions[2], allPermissions[3],
                    allPermissions[4], allPermissions[5], allPermissions[6], allPermissions[7]);
            } else {
                Pokecobbleclaim.LOGGER.error("RANK PERMISSION ERROR: getPermissionsForRank returned null for rank {} on tag '{}'",
                    playerRank, chunkTag.getName());
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("RANK PERMISSION ERROR: Exception getting permissions for rank {} on tag '{}': {}",
                playerRank, chunkTag.getName(), e.getMessage(), e);
        }

        return hasPermission;
    }
    
    /**
     * Checks permission for a non-member based on the chunk's claim tag.
     */
    private boolean checkNonMemberPermission(Town town, ChunkPos chunkPos, int permissionType) {
        ClaimTag chunkTag = ownershipManager.getChunkTag(chunkPos);
        
        if (chunkTag == null) {
            // No tag set - deny all actions for non-members
            return false;
        }
        
        // Check non-member permissions (rank = null)
        return chunkTag.getRankPermissions().hasPermission(null, permissionType);
    }
    
    /**
     * Called when claim tag settings are updated to invalidate cache and notify clients.
     */
    public void onClaimTagUpdated(UUID townId, ChunkPos chunkPos, ClaimTag newTag) {
        // Invalidate cache for this chunk
        permissionCache.invalidateChunk(chunkPos);
        
        // Notify all relevant players about the permission change
        updateNotifier.notifyPermissionUpdate(townId, chunkPos, newTag);
        
        Pokecobbleclaim.LOGGER.info("Updated permissions for chunk {} in town {}", chunkPos, townId);
    }
    
    /**
     * Called when a chunk's ownership changes.
     */
    public void onChunkOwnershipChanged(ChunkPos chunkPos, UUID oldTownId, UUID newTownId) {
        // Update ownership mapping
        ownershipManager.updateChunkOwnership(chunkPos, newTownId);

        // Invalidate cache for this chunk
        permissionCache.invalidateChunk(chunkPos);

        // Notify players
        updateNotifier.notifyOwnershipChange(chunkPos, oldTownId, newTownId);
    }

    /**
     * Called when a player's rank changes in a town.
     * This invalidates all cached permissions for the player to ensure
     * they get the correct permissions based on their new rank.
     */
    public void onPlayerRankChanged(UUID playerId, UUID townId, TownPlayerRank oldRank, TownPlayerRank newRank) {
        try {
            // Invalidate all cached permissions for this player
            permissionCache.invalidatePlayer(playerId.toString());

            Pokecobbleclaim.LOGGER.info("Invalidated permission cache for player {} due to rank change from {} to {} in town {}",
                playerId, oldRank, newRank, townId);

            // Note: We don't need to send specific notifications to clients here because
            // the permission system will automatically re-evaluate permissions when needed.
            // The cache invalidation ensures fresh permission checks will use the new rank.

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling player rank change for player {} in town {}: {}",
                playerId, townId, e.getMessage(), e);
        }
    }
    
    /**
     * Gets a human-readable name for a permission type.
     */
    public static String getPermissionName(int permissionType) {
        return switch (permissionType) {
            case PERMISSION_BUILD -> "Build";
            case PERMISSION_INTERACT -> "Interact";
            case PERMISSION_CONTAINERS -> "Containers";
            case PERMISSION_REDSTONE -> "Redstone";
            case PERMISSION_DOORS -> "Doors";
            case PERMISSION_CROPS -> "Crops";
            case PERMISSION_ANIMALS -> "Animals";
            case PERMISSION_VILLAGERS -> "Villagers";
            default -> "Unknown";
        };
    }
    
    /**
     * Generates a cache key for permission lookups.
     */
    private String generateCacheKey(UUID playerId, ChunkPos chunkPos, int permissionType) {
        return playerId + ":" + chunkPos.x + "," + chunkPos.z + ":" + permissionType;
    }
    
    /**
     * Clears all cached permissions (useful for debugging or major updates).
     */
    public void clearCache() {
        permissionCache.clearAll();
        Pokecobbleclaim.LOGGER.info("Cleared all permission cache");
    }

    /**
     * Clears cached permissions for a specific chunk.
     */
    public void clearCacheForChunk(ChunkPos chunkPos) {
        permissionCache.invalidateChunk(chunkPos);
        Pokecobbleclaim.LOGGER.debug("Cleared permission cache for chunk {}", chunkPos);
    }

    /**
     * Forces a permission refresh for a specific player in a specific chunk.
     * Useful for testing and debugging permission issues.
     */
    public void forceRefreshPlayerPermissions(UUID playerId, ChunkPos chunkPos) {
        // Clear all cached permissions for this player in this chunk
        permissionCache.invalidatePlayerChunk(playerId, chunkPos);
        Pokecobbleclaim.LOGGER.info("Forced permission refresh for player {} in chunk {}", playerId, chunkPos);
    }
    
    /**
     * Gets cache statistics for monitoring performance.
     */
    public String getCacheStats() {
        return permissionCache.getStats();
    }

    /**
     * Gets the chunk owner for debugging purposes.
     */
    public UUID getChunkOwner(ChunkPos chunkPos) {
        return ownershipManager.getChunkOwner(chunkPos);
    }

    /**
     * Gets the ownership manager for cache management.
     */
    public ChunkOwnershipManager getOwnershipManager() {
        return ownershipManager;
    }
}
