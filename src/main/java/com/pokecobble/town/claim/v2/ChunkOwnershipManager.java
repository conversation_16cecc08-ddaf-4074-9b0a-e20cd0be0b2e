package com.pokecobble.town.claim.v2;

import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.claim.TownClaimData;
import com.pokecobble.town.chunk.TownChunkDataManager;
import com.pokecobble.Pokecobbleclaim;
import net.minecraft.util.math.ChunkPos;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Manages chunk ownership and tag assignments.
 * Provides fast lookups and maintains consistency with the underlying data storage.
 */
public class ChunkOwnershipManager {
    // Fast lookup maps for performance
    private final ConcurrentHashMap<ChunkPos, UUID> chunkToTownMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<ChunkPos, ClaimTag> chunkToTagMap = new ConcurrentHashMap<>();
    
    // Lock for thread safety during bulk operations
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    
    // Reference to the underlying data manager
    private final TownChunkDataManager dataManager;
    
    public ChunkOwnershipManager() {
        this.dataManager = TownChunkDataManager.getInstance();
        initializeFromDataManager();
    }
    
    /**
     * Initializes the fast lookup maps from the existing data manager.
     */
    private void initializeFromDataManager() {
        lock.writeLock().lock();
        try {
            // This will be populated as chunks are accessed
            // We don't preload everything to avoid memory issues with large worlds
            Pokecobbleclaim.LOGGER.info("ChunkOwnershipManager initialized");
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Gets the town that owns a specific chunk.
     */
    public UUID getChunkOwner(ChunkPos chunkPos) {
        if (chunkPos == null) return null;
        
        lock.readLock().lock();
        try {
            // Check fast lookup first
            UUID cachedOwner = chunkToTownMap.get(chunkPos);
            if (cachedOwner != null) {
                return cachedOwner;
            }
            
            // Fall back to data manager - integrate with existing system
            try {
                // Use the existing chunk data manager to get ownership
                com.pokecobble.town.chunk.TownChunkDataManager existingDataManager =
                    com.pokecobble.town.chunk.TownChunkDataManager.getInstance();
                UUID owner = existingDataManager.getChunkOwner(chunkPos);

                if (owner != null) {
                    // Cache the result for future lookups
                    chunkToTownMap.put(chunkPos, owner);
                }

                return owner;
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error getting chunk owner from existing data manager", e);
                return null;
            }
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Gets the claim tag for a specific chunk.
     */
    public ClaimTag getChunkTag(ChunkPos chunkPos) {
        if (chunkPos == null) return null;
        
        lock.readLock().lock();
        try {
            // Check fast lookup first
            ClaimTag cachedTag = chunkToTagMap.get(chunkPos);
            if (cachedTag != null) {
                return cachedTag;
            }
            
            // Fall back to data manager - integrate with existing system
            try {
                // Use the existing chunk data manager to get tag
                com.pokecobble.town.chunk.TownChunkDataManager existingDataManager =
                    com.pokecobble.town.chunk.TownChunkDataManager.getInstance();
                ClaimTag tag = existingDataManager.getChunkTag(chunkPos);

                if (tag != null) {
                    // Cache the result for future lookups
                    chunkToTagMap.put(chunkPos, tag);
                }

                return tag;
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error getting chunk tag from existing data manager", e);
                return null;
            }
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Updates chunk ownership in both the fast lookup and underlying storage.
     */
    public void updateChunkOwnership(ChunkPos chunkPos, UUID newTownId) {
        if (chunkPos == null) return;
        
        lock.writeLock().lock();
        try {
            if (newTownId != null) {
                chunkToTownMap.put(chunkPos, newTownId);
            } else {
                chunkToTownMap.remove(chunkPos);
                chunkToTagMap.remove(chunkPos); // Remove tag when chunk is unclaimed
            }
            
            Pokecobbleclaim.LOGGER.debug("Updated chunk ownership: {} -> {}", chunkPos, newTownId);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Updates the claim tag for a specific chunk.
     */
    public void updateChunkTag(ChunkPos chunkPos, ClaimTag newTag) {
        if (chunkPos == null) return;
        
        lock.writeLock().lock();
        try {
            if (newTag != null) {
                chunkToTagMap.put(chunkPos, newTag);
            } else {
                chunkToTagMap.remove(chunkPos);
            }
            
            Pokecobbleclaim.LOGGER.debug("Updated chunk tag: {} -> {}", chunkPos, 
                newTag != null ? newTag.getName() : "null");
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Batch update multiple chunk tags for efficiency.
     * This is used when claim tag settings are changed and affect multiple chunks.
     */
    public void batchUpdateChunkTags(UUID townId, java.util.Map<ChunkPos, ClaimTag> tagUpdates) {
        if (tagUpdates == null || tagUpdates.isEmpty()) return;
        
        lock.writeLock().lock();
        try {
            for (java.util.Map.Entry<ChunkPos, ClaimTag> entry : tagUpdates.entrySet()) {
                ChunkPos chunkPos = entry.getKey();
                ClaimTag tag = entry.getValue();
                
                // Verify this chunk is owned by the town
                UUID chunkOwner = getChunkOwner(chunkPos);
                if (townId.equals(chunkOwner)) {
                    updateChunkTag(chunkPos, tag);
                } else {
                    Pokecobbleclaim.LOGGER.warn("Attempted to update tag for chunk {} not owned by town {}", 
                        chunkPos, townId);
                }
            }
            
            Pokecobbleclaim.LOGGER.info("Batch updated {} chunk tags for town {}", 
                tagUpdates.size(), townId);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Invalidates cached data for a specific chunk.
     * Forces the next lookup to go to the underlying data manager.
     */
    public void invalidateChunk(ChunkPos chunkPos) {
        if (chunkPos == null) return;
        
        lock.writeLock().lock();
        try {
            chunkToTownMap.remove(chunkPos);
            chunkToTagMap.remove(chunkPos);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Invalidates all cached data for a specific town.
     * Used when a town is deleted or undergoes major changes.
     */
    public void invalidateTown(UUID townId) {
        if (townId == null) return;
        
        lock.writeLock().lock();
        try {
            // Remove all chunks owned by this town
            chunkToTownMap.entrySet().removeIf(entry -> townId.equals(entry.getValue()));
            
            // Remove corresponding tags
            chunkToTagMap.entrySet().removeIf(entry -> {
                ChunkPos chunkPos = entry.getKey();
                return !chunkToTownMap.containsKey(chunkPos);
            });
            
            Pokecobbleclaim.LOGGER.info("Invalidated cached data for town {}", townId);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Clears all cached data.
     * Forces all future lookups to go to the underlying data manager.
     */
    public void clearCache() {
        lock.writeLock().lock();
        try {
            chunkToTownMap.clear();
            chunkToTagMap.clear();
            Pokecobbleclaim.LOGGER.info("Cleared all chunk ownership cache");
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * Gets statistics about the cached data.
     */
    public String getStats() {
        lock.readLock().lock();
        try {
            return String.format("Ownership Cache: %d chunks, %d tags", 
                chunkToTownMap.size(), chunkToTagMap.size());
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * Gets the number of cached chunk ownerships.
     */
    public int getCachedChunkCount() {
        return chunkToTownMap.size();
    }
    
    /**
     * Gets the number of cached chunk tags.
     */
    public int getCachedTagCount() {
        return chunkToTagMap.size();
    }
}
