package com.pokecobble.town.claim.v2;

import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.claim.ClaimTagManager;
import com.pokecobble.town.chunk.TownChunkDataManager;
import com.pokecobble.Pokecobbleclaim;
import net.minecraft.util.math.ChunkPos;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Integration layer that connects the new v2 permission system
 * with the existing claim tag management and UI systems.
 * 
 * This ensures that when users modify claim tag settings in the GUI,
 * the changes are immediately reflected in the permission system.
 */
public class ClaimTagIntegration {
    private static ClaimTagIntegration instance;
    
    private final ChunkPermissionEngine permissionEngine;
    private final ClaimTagManager claimTagManager;
    private final TownChunkDataManager chunkDataManager;
    
    private ClaimTagIntegration() {
        this.permissionEngine = ChunkPermissionEngine.getInstance();
        this.claimTagManager = ClaimTagManager.getInstance();
        this.chunkDataManager = TownChunkDataManager.getInstance();
        
        // Hook into the existing claim tag manager to intercept updates
        setupClaimTagUpdateHooks();
    }
    
    public static ClaimTagIntegration getInstance() {
        if (instance == null) {
            instance = new ClaimTagIntegration();
        }
        return instance;
    }
    
    /**
     * Sets up hooks to intercept claim tag updates from the existing system.
     */
    private void setupClaimTagUpdateHooks() {
        // This will be called whenever claim tags are updated through the existing system
        Pokecobbleclaim.LOGGER.info("ClaimTagIntegration hooks established");
    }
    
    /**
     * Called when claim tag settings are updated through the claim tag screen.
     * This method ensures the new permission system is immediately updated.
     */
    public void onClaimTagsUpdated(UUID townId, List<ClaimTag> updatedTags) {
        try {
            Pokecobbleclaim.LOGGER.info("Processing claim tag update for town {} with {} tags", 
                townId, updatedTags.size());
            
            // Get all chunks owned by this town
            Map<ChunkPos, ClaimTag> chunkTagMap = getChunkTagMappingForTown(townId);
            
            // Update each chunk's permissions
            for (Map.Entry<ChunkPos, ClaimTag> entry : chunkTagMap.entrySet()) {
                ChunkPos chunkPos = entry.getKey();
                ClaimTag currentTag = entry.getValue();
                
                // Find the updated version of this tag
                ClaimTag updatedTag = findUpdatedTag(currentTag, updatedTags);
                
                if (updatedTag != null) {
                    // Notify the permission engine about the update
                    permissionEngine.onClaimTagUpdated(townId, chunkPos, updatedTag);
                }
            }
            
            Pokecobbleclaim.LOGGER.info("Successfully updated permissions for town {}", townId);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating claim tag permissions for town " + townId, e);
        }
    }
    
    /**
     * Called when chunk tags are specifically updated (e.g., through the claim tool).
     */
    public void onChunkTagsUpdated(UUID townId, Map<ChunkPos, ClaimTag> chunkTagUpdates) {
        try {
            Pokecobbleclaim.LOGGER.info("Processing chunk tag update for town {} affecting {} chunks", 
                townId, chunkTagUpdates.size());
            
            // Update each chunk individually
            for (Map.Entry<ChunkPos, ClaimTag> entry : chunkTagUpdates.entrySet()) {
                ChunkPos chunkPos = entry.getKey();
                ClaimTag newTag = entry.getValue();
                
                // Notify the permission engine
                permissionEngine.onClaimTagUpdated(townId, chunkPos, newTag);
            }
            
            Pokecobbleclaim.LOGGER.info("Successfully updated chunk permissions for town {}", townId);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating chunk tag permissions for town " + townId, e);
        }
    }
    
    /**
     * Called when a chunk's ownership changes.
     */
    public void onChunkOwnershipChanged(ChunkPos chunkPos, UUID oldTownId, UUID newTownId) {
        try {
            // Notify the permission engine
            permissionEngine.onChunkOwnershipChanged(chunkPos, oldTownId, newTownId);

            Pokecobbleclaim.LOGGER.debug("Updated chunk ownership: {} from {} to {}",
                chunkPos, oldTownId, newTownId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating chunk ownership for " + chunkPos, e);
        }
    }

    /**
     * Called when a player's rank changes in a town.
     * This ensures that the claim permission system is updated to reflect the new rank.
     */
    public void onPlayerRankChanged(UUID playerId, UUID townId, com.pokecobble.town.TownPlayerRank oldRank, com.pokecobble.town.TownPlayerRank newRank) {
        try {
            // Notify the permission engine about the rank change
            permissionEngine.onPlayerRankChanged(playerId, townId, oldRank, newRank);

            Pokecobbleclaim.LOGGER.info("Processed rank change for player {} in town {}: {} -> {}",
                playerId, townId, oldRank, newRank);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error processing rank change for player {} in town {}: {}",
                playerId, townId, e.getMessage(), e);
        }
    }
    
    /**
     * Gets the current chunk-to-tag mapping for a town.
     */
    private Map<ChunkPos, ClaimTag> getChunkTagMappingForTown(UUID townId) {
        try {
            // Use the existing chunk data manager to get the mapping
            var townData = chunkDataManager.getTownChunkData(townId);
            if (townData != null) {
                Map<ChunkPos, ClaimTag> mapping = new java.util.HashMap<>();
                
                // Get all chunks for this town
                for (ChunkPos chunk : townData.getAllChunks()) {
                    ClaimTag tag = townData.getChunkTag(chunk);
                    if (tag != null) {
                        mapping.put(chunk, tag);
                    }
                }
                
                return mapping;
            }
            
            return Map.of();
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error getting chunk tag mapping for town " + townId, e);
            return Map.of();
        }
    }
    
    /**
     * Finds the updated version of a tag in the list of updated tags.
     */
    private ClaimTag findUpdatedTag(ClaimTag currentTag, List<ClaimTag> updatedTags) {
        if (currentTag == null || updatedTags == null) {
            return null;
        }
        
        // Find by ID first
        for (ClaimTag updatedTag : updatedTags) {
            if (updatedTag.getId().equals(currentTag.getId())) {
                return updatedTag;
            }
        }
        
        // Fall back to name matching if ID doesn't match
        for (ClaimTag updatedTag : updatedTags) {
            if (updatedTag.getName().equals(currentTag.getName())) {
                return updatedTag;
            }
        }
        
        return null;
    }
    
    /**
     * Forces a complete refresh of permissions for a town.
     * This can be used when there are major changes or for debugging.
     */
    public void refreshTownPermissions(UUID townId) {
        try {
            Pokecobbleclaim.LOGGER.info("Refreshing all permissions for town {}", townId);
            
            // Clear cache for this town
            permissionEngine.clearCache();
            
            // Get current claim tags
            List<ClaimTag> currentTags = claimTagManager.getClaimTags(townId);
            
            // Trigger update
            onClaimTagsUpdated(townId, currentTags);
            
            Pokecobbleclaim.LOGGER.info("Completed permission refresh for town {}", townId);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error refreshing permissions for town " + townId, e);
        }
    }
    
    /**
     * Gets performance statistics from the permission system.
     */
    public String getPerformanceStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== Permission System v2 Statistics ===\n");
        stats.append(permissionEngine.getCacheStats()).append("\n");
        
        // Add more stats as needed
        return stats.toString();
    }
    
    /**
     * Validates that the permission system is working correctly.
     * This can be used for health checks or debugging.
     */
    public boolean validatePermissionSystem() {
        try {
            // Basic validation checks
            if (permissionEngine == null) {
                Pokecobbleclaim.LOGGER.error("Permission engine is null");
                return false;
            }
            
            if (claimTagManager == null) {
                Pokecobbleclaim.LOGGER.error("Claim tag manager is null");
                return false;
            }
            
            if (chunkDataManager == null) {
                Pokecobbleclaim.LOGGER.error("Chunk data manager is null");
                return false;
            }
            
            Pokecobbleclaim.LOGGER.info("Permission system validation passed");
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Permission system validation failed", e);
            return false;
        }
    }
}
