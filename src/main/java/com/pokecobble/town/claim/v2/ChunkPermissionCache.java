package com.pokecobble.town.claim.v2;

import net.minecraft.util.math.ChunkPos;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.UUID;

/**
 * High-performance caching system for chunk permissions.
 * Uses intelligent cache invalidation and LRU eviction.
 */
public class ChunkPermissionCache {
    private static final long CACHE_EXPIRY_MS = 30000; // 30 seconds
    private static final int MAX_CACHE_SIZE = 10000; // Maximum cached entries
    
    // Cache data structures
    private final ConcurrentHashMap<String, Boolean> permissionCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> cacheTimestamps = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> accessTimes = new ConcurrentHashMap<>();
    
    // Statistics
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    private final AtomicLong cacheEvictions = new AtomicLong(0);
    
    /**
     * Gets a cached permission result if it exists and is still valid.
     */
    public Boolean getCachedPermission(String cacheKey) {
        Long timestamp = cacheTimestamps.get(cacheKey);
        if (timestamp == null) {
            cacheMisses.incrementAndGet();
            return null;
        }
        
        // Check if cache entry has expired
        if (System.currentTimeMillis() - timestamp > CACHE_EXPIRY_MS) {
            // Remove expired entry
            permissionCache.remove(cacheKey);
            cacheTimestamps.remove(cacheKey);
            accessTimes.remove(cacheKey);
            cacheMisses.incrementAndGet();
            return null;
        }
        
        // Update access time for LRU
        accessTimes.put(cacheKey, System.currentTimeMillis());
        cacheHits.incrementAndGet();
        
        return permissionCache.get(cacheKey);
    }
    
    /**
     * Caches a permission result.
     */
    public void cachePermission(String cacheKey, boolean hasPermission) {
        // Check if we need to evict old entries
        if (permissionCache.size() >= MAX_CACHE_SIZE) {
            evictOldestEntries();
        }
        
        long currentTime = System.currentTimeMillis();
        permissionCache.put(cacheKey, hasPermission);
        cacheTimestamps.put(cacheKey, currentTime);
        accessTimes.put(cacheKey, currentTime);
    }
    
    /**
     * Invalidates all cache entries for a specific chunk.
     * Called when chunk permissions change.
     */
    public void invalidateChunk(ChunkPos chunkPos) {
        String chunkPattern = ":" + chunkPos.x + "," + chunkPos.z + ":";
        
        // Remove all entries that match this chunk
        permissionCache.entrySet().removeIf(entry -> entry.getKey().contains(chunkPattern));
        cacheTimestamps.entrySet().removeIf(entry -> entry.getKey().contains(chunkPattern));
        accessTimes.entrySet().removeIf(entry -> entry.getKey().contains(chunkPattern));
    }
    
    /**
     * Invalidates all cache entries for a specific player.
     * Called when player's town membership or rank changes.
     */
    public void invalidatePlayer(String playerId) {
        String playerPattern = playerId + ":";
        
        // Remove all entries that match this player
        permissionCache.entrySet().removeIf(entry -> entry.getKey().startsWith(playerPattern));
        cacheTimestamps.entrySet().removeIf(entry -> entry.getKey().startsWith(playerPattern));
        accessTimes.entrySet().removeIf(entry -> entry.getKey().startsWith(playerPattern));
    }
    
    /**
     * Clears all cached entries.
     */
    public void clearAll() {
        permissionCache.clear();
        cacheTimestamps.clear();
        accessTimes.clear();
    }

    /**
     * Invalidates all cache entries for a specific player in a specific chunk.
     */
    public void invalidatePlayerChunk(UUID playerId, ChunkPos chunkPos) {
        String playerChunkPattern = playerId + ":" + chunkPos.x + "," + chunkPos.z + ":";

        // Remove all entries that match this player and chunk
        permissionCache.entrySet().removeIf(entry -> entry.getKey().startsWith(playerChunkPattern));
        cacheTimestamps.entrySet().removeIf(entry -> entry.getKey().startsWith(playerChunkPattern));
        accessTimes.entrySet().removeIf(entry -> entry.getKey().startsWith(playerChunkPattern));
    }
    
    /**
     * Evicts the oldest entries when cache is full.
     * Uses LRU (Least Recently Used) strategy.
     */
    private void evictOldestEntries() {
        int entriesToEvict = MAX_CACHE_SIZE / 4; // Evict 25% when full
        
        // Find the oldest entries by access time
        accessTimes.entrySet().stream()
            .sorted((e1, e2) -> Long.compare(e1.getValue(), e2.getValue()))
            .limit(entriesToEvict)
            .forEach(entry -> {
                String key = entry.getKey();
                permissionCache.remove(key);
                cacheTimestamps.remove(key);
                accessTimes.remove(key);
                cacheEvictions.incrementAndGet();
            });
    }
    
    /**
     * Removes expired entries from the cache.
     * Should be called periodically to prevent memory leaks.
     */
    public void cleanupExpiredEntries() {
        long currentTime = System.currentTimeMillis();
        
        cacheTimestamps.entrySet().removeIf(entry -> {
            if (currentTime - entry.getValue() > CACHE_EXPIRY_MS) {
                String key = entry.getKey();
                permissionCache.remove(key);
                accessTimes.remove(key);
                return true;
            }
            return false;
        });
    }
    
    /**
     * Gets cache performance statistics.
     */
    public String getStats() {
        long hits = cacheHits.get();
        long misses = cacheMisses.get();
        long total = hits + misses;
        double hitRate = total > 0 ? (double) hits / total * 100 : 0;
        
        return String.format(
            "Cache Stats: %d entries, %.1f%% hit rate (%d hits, %d misses), %d evictions",
            permissionCache.size(), hitRate, hits, misses, cacheEvictions.get()
        );
    }
    
    /**
     * Gets the current cache size.
     */
    public int getSize() {
        return permissionCache.size();
    }
    
    /**
     * Gets the cache hit rate as a percentage.
     */
    public double getHitRate() {
        long hits = cacheHits.get();
        long misses = cacheMisses.get();
        long total = hits + misses;
        return total > 0 ? (double) hits / total * 100 : 0;
    }
}
