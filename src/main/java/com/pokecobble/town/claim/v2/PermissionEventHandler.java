package com.pokecobble.town.claim.v2;

import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.fabric.api.event.player.PlayerBlockBreakEvents;
import net.fabricmc.fabric.api.event.player.UseBlockCallback;
import net.fabricmc.fabric.api.event.player.UseEntityCallback;
import net.minecraft.block.*;
import net.minecraft.block.Blocks;
import net.minecraft.entity.Entity;
import net.minecraft.entity.passive.AnimalEntity;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.item.BlockItem;
import net.minecraft.item.HoeItem;
import net.minecraft.item.Items;
import net.minecraft.item.ItemStack;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.ActionResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;

/**
 * Event handler that intercepts player actions and validates permissions
 * using the new ChunkPermissionEngine.
 * 
 * This replaces the old ServerChunkProtectionHandler with a cleaner,
 * more efficient implementation.
 */
public class PermissionEventHandler {
    private static PermissionEventHandler instance;
    private final ChunkPermissionEngine permissionEngine;
    
    private PermissionEventHandler() {
        this.permissionEngine = ChunkPermissionEngine.getInstance();
        registerEventHandlers();
    }
    
    public static PermissionEventHandler getInstance() {
        if (instance == null) {
            instance = new PermissionEventHandler();
        }
        return instance;
    }
    
    /**
     * Registers all event handlers for permission checking.
     */
    private void registerEventHandlers() {
        // Block breaking events
        PlayerBlockBreakEvents.BEFORE.register((world, player, pos, state, blockEntity) -> {
            if (!(player instanceof ServerPlayerEntity serverPlayer)) {
                return true; // Allow for non-server players
            }

            // Determine the permission type based on the block being broken
            Block block = state.getBlock();
            int permissionType;

            // Check if this is a crop block - use crops permission for breaking crops
            if (isCropBlock(block)) {
                permissionType = ChunkPermissionEngine.PERMISSION_CROPS;
            } else {
                // For all other blocks, use build permission
                permissionType = ChunkPermissionEngine.PERMISSION_BUILD;
            }

            return permissionEngine.hasPermission(serverPlayer, world, pos, permissionType);
        });
        
        // Block interaction events (right-click)
        UseBlockCallback.EVENT.register((player, world, hand, hitResult) -> {
            if (!(player instanceof ServerPlayerEntity serverPlayer)) {
                return ActionResult.PASS;
            }

            BlockPos pos = hitResult.getBlockPos();
            BlockState state = world.getBlockState(pos);
            Block block = state.getBlock();
            ItemStack itemInHand = player.getStackInHand(hand);

            // Check for hoe usage on grass blocks (creating farmland)
            if (itemInHand.getItem() instanceof HoeItem &&
                (block instanceof GrassBlock || block == Blocks.DIRT || block == Blocks.COARSE_DIRT)) {
                // Hoe on grass/dirt to create farmland - use crops permission
                if (!permissionEngine.hasPermission(serverPlayer, world, pos,
                    ChunkPermissionEngine.PERMISSION_CROPS)) {
                    return ActionResult.FAIL;
                }
                return ActionResult.PASS;
            }

            // Check for crop seed placement on farmland
            if (block instanceof FarmlandBlock && isCropSeedItem(itemInHand.getItem())) {
                // Planting seeds on farmland - use crops permission
                if (!permissionEngine.hasPermission(serverPlayer, world, pos,
                    ChunkPermissionEngine.PERMISSION_CROPS)) {
                    return ActionResult.FAIL;
                }
                return ActionResult.PASS;
            }

            // Determine the permission type for the block being clicked
            int permissionType = getPermissionTypeForBlock(block);

            // If the block is interactable (not build permission), prioritize interaction over placement
            if (permissionType != ChunkPermissionEngine.PERMISSION_BUILD) {
                // This is an interactable block (door, chest, etc.) - check interaction permission
                if (!permissionEngine.hasPermission(serverPlayer, world, pos, permissionType)) {
                    return ActionResult.FAIL;
                }
                // Interaction allowed - don't check for block placement
                return ActionResult.PASS;
            }

            // For non-interactable blocks, check if player is placing a block
            if (player.getStackInHand(hand).getItem() instanceof BlockItem) {
                BlockPos placementPos = pos.offset(hitResult.getSide());

                if (!permissionEngine.hasPermission(serverPlayer, world, placementPos,
                    ChunkPermissionEngine.PERMISSION_BUILD)) {
                    return ActionResult.FAIL;
                }
            }

            // Check interaction permissions for the block (for non-interactable blocks, this will be build permission)
            if (!permissionEngine.hasPermission(serverPlayer, world, pos, permissionType)) {
                return ActionResult.FAIL;
            }

            return ActionResult.PASS;
        });
        
        // Entity interaction events
        UseEntityCallback.EVENT.register((player, world, hand, entity, hitResult) -> {
            if (!(player instanceof ServerPlayerEntity serverPlayer)) {
                return ActionResult.PASS;
            }
            
            BlockPos entityPos = entity.getBlockPos();
            int permissionType = getPermissionTypeForEntity(entity);
            
            if (!permissionEngine.hasPermission(serverPlayer, world, entityPos, permissionType)) {
                return ActionResult.FAIL;
            }
            
            return ActionResult.PASS;
        });
        
        Pokecobbleclaim.LOGGER.info("PermissionEventHandler v2 registered all event handlers");
    }
    
    /**
     * Determines the permission type required for interacting with a block.
     */
    private int getPermissionTypeForBlock(Block block) {
        // Container blocks
        if (isContainerBlock(block)) {
            return ChunkPermissionEngine.PERMISSION_CONTAINERS;
        }
        
        // Door blocks
        if (isDoorBlock(block)) {
            return ChunkPermissionEngine.PERMISSION_DOORS;
        }
        
        // Redstone blocks
        if (isRedstoneBlock(block)) {
            return ChunkPermissionEngine.PERMISSION_REDSTONE;
        }
        
        // Crop blocks
        if (isCropBlock(block)) {
            return ChunkPermissionEngine.PERMISSION_CROPS;
        }
        
        // Interactive blocks (buttons, levers, etc.)
        if (isInteractiveBlock(block)) {
            return ChunkPermissionEngine.PERMISSION_INTERACT;
        }
        
        // Default to build permission
        return ChunkPermissionEngine.PERMISSION_BUILD;
    }
    
    /**
     * Determines the permission type required for interacting with an entity.
     */
    private int getPermissionTypeForEntity(Entity entity) {
        if (entity instanceof VillagerEntity) {
            return ChunkPermissionEngine.PERMISSION_VILLAGERS;
        }
        
        if (entity instanceof AnimalEntity) {
            return ChunkPermissionEngine.PERMISSION_ANIMALS;
        }
        
        // Default to interact permission for other entities
        return ChunkPermissionEngine.PERMISSION_INTERACT;
    }
    
    /**
     * Checks if a block is a container (chest, furnace, etc.).
     */
    private boolean isContainerBlock(Block block) {
        return block instanceof ChestBlock ||
               block instanceof FurnaceBlock ||
               block instanceof BlastFurnaceBlock ||
               block instanceof SmokerBlock ||
               block instanceof BarrelBlock ||
               block instanceof ShulkerBoxBlock ||
               block instanceof HopperBlock ||
               block instanceof DispenserBlock ||
               block instanceof DropperBlock ||
               block instanceof BrewingStandBlock ||
               block instanceof EnchantingTableBlock ||
               block instanceof AnvilBlock ||
               block instanceof BeaconBlock;
    }
    
    /**
     * Checks if a block is a door or trapdoor.
     */
    private boolean isDoorBlock(Block block) {
        return block instanceof DoorBlock ||
               block instanceof TrapdoorBlock ||
               block instanceof FenceGateBlock;
    }
    
    /**
     * Checks if a block is redstone-related.
     */
    private boolean isRedstoneBlock(Block block) {
        return block instanceof RedstoneBlock ||
               block instanceof RedstoneWireBlock ||
               block instanceof RepeaterBlock ||
               block instanceof ComparatorBlock ||
               block instanceof RedstoneOreBlock ||
               block instanceof RedstoneTorchBlock ||
               block instanceof WallRedstoneTorchBlock ||
               block instanceof PistonBlock ||
               block instanceof PistonHeadBlock ||
               block instanceof NoteBlock ||
               block instanceof JukeboxBlock ||
               block instanceof ObserverBlock ||
               block instanceof PoweredRailBlock ||
               block instanceof DetectorRailBlock ||
               block instanceof RailBlock;
    }
    
    /**
     * Checks if a block is crop-related (for breaking - excludes farmland).
     */
    private boolean isCropBlock(Block block) {
        return block instanceof CropBlock ||
               block instanceof StemBlock ||
               block instanceof AttachedStemBlock ||
               block instanceof ComposterBlock ||
               block instanceof SweetBerryBushBlock ||
               block instanceof CocoaBlock ||
               block instanceof NetherWartBlock ||
               block instanceof BambooBlock ||
               block instanceof SugarCaneBlock ||
               block instanceof CactusBlock ||
               block instanceof KelpBlock ||
               block instanceof SeaPickleBlock;
    }

    /**
     * Checks if an item is a crop seed that can be planted.
     */
    private boolean isCropSeedItem(net.minecraft.item.Item item) {
        return item == Items.WHEAT_SEEDS ||
               item == Items.CARROT ||
               item == Items.POTATO ||
               item == Items.BEETROOT_SEEDS ||
               item == Items.PUMPKIN_SEEDS ||
               item == Items.MELON_SEEDS ||
               item == Items.NETHER_WART ||
               item == Items.SWEET_BERRIES ||
               item == Items.COCOA_BEANS;
    }
    
    /**
     * Checks if a block is interactive (buttons, levers, etc.).
     */
    private boolean isInteractiveBlock(Block block) {
        return block instanceof ButtonBlock ||
               block instanceof LeverBlock ||
               block instanceof PressurePlateBlock ||
               block instanceof WeightedPressurePlateBlock ||
               block instanceof TripwireHookBlock ||
               block instanceof BellBlock ||
               block instanceof LecternBlock ||
               block instanceof CraftingTableBlock ||
               block instanceof CartographyTableBlock ||
               block instanceof FletchingTableBlock ||
               block instanceof SmithingTableBlock ||
               block instanceof StonecutterBlock ||
               block instanceof LoomBlock ||
               block instanceof GrindstoneBlock;
    }
}
