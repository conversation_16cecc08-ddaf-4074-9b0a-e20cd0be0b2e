package com.pokecobble.town.claim;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.world.World;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.UUID;

/**
 * Monitors and validates the protection system to ensure it's working correctly.
 * This class provides fail-safe monitoring and alerts for protection failures.
 */
public class ProtectionMonitor {
    private static ProtectionMonitor instance;
    
    // Statistics tracking
    private final AtomicLong totalPermissionChecks = new AtomicLong(0);
    private final AtomicLong permissionDenials = new AtomicLong(0);
    private final AtomicLong protectionFailures = new AtomicLong(0);
    private final ConcurrentHashMap<String, AtomicLong> actionCounts = new ConcurrentHashMap<>();
    
    // Alert thresholds
    private static final long FAILURE_ALERT_THRESHOLD = 10; // Alert after 10 failures
    private static final long CHECK_INTERVAL = 60000; // Check every minute
    
    private long lastStatsReport = System.currentTimeMillis();
    
    private ProtectionMonitor() {
        // Initialize action counters
        for (int i = 0; i <= 7; i++) {
            actionCounts.put("action_" + i, new AtomicLong(0));
        }
    }
    
    public static ProtectionMonitor getInstance() {
        if (instance == null) {
            instance = new ProtectionMonitor();
        }
        return instance;
    }
    
    /**
     * Records a permission check for monitoring.
     */
    public void recordPermissionCheck(ServerPlayerEntity player, World world, BlockPos pos, int action, boolean granted) {
        try {
            totalPermissionChecks.incrementAndGet();
            actionCounts.get("action_" + action).incrementAndGet();
            
            if (!granted) {
                permissionDenials.incrementAndGet();
                
                // Log detailed denial information
                ChunkPos chunkPos = new ChunkPos(pos);
                com.pokecobble.town.chunk.TownChunkDataManager dataManager =
                    com.pokecobble.town.chunk.TownChunkDataManager.getInstance();
                UUID owningTownId = dataManager.getChunkOwner(chunkPos);
                
                Pokecobbleclaim.LOGGER.info("MONITOR: Permission denied - Player: {}, Chunk: {}, Town: {}, Action: {}", 
                    player.getName().getString(), chunkPos, owningTownId, action);
            }
            
            // Report statistics periodically
            long now = System.currentTimeMillis();
            if (now - lastStatsReport > CHECK_INTERVAL) {
                reportStatistics();
                lastStatsReport = now;
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error in protection monitor: " + e.getMessage(), e);
        }
    }
    
    /**
     * Records a protection system failure for monitoring.
     */
    public void recordProtectionFailure(String context, Exception error) {
        try {
            long failures = protectionFailures.incrementAndGet();
            
            Pokecobbleclaim.LOGGER.error("CRITICAL: Protection system failure #{} in {}: {}", 
                failures, context, error.getMessage(), error);
            
            // Alert if too many failures
            if (failures % FAILURE_ALERT_THRESHOLD == 0) {
                Pokecobbleclaim.LOGGER.error("ALERT: {} protection failures detected! System may be compromised!", failures);
            }
            
        } catch (Exception e) {
            // Even the monitor can fail - use basic logging
            System.err.println("CRITICAL: Protection monitor itself failed: " + e.getMessage());
        }
    }
    
    /**
     * Validates that the protection system is working correctly.
     * This performs basic sanity checks.
     */
    public boolean validateProtectionSystem() {
        try {
            // Check if permission validator is initialized
            if (ServerChunkPermissionValidator.getInstance() == null) {
                Pokecobbleclaim.LOGGER.error("CRITICAL: Permission validator is not initialized!");
                return false;
            }
            
            // Check if chunk data manager is working
            com.pokecobble.town.chunk.TownChunkDataManager dataManager =
                com.pokecobble.town.chunk.TownChunkDataManager.getInstance();
            if (dataManager == null) {
                Pokecobbleclaim.LOGGER.error("CRITICAL: Chunk data manager is not initialized!");
                return false;
            }
            
            // Check if protection handlers are initialized
            if (!ServerChunkProtectionHandler.isInitialized()) {
                Pokecobbleclaim.LOGGER.error("CRITICAL: Protection handlers are not initialized!");
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("CRITICAL: Protection system validation failed: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Reports protection system statistics.
     */
    private void reportStatistics() {
        try {
            long total = totalPermissionChecks.get();
            long denials = permissionDenials.get();
            long failures = protectionFailures.get();
            
            double denialRate = total > 0 ? (denials * 100.0 / total) : 0;
            
            Pokecobbleclaim.LOGGER.info("PROTECTION STATS: {} checks, {} denials ({:.1f}%), {} failures", 
                total, denials, denialRate, failures);
            
            // Report action breakdown
            StringBuilder actionStats = new StringBuilder("Action breakdown: ");
            for (int i = 0; i <= 7; i++) {
                long count = actionCounts.get("action_" + i).get();
                if (count > 0) {
                    actionStats.append(getActionName(i)).append(":").append(count).append(" ");
                }
            }
            Pokecobbleclaim.LOGGER.debug(actionStats.toString());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error reporting protection statistics: " + e.getMessage(), e);
        }
    }
    
    /**
     * Gets a human-readable action name.
     */
    private String getActionName(int action) {
        switch (action) {
            case 0: return "BUILD";
            case 1: return "INTERACT";
            case 2: return "CONTAINERS";
            case 3: return "REDSTONE";
            case 4: return "DOORS";
            case 5: return "CROPS";
            case 6: return "ANIMALS";
            case 7: return "VILLAGERS";
            default: return "UNKNOWN";
        }
    }
    
    /**
     * Gets current protection statistics.
     */
    public String getStatistics() {
        long total = totalPermissionChecks.get();
        long denials = permissionDenials.get();
        long failures = protectionFailures.get();
        double denialRate = total > 0 ? (denials * 100.0 / total) : 0;
        
        return String.format("Protection Stats: %d checks, %d denials (%.1f%%), %d failures", 
            total, denials, denialRate, failures);
    }
}
