package com.pokecobble.town.claim;

import com.pokecobble.town.network.chunk.ClaimTagSyncHandler;
import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.fabric.api.event.player.PlayerBlockBreakEvents;
import net.fabricmc.fabric.api.event.player.UseBlockCallback;
import net.fabricmc.fabric.api.event.player.UseEntityCallback;
import net.minecraft.block.*;
import net.minecraft.block.Blocks;
import net.minecraft.entity.Entity;
import net.minecraft.entity.passive.AnimalEntity;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.item.HoeItem;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.ActionResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.world.World;

/**
 * Simple chunk protection handler that uses the new unified permission system.
 * Replaces the complex overlapping permission systems with a single, clear implementation.
 */
public class SimpleChunkProtectionHandler {
    
    private static SimpleChunkProtectionHandler instance;
    
    private SimpleChunkProtectionHandler() {}
    
    public static SimpleChunkProtectionHandler getInstance() {
        if (instance == null) {
            instance = new SimpleChunkProtectionHandler();
        }
        return instance;
    }
    
    /**
     * Initializes the protection system by registering event handlers.
     */
    public void initialize() {
        registerEventHandlers();
        Pokecobbleclaim.LOGGER.info("Simple chunk protection handler initialized");
    }
    
    /**
     * Registers all event handlers for protection.
     */
    private void registerEventHandlers() {
        // Block breaking events
        PlayerBlockBreakEvents.BEFORE.register((world, player, pos, state, blockEntity) -> {
            if (!(player instanceof ServerPlayerEntity serverPlayer)) {
                return true; // Allow for non-server players
            }

            ChunkPos chunkPos = new ChunkPos(pos);
            Block block = state.getBlock();
            int permissionType;

            // Check if this is a crop block - use crops permission for breaking crops
            if (isCropBlock(block)) {
                permissionType = ClaimTagSyncHandler.PERMISSION_CROPS;
            } else {
                // For all other blocks, use build permission
                permissionType = ClaimTagSyncHandler.PERMISSION_BUILD;
            }

            boolean allowed = ClaimTagSyncHandler.hasPermission(serverPlayer, chunkPos, permissionType);

            if (!allowed) {
                String actionName = (permissionType == ClaimTagSyncHandler.PERMISSION_CROPS) ? "farm crops" : "break blocks";
                notifyPermissionDenied(serverPlayer, actionName);
            }

            return allowed;
        });
        
        // Block interaction events (right-click) - handles both interaction and placement
        UseBlockCallback.EVENT.register((player, world, hand, hitResult) -> {
            if (!(player instanceof ServerPlayerEntity serverPlayer)) {
                return ActionResult.PASS;
            }

            BlockPos pos = hitResult.getBlockPos();
            BlockState state = world.getBlockState(pos);
            Block block = state.getBlock();
            ChunkPos chunkPos = new ChunkPos(pos);

            // Check for hoe usage on grass blocks (creating farmland)
            if (player.getStackInHand(hand).getItem() instanceof HoeItem &&
                (block instanceof GrassBlock || block == Blocks.DIRT || block == Blocks.COARSE_DIRT)) {
                // Hoe on grass/dirt to create farmland - use crops permission
                boolean allowed = ClaimTagSyncHandler.hasPermission(serverPlayer, chunkPos,
                    ClaimTagSyncHandler.PERMISSION_CROPS);

                if (!allowed) {
                    notifyPermissionDenied(serverPlayer, "farm crops");
                    return ActionResult.FAIL;
                }
                return ActionResult.PASS;
            }

            // Check for crop seed placement on farmland
            if (block instanceof FarmlandBlock && isCropSeedItem(player.getStackInHand(hand).getItem())) {
                // Planting seeds on farmland - use crops permission
                boolean allowed = ClaimTagSyncHandler.hasPermission(serverPlayer, chunkPos,
                    ClaimTagSyncHandler.PERMISSION_CROPS);

                if (!allowed) {
                    notifyPermissionDenied(serverPlayer, "farm crops");
                    return ActionResult.FAIL;
                }
                return ActionResult.PASS;
            }

            // Determine the permission type for the block being clicked
            int permissionType = getPermissionTypeForBlock(block, state);

            // If the block is interactable (not build permission), prioritize interaction over placement
            if (permissionType != ClaimTagSyncHandler.PERMISSION_BUILD) {
                // This is an interactable block (door, chest, etc.) - check interaction permission first
                boolean interactionAllowed = ClaimTagSyncHandler.hasPermission(serverPlayer, chunkPos, permissionType);

                if (!interactionAllowed) {
                    notifyPermissionDenied(serverPlayer, getActionNameForPermission(permissionType));
                    return ActionResult.FAIL;
                }

                // Interaction is allowed, but if player is holding a block, also check build permission
                if (player.getStackInHand(hand).getItem() instanceof net.minecraft.item.BlockItem) {
                    // Player is holding a block - check if they can place blocks
                    boolean buildAllowed = ClaimTagSyncHandler.hasPermission(serverPlayer, chunkPos,
                        ClaimTagSyncHandler.PERMISSION_BUILD);

                    if (!buildAllowed) {
                        notifyPermissionDenied(serverPlayer, "place blocks");
                        return ActionResult.FAIL;
                    }
                }

                // Both interaction and (if applicable) build permissions are satisfied
                return ActionResult.PASS;
            }

            // For non-interactable blocks, check if this is a block placement (player holding a block item)
            if (player.getStackInHand(hand).getItem() instanceof net.minecraft.item.BlockItem) {
                // Block placement - check build permission
                boolean allowed = ClaimTagSyncHandler.hasPermission(serverPlayer, chunkPos,
                    ClaimTagSyncHandler.PERMISSION_BUILD);

                if (!allowed) {
                    notifyPermissionDenied(serverPlayer, "place blocks");
                    return ActionResult.FAIL;
                }
            } else {
                // Block interaction on non-interactable block - check build permission
                boolean allowed = ClaimTagSyncHandler.hasPermission(serverPlayer, chunkPos, permissionType);

                if (!allowed) {
                    notifyPermissionDenied(serverPlayer, getActionNameForPermission(permissionType));
                    return ActionResult.FAIL;
                }
            }

            return ActionResult.PASS;
        });
        
        // Entity interaction events
        UseEntityCallback.EVENT.register((player, world, hand, entity, hitResult) -> {
            if (!(player instanceof ServerPlayerEntity serverPlayer)) {
                return ActionResult.PASS;
            }
            
            BlockPos entityPos = entity.getBlockPos();
            ChunkPos chunkPos = new ChunkPos(entityPos);
            int permissionType = getPermissionTypeForEntity(entity);
            
            boolean allowed = ClaimTagSyncHandler.hasPermission(serverPlayer, chunkPos, permissionType);
            
            if (!allowed) {
                notifyPermissionDenied(serverPlayer, getActionNameForPermission(permissionType));
                return ActionResult.FAIL;
            }
            
            return ActionResult.PASS;
        });
    }
    
    /**
     * Determines the permission type required for a block interaction.
     */
    private int getPermissionTypeForBlock(Block block, BlockState state) {
        // Container blocks
        if (block instanceof ChestBlock || block instanceof BarrelBlock || 
            block instanceof ShulkerBoxBlock || block instanceof FurnaceBlock ||
            block instanceof BlastFurnaceBlock || block instanceof SmokerBlock ||
            block instanceof BrewingStandBlock || block instanceof HopperBlock ||
            block instanceof DispenserBlock || block instanceof DropperBlock) {
            return ClaimTagSyncHandler.PERMISSION_CONTAINERS;
        }
        
        // Door blocks
        if (block instanceof DoorBlock || block instanceof TrapdoorBlock ||
            block instanceof FenceGateBlock) {
            return ClaimTagSyncHandler.PERMISSION_DOORS;
        }
        
        // Redstone blocks
        if (block instanceof RedstoneBlock || block instanceof RepeaterBlock ||
            block instanceof ComparatorBlock || block instanceof RedstoneWireBlock ||
            block instanceof LeverBlock || block instanceof ButtonBlock ||
            block instanceof PressurePlateBlock || block instanceof WeightedPressurePlateBlock) {
            return ClaimTagSyncHandler.PERMISSION_REDSTONE;
        }
        
        // Crop blocks
        if (block instanceof CropBlock || block instanceof StemBlock ||
            block instanceof AttachedStemBlock || block instanceof CocoaBlock ||
            block instanceof NetherWartBlock || block instanceof SweetBerryBushBlock) {
            return ClaimTagSyncHandler.PERMISSION_CROPS;
        }
        
        // Default to interact permission
        return ClaimTagSyncHandler.PERMISSION_INTERACT;
    }
    
    /**
     * Determines the permission type required for an entity interaction.
     */
    private int getPermissionTypeForEntity(Entity entity) {
        if (entity instanceof VillagerEntity) {
            return ClaimTagSyncHandler.PERMISSION_VILLAGERS;
        } else if (entity instanceof AnimalEntity) {
            return ClaimTagSyncHandler.PERMISSION_ANIMALS;
        } else {
            return ClaimTagSyncHandler.PERMISSION_INTERACT;
        }
    }
    
    /**
     * Gets a human-readable action name for a permission type.
     */
    private String getActionNameForPermission(int permissionType) {
        return switch (permissionType) {
            case ClaimTagSyncHandler.PERMISSION_BUILD -> "build";
            case ClaimTagSyncHandler.PERMISSION_INTERACT -> "interact";
            case ClaimTagSyncHandler.PERMISSION_CONTAINERS -> "access containers";
            case ClaimTagSyncHandler.PERMISSION_REDSTONE -> "use redstone";
            case ClaimTagSyncHandler.PERMISSION_DOORS -> "use doors";
            case ClaimTagSyncHandler.PERMISSION_CROPS -> "farm crops";
            case ClaimTagSyncHandler.PERMISSION_ANIMALS -> "interact with animals";
            case ClaimTagSyncHandler.PERMISSION_VILLAGERS -> "trade with villagers";
            default -> "perform this action";
        };
    }
    
    /**
     * Notifies a player that their action was denied.
     */
    private void notifyPermissionDenied(ServerPlayerEntity player, String action) {
        player.sendMessage(Text.literal("§cYou don't have permission to " + action + " here!"), false);
    }

    // Static helper methods for compatibility with existing code

    /**
     * Validates if a player can place a block at a specific position.
     * This is called by other systems that need to check block placement permissions.
     */
    public static boolean canPlaceBlock(ServerPlayerEntity player, World world, BlockPos pos, BlockState state) {
        ChunkPos chunkPos = new ChunkPos(pos);
        return ClaimTagSyncHandler.hasPermission(player, chunkPos, ClaimTagSyncHandler.PERMISSION_BUILD);
    }

    /**
     * Validates if a player can break a block at a specific position.
     * This is called by other systems that need to check block breaking permissions.
     */
    public static boolean canBreakBlock(ServerPlayerEntity player, World world, BlockPos pos) {
        ChunkPos chunkPos = new ChunkPos(pos);
        return ClaimTagSyncHandler.hasPermission(player, chunkPos, ClaimTagSyncHandler.PERMISSION_BUILD);
    }

    /**
     * Validates if a player can interact with a block at a specific position.
     * This is called by other systems that need to check interaction permissions.
     */
    public static boolean canInteractWithBlock(ServerPlayerEntity player, World world, BlockPos pos, BlockState state) {
        ChunkPos chunkPos = new ChunkPos(pos);
        Block block = state.getBlock();
        int permissionType = getInstance().getPermissionTypeForBlock(block, state);
        return ClaimTagSyncHandler.hasPermission(player, chunkPos, permissionType);
    }

    /**
     * Validates if a player can interact with an entity at a specific position.
     * This is called by other systems that need to check entity interaction permissions.
     */
    public static boolean canInteractWithEntity(ServerPlayerEntity player, World world, Entity entity) {
        BlockPos entityPos = entity.getBlockPos();
        ChunkPos chunkPos = new ChunkPos(entityPos);
        int permissionType = getInstance().getPermissionTypeForEntity(entity);
        return ClaimTagSyncHandler.hasPermission(player, chunkPos, permissionType);
    }

    /**
     * Checks if a block is crop-related and should use crops permission (for breaking - excludes farmland).
     */
    private boolean isCropBlock(Block block) {
        return block instanceof CropBlock ||
               block instanceof StemBlock ||
               block instanceof AttachedStemBlock ||
               block instanceof ComposterBlock ||
               block instanceof SweetBerryBushBlock ||
               block instanceof CocoaBlock ||
               block instanceof NetherWartBlock ||
               block instanceof BambooBlock ||
               block instanceof SugarCaneBlock ||
               block instanceof CactusBlock ||
               block instanceof KelpBlock ||
               block instanceof SeaPickleBlock;
    }

    /**
     * Checks if an item is a crop seed that can be planted.
     */
    private boolean isCropSeedItem(net.minecraft.item.Item item) {
        return item == net.minecraft.item.Items.WHEAT_SEEDS ||
               item == net.minecraft.item.Items.CARROT ||
               item == net.minecraft.item.Items.POTATO ||
               item == net.minecraft.item.Items.BEETROOT_SEEDS ||
               item == net.minecraft.item.Items.PUMPKIN_SEEDS ||
               item == net.minecraft.item.Items.MELON_SEEDS ||
               item == net.minecraft.item.Items.NETHER_WART ||
               item == net.minecraft.item.Items.SWEET_BERRIES ||
               item == net.minecraft.item.Items.COCOA_BEANS;
    }
}
