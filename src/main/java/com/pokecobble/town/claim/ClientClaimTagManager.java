package com.pokecobble.town.claim;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.client.ClientTownManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.minecraft.client.MinecraftClient;
import net.minecraft.network.PacketByteBuf;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Client-side claim tag manager that handles claim tag data synchronization.
 * Follows the same pattern as ClientPermissionManager.
 */
@Environment(EnvType.CLIENT)
public class ClientClaimTagManager {
    private static final ClientClaimTagManager INSTANCE = new ClientClaimTagManager();
    
    // Cache for claim tag data
    private final Map<UUID, List<ClaimTag>> claimTagCache = new ConcurrentHashMap<>();

    // Track active claim tag screen users by town ID
    private final Map<UUID, Map<UUID, String>> activeClaimTagUsers = new ConcurrentHashMap<>();
    
    private ClientClaimTagManager() {
        // Private constructor for singleton
    }
    
    public static ClientClaimTagManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * Registers client-side network handlers for claim tag-related packets.
     * This should only be called on the client side.
     */
    public static void registerClientNetworkHandlers() {
        // Handler for claim tag data sync
        ClientPlayNetworking.registerGlobalReceiver(ClaimTagNetworkConstants.CLAIM_TAG_DATA_SYNC, (client, handler, buf, responseSender) -> {
            try {
                UUID townId = buf.readUuid();
                int tagCount = buf.readInt();
                
                List<ClaimTag> tags = new ArrayList<>();
                for (int i = 0; i < tagCount; i++) {
                    ClaimTag tag = readClaimTagFromBuffer(buf);
                    if (tag != null) {
                        tags.add(tag);
                    }
                }
                
                // Update client-side cache
                getInstance().claimTagCache.put(townId, tags);
                
                // Update the client-side Town object with new claim tags
                updateClientTownClaimTags(townId, tags);
                
                Pokecobbleclaim.LOGGER.info("Received claim tag data sync for town: " + townId + " with " + tags.size() + " tags");
                
                // Update claim tool if it's active
                updateClaimToolWithNewTags(tags);
                
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling claim tag data sync: " + e.getMessage(), e);
            }
        });
        
        // Handler for claim tag data response
        ClientPlayNetworking.registerGlobalReceiver(ClaimTagNetworkConstants.CLAIM_TAG_DATA_RESPONSE, (client, handler, buf, responseSender) -> {
            try {
                UUID townId = buf.readUuid();
                int tagCount = buf.readInt();

                List<ClaimTag> tags = new ArrayList<>();
                for (int i = 0; i < tagCount; i++) {
                    ClaimTag tag = readClaimTagFromBuffer(buf);
                    if (tag != null) {
                        tags.add(tag);
                    }
                }

                Pokecobbleclaim.LOGGER.info("CLAIM TAG RESPONSE: Received " + tags.size() + " tags for town " + townId);
                if (!tags.isEmpty()) {
                    ClaimTag firstTag = tags.get(0);
                    Pokecobbleclaim.LOGGER.info("CLAIM TAG RESPONSE: First tag name: '" + firstTag.getName() + "', color: " + firstTag.getColor());
                }

                // Update client-side cache
                getInstance().claimTagCache.put(townId, tags);

                // Update the client-side Town object with new claim tags
                updateClientTownClaimTags(townId, tags);

                Pokecobbleclaim.LOGGER.info("Received claim tag data response for town: " + townId + " with " + tags.size() + " tags");

                // Update claim tool if it's active
                updateClaimToolWithNewTags(tags);

                // Update ClaimTagScreen if it's open
                updateClaimTagScreenWithNewTags(tags);
                
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling claim tag data response: " + e.getMessage(), e);
            }
        });

        // Handler for active users sync
        ClientPlayNetworking.registerGlobalReceiver(ClaimTagNetworkConstants.CLAIM_TAG_ACTIVE_USERS_SYNC, (client, handler, buf, responseSender) -> {
            try {
                UUID townId = buf.readUuid();
                int userCount = buf.readInt();

                Map<UUID, String> users = new HashMap<>();
                for (int i = 0; i < userCount; i++) {
                    UUID userId = buf.readUuid();
                    String userName = buf.readString();
                    users.put(userId, userName);
                }

                // Update local cache
                getInstance().activeClaimTagUsers.put(townId, users);

                // Update ClaimTagScreen if it's open
                updateClaimTagScreenActiveUsers(townId, users);

                Pokecobbleclaim.LOGGER.debug("Received active users sync for town {}: {} users", townId, userCount);

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling active users sync: " + e.getMessage(), e);
            }
        });

        // Handler for real-time tag changes broadcast
        ClientPlayNetworking.registerGlobalReceiver(ClaimTagNetworkConstants.CLAIM_TAG_REALTIME_BROADCAST, (client, handler, buf, responseSender) -> {
            try {
                UUID townId = buf.readUuid();
                UUID senderId = buf.readUuid();
                String changeType = buf.readString();

                // Don't process our own changes
                if (client.player != null && senderId.equals(client.player.getUuid())) {
                    return;
                }

                // Process different types of changes
                switch (changeType) {
                    case "tag_selected":
                        String tagName = buf.readString();
                        updateClaimTagScreenSelection(townId, tagName);
                        break;
                    case "tag_name_changed":
                        String oldName = buf.readString();
                        String newName = buf.readString();
                        updateClaimTagScreenNameChange(townId, oldName, newName);
                        break;
                    case "tag_color_changed":
                        String colorTagName = buf.readString();
                        int newColor = Integer.parseInt(buf.readString());
                        updateClaimTagScreenColorChange(townId, colorTagName, newColor);
                        break;
                    case "permission_changed":
                        String permTagName = buf.readString();
                        String rankName = buf.readString();
                        int permissionIndex = Integer.parseInt(buf.readString());
                        boolean enabled = Boolean.parseBoolean(buf.readString());
                        updateClaimTagScreenPermissionChange(townId, permTagName, rankName, permissionIndex, enabled);
                        break;
                }

                Pokecobbleclaim.LOGGER.debug("Received real-time change: {} for town {}", changeType, townId);

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling real-time tag change: " + e.getMessage(), e);
            }
        });

        // Handler for temporary state response
        ClientPlayNetworking.registerGlobalReceiver(ClaimTagNetworkConstants.CLAIM_TAG_TEMP_STATE_RESPONSE, (client, handler, buf, responseSender) -> {
            try {
                UUID townId = buf.readUuid();
                int tagCount = buf.readInt();

                List<ClaimTag> tempTags = new ArrayList<>();
                for (int i = 0; i < tagCount; i++) {
                    ClaimTag tag = readClaimTagFromBuffer(buf);
                    tempTags.add(tag);
                }

                // Update local cache with temporary state
                getInstance().claimTagCache.put(townId, tempTags);

                // Update ClaimTagScreen if it's open
                updateClaimTagScreenWithTemporaryState(townId, tempTags);

                Pokecobbleclaim.LOGGER.info("Received temporary state with {} tags for town {}", tagCount, townId);

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling temporary state response: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * Sends a claim tag data request to the server.
     */
    public void requestClaimTagData(UUID townId) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                Pokecobbleclaim.LOGGER.error("Cannot request claim tag data: no player");
                return;
            }
            
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(client.player.getUuid());
            buf.writeUuid(townId);
            
            ClientPlayNetworking.send(ClaimTagNetworkConstants.CLAIM_TAG_DATA_REQUEST, buf);
            
            Pokecobbleclaim.LOGGER.debug("Sent claim tag data request for town: " + townId);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending claim tag data request: " + e.getMessage(), e);
        }
    }
    
    /**
     * Sends claim tag updates to the server.
     */
    public void sendClaimTagUpdate(UUID townId, List<ClaimTag> tags) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                Pokecobbleclaim.LOGGER.error("Cannot send claim tag update: no player");
                return;
            }
            
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(client.player.getUuid());
            buf.writeUuid(townId);
            buf.writeInt(tags.size());
            
            for (ClaimTag tag : tags) {
                writeClaimTagToBuffer(buf, tag);
            }
            
            ClientPlayNetworking.send(ClaimTagNetworkConstants.CLAIM_TAG_UPDATE, buf);
            
            Pokecobbleclaim.LOGGER.debug("Sent claim tag update for town: " + townId + " with " + tags.size() + " tags");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending claim tag update: " + e.getMessage(), e);
        }
    }
    
    /**
     * Gets cached claim tags for a town.
     */
    public List<ClaimTag> getCachedClaimTags(UUID townId) {
        List<ClaimTag> cached = claimTagCache.get(townId);
        return cached != null ? new ArrayList<>(cached) : new ArrayList<>();
    }
    
    /**
     * Reads a ClaimTag from a packet buffer.
     * Enhanced with buffer underrun protection for backward compatibility.
     */
    private static ClaimTag readClaimTagFromBuffer(PacketByteBuf buf) {
        try {
            int initialBytes = buf.readableBytes();
            Pokecobbleclaim.LOGGER.debug("CLIENT: Starting to read claim tag, buffer readable bytes: " + initialBytes);

            // Calculate expected minimum size
            // UUID (16) + 2 strings (variable) + int (4) + permissions (6 ranks * 8 + 8 non-member = 56 booleans)
            int expectedPermissionBytes = (com.pokecobble.town.TownPlayerRank.values().length * 8) + 8;
            Pokecobbleclaim.LOGGER.debug("CLIENT: Expected permission bytes: " + expectedPermissionBytes);

            if (buf.readableBytes() < 20) { // Minimum for UUID + int
                Pokecobbleclaim.LOGGER.error("CLIENT: Buffer too small to contain basic claim tag data. Available: " + buf.readableBytes() + " bytes");
                return null;
            }

            UUID id = buf.readUuid();
            Pokecobbleclaim.LOGGER.debug("CLIENT: Read tag UUID: " + id + ", remaining bytes: " + buf.readableBytes());

            String name = buf.readString(com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
            Pokecobbleclaim.LOGGER.debug("CLIENT: Read tag name: '" + name + "' (length: " + name.length() + "), remaining bytes: " + buf.readableBytes());

            String description = buf.readString(com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
            Pokecobbleclaim.LOGGER.debug("CLIENT: Read tag description: '" + description + "' (length: " + description.length() + "), remaining bytes: " + buf.readableBytes());

            int color = buf.readInt();
            Pokecobbleclaim.LOGGER.debug("CLIENT: Read tag color: " + color + ", remaining bytes: " + buf.readableBytes());

            // Check if we have enough bytes for permissions
            if (buf.readableBytes() < expectedPermissionBytes) {
                Pokecobbleclaim.LOGGER.error("CLIENT: Not enough bytes for permissions. Expected: " + expectedPermissionBytes + ", available: " + buf.readableBytes());
                Pokecobbleclaim.LOGGER.error("CLIENT: This suggests a format mismatch - data may have been written in v2 efficient format but being read in standard format");
                // Try to create a tag with default permissions
                return new ClaimTag(id, name, description, color);
            }

            // Create tag with preserved UUID
            ClaimTag tag = new ClaimTag(id, name, description, color);

            // Read rank permissions
            int rankCount = com.pokecobble.town.TownPlayerRank.values().length;
            Pokecobbleclaim.LOGGER.debug("CLIENT: About to read permissions for " + rankCount + " ranks, remaining bytes: " + buf.readableBytes());

            for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
                Pokecobbleclaim.LOGGER.debug("CLIENT: Reading permissions for rank " + rank + ", remaining bytes: " + buf.readableBytes());
                for (int i = 0; i < 8; i++) {
                    if (buf.readableBytes() < 1) {
                        Pokecobbleclaim.LOGGER.warn("CLIENT: Buffer underrun while reading rank permissions for tag: " + name + " at rank " + rank + " permission " + i + ". Using default permissions.");
                        return tag; // Return tag with default permissions
                    }
                    boolean permission = buf.readBoolean();
                    tag.getRankPermissions().setPermission(rank, i, permission);
                }
            }

            // Read non-member permissions (with backward compatibility check)
            Pokecobbleclaim.LOGGER.debug("CLIENT: About to read non-member permissions, remaining bytes: " + buf.readableBytes());
            for (int i = 0; i < 8; i++) {
                if (buf.readableBytes() < 1) {
                    Pokecobbleclaim.LOGGER.warn("CLIENT: Buffer underrun while reading non-member permissions for tag: " + name + " at permission " + i + ". Using default permissions. Remaining bytes: " + buf.readableBytes());
                    return tag; // Return tag with default non-member permissions
                }
                boolean permission = buf.readBoolean();
                tag.getRankPermissions().setPermission(null, i, permission);
            }

            Pokecobbleclaim.LOGGER.debug("CLIENT: Successfully read claim tag: " + name + ", final remaining bytes: " + buf.readableBytes());
            return tag;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error reading claim tag from buffer: " + e.getMessage() + ", remaining bytes: " + buf.readableBytes(), e);
            // Instead of throwing, return null to prevent crashes
            return null;
        }
    }
    
    /**
     * Writes a ClaimTag to a packet buffer.
     */
    private static void writeClaimTagToBuffer(PacketByteBuf buf, ClaimTag tag) {
        buf.writeUuid(tag.getId());
        buf.writeString(tag.getName(), com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
        buf.writeString(tag.getDescription(), com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
        buf.writeInt(tag.getColor());
        
        // Write rank permissions
        for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
            boolean[] permissions = tag.getRankPermissions().getPermissions(rank);
            for (boolean permission : permissions) {
                buf.writeBoolean(permission);
            }
        }
        
        // Write non-member permissions
        boolean[] nonMemberPermissions = tag.getRankPermissions().getPermissions(null);
        for (boolean permission : nonMemberPermissions) {
            buf.writeBoolean(permission);
        }
    }
    
    /**
     * Updates the client-side Town object with new claim tags.
     */
    private static void updateClientTownClaimTags(UUID townId, List<ClaimTag> tags) {
        try {
            Town town = ClientTownManager.getInstance().getTown(townId);
            if (town != null) {
                town.updateClaimTags(tags);
                Pokecobbleclaim.LOGGER.debug("Updated client town " + townId + " with " + tags.size() + " claim tags");
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating client town claim tags: " + e.getMessage());
        }
    }
    
    /**
     * Updates the claim tool with new tags if it's currently active.
     */
    private static void updateClaimToolWithNewTags(List<ClaimTag> newTags) {
        try {
            ClaimTool claimTool = ClaimTool.getInstance();
            if (claimTool != null && claimTool.isActive()) {
                claimTool.updateTags(newTags);
                Pokecobbleclaim.LOGGER.debug("Updated active claim tool with " + newTags.size() + " new tags");
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating claim tool with new tags: " + e.getMessage());
        }
    }
    
    /**
     * Updates the ClaimTagScreen with new tags if it's currently open.
     */
    private static void updateClaimTagScreenWithNewTags(List<ClaimTag> newTags) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.currentScreen instanceof com.pokecobble.town.gui.ClaimTagScreen) {
                com.pokecobble.town.gui.ClaimTagScreen screen = (com.pokecobble.town.gui.ClaimTagScreen) client.currentScreen;
                screen.updateTagsFromServer(newTags);
                Pokecobbleclaim.LOGGER.debug("Updated ClaimTagScreen with " + newTags.size() + " new tags from server");
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating ClaimTagScreen with new tags: " + e.getMessage());
        }
    }

    /**
     * Updates the ClaimTagScreen with active users if it's currently open.
     */
    private static void updateClaimTagScreenActiveUsers(UUID townId, Map<UUID, String> activeUsers) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.currentScreen instanceof com.pokecobble.town.gui.ClaimTagScreen) {
                com.pokecobble.town.gui.ClaimTagScreen screen = (com.pokecobble.town.gui.ClaimTagScreen) client.currentScreen;
                screen.updateActiveUsers(activeUsers);
                Pokecobbleclaim.LOGGER.debug("Updated ClaimTagScreen with {} active users", activeUsers.size());
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating ClaimTagScreen with active users: " + e.getMessage());
        }
    }

    /**
     * Gets the active users for a town.
     */
    public Map<UUID, String> getActiveUsers(UUID townId) {
        return activeClaimTagUsers.getOrDefault(townId, Collections.emptyMap());
    }

    /**
     * Sends screen opened notification to server.
     */
    public void notifyScreenOpened(UUID townId) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                Pokecobbleclaim.LOGGER.error("Cannot notify screen opened: no player");
                return;
            }

            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(client.player.getUuid());
            buf.writeUuid(townId);

            ClientPlayNetworking.send(ClaimTagNetworkConstants.CLAIM_TAG_SCREEN_OPENED, buf);

            Pokecobbleclaim.LOGGER.debug("Notified server that claim tag screen opened for town: " + townId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error notifying screen opened: " + e.getMessage(), e);
        }
    }

    /**
     * Sends screen closed notification to server.
     */
    public void notifyScreenClosed(UUID townId) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                Pokecobbleclaim.LOGGER.error("Cannot notify screen closed: no player");
                return;
            }

            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(client.player.getUuid());
            buf.writeUuid(townId);

            ClientPlayNetworking.send(ClaimTagNetworkConstants.CLAIM_TAG_SCREEN_CLOSED, buf);

            Pokecobbleclaim.LOGGER.debug("Notified server that claim tag screen closed for town: " + townId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error notifying screen closed: " + e.getMessage(), e);
        }
    }

    /**
     * Sends a real-time change notification to the server.
     */
    public void sendRealtimeChange(UUID townId, String changeType, String... data) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                Pokecobbleclaim.LOGGER.error("Cannot send real-time change: no player");
                return;
            }

            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(client.player.getUuid());
            buf.writeUuid(townId);
            buf.writeString(changeType);

            // Write additional data
            for (String datum : data) {
                buf.writeString(datum);
            }

            ClientPlayNetworking.send(ClaimTagNetworkConstants.CLAIM_TAG_REALTIME_CHANGE, buf);

            Pokecobbleclaim.LOGGER.debug("Sent real-time change: {} for town: {}", changeType, townId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending real-time change: " + e.getMessage(), e);
        }
    }

    /**
     * Updates the ClaimTagScreen with tag selection change.
     */
    private static void updateClaimTagScreenSelection(UUID townId, String tagName) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.currentScreen instanceof com.pokecobble.town.gui.ClaimTagScreen) {
                com.pokecobble.town.gui.ClaimTagScreen screen = (com.pokecobble.town.gui.ClaimTagScreen) client.currentScreen;
                screen.updateSelectedTagFromRemote(tagName);
                Pokecobbleclaim.LOGGER.debug("Updated ClaimTagScreen selection to: {}", tagName);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating ClaimTagScreen selection: " + e.getMessage());
        }
    }

    /**
     * Updates the ClaimTagScreen with tag name change.
     */
    private static void updateClaimTagScreenNameChange(UUID townId, String oldName, String newName) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.currentScreen instanceof com.pokecobble.town.gui.ClaimTagScreen) {
                com.pokecobble.town.gui.ClaimTagScreen screen = (com.pokecobble.town.gui.ClaimTagScreen) client.currentScreen;
                screen.updateTagNameFromRemote(oldName, newName);
                Pokecobbleclaim.LOGGER.debug("Updated ClaimTagScreen tag name from {} to {}", oldName, newName);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating ClaimTagScreen tag name: " + e.getMessage());
        }
    }

    /**
     * Updates the ClaimTagScreen with tag color change.
     */
    private static void updateClaimTagScreenColorChange(UUID townId, String tagName, int newColor) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.currentScreen instanceof com.pokecobble.town.gui.ClaimTagScreen) {
                com.pokecobble.town.gui.ClaimTagScreen screen = (com.pokecobble.town.gui.ClaimTagScreen) client.currentScreen;
                screen.updateTagColorFromRemote(tagName, newColor);
                Pokecobbleclaim.LOGGER.debug("Updated ClaimTagScreen tag color for {} to {}", tagName, newColor);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating ClaimTagScreen tag color: " + e.getMessage());
        }
    }

    /**
     * Updates the ClaimTagScreen with permission change.
     */
    private static void updateClaimTagScreenPermissionChange(UUID townId, String tagName, String rankName, int permissionIndex, boolean enabled) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.currentScreen instanceof com.pokecobble.town.gui.ClaimTagScreen) {
                com.pokecobble.town.gui.ClaimTagScreen screen = (com.pokecobble.town.gui.ClaimTagScreen) client.currentScreen;
                screen.updatePermissionFromRemote(tagName, rankName, permissionIndex, enabled);
                Pokecobbleclaim.LOGGER.debug("Updated ClaimTagScreen permission for {} rank {} permission {} to {}", tagName, rankName, permissionIndex, enabled);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating ClaimTagScreen permission: " + e.getMessage());
        }
    }

    /**
     * Updates the ClaimTagScreen with temporary state.
     */
    private static void updateClaimTagScreenWithTemporaryState(UUID townId, List<ClaimTag> tempTags) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.currentScreen instanceof com.pokecobble.town.gui.ClaimTagScreen) {
                com.pokecobble.town.gui.ClaimTagScreen screen = (com.pokecobble.town.gui.ClaimTagScreen) client.currentScreen;
                screen.updateTagsFromServer(tempTags);
                Pokecobbleclaim.LOGGER.info("Updated ClaimTagScreen with temporary state containing {} tags", tempTags.size());
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating ClaimTagScreen with temporary state: " + e.getMessage());
        }
    }

    /**
     * Requests temporary state from server when opening the screen.
     */
    public void requestTemporaryState(UUID townId) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                Pokecobbleclaim.LOGGER.error("Cannot request temporary state: no player");
                return;
            }

            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(client.player.getUuid());
            buf.writeUuid(townId);

            ClientPlayNetworking.send(ClaimTagNetworkConstants.CLAIM_TAG_TEMP_STATE_REQUEST, buf);

            Pokecobbleclaim.LOGGER.debug("Requested temporary state for town: " + townId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting temporary state: " + e.getMessage(), e);
        }
    }

    /**
     * Sends complete temporary state to server when changes are made.
     */
    public void sendTemporaryStateUpdate(UUID townId, List<ClaimTag> currentTags) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                Pokecobbleclaim.LOGGER.error("Cannot send temporary state: no player");
                return;
            }

            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(client.player.getUuid());
            buf.writeUuid(townId);
            buf.writeInt(currentTags.size());

            // Write all tags to the packet
            for (ClaimTag tag : currentTags) {
                writeClaimTagToBuffer(buf, tag);
            }

            ClientPlayNetworking.send(ClaimTagNetworkConstants.CLAIM_TAG_TEMP_STATE_UPDATE, buf);

            Pokecobbleclaim.LOGGER.debug("Sent temporary state update with {} tags for town: {}", currentTags.size(), townId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending temporary state update: " + e.getMessage(), e);
        }
    }

}
