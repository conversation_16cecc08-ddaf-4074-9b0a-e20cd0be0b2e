package com.pokecobble.town.claim;

import java.util.ArrayList;
import java.util.List;

/**
 * Serializable representation of claim history data for a town.
 * Used for JSON serialization/deserialization in the file-based persistence system.
 * Follows the same pattern as other serializable data classes in the mod.
 */
public class SerializableClaimHistoryData {
    public String townId;
    public List<SerializableClaimHistoryEntry> entries;
    public long lastModified;
    public int version;
    
    public SerializableClaimHistoryData() {
        this.entries = new ArrayList<>();
        this.lastModified = System.currentTimeMillis();
        this.version = 1;
    }
}
