package com.pokecobble.town.bank;

import com.pokecobble.Pokecobbleclaim;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.TimeUnit;
import java.util.Set;
import java.util.concurrent.ConcurrentSkipListSet;

/**
 * Manages thread-safe transactions for town banks to prevent race conditions,
 * money duplication, and ensure atomic operations.
 */
public class TownBankTransactionManager {
    private static TownBankTransactionManager instance;
    
    // Lock per town to prevent concurrent transactions on the same town
    private final ConcurrentHashMap<UUID, ReentrantLock> townLocks = new ConcurrentHashMap<>();
    
    // Track active transactions to prevent duplicates
    private final Set<String> activeTransactions = new ConcurrentSkipListSet<>();
    
    // Transaction timeout in seconds
    private static final int TRANSACTION_TIMEOUT_SECONDS = 30;
    
    private TownBankTransactionManager() {}
    
    public static TownBankTransactionManager getInstance() {
        if (instance == null) {
            synchronized (TownBankTransactionManager.class) {
                if (instance == null) {
                    instance = new TownBankTransactionManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * Executes a transaction with proper locking and safety measures.
     * 
     * @param townId The town ID
     * @param playerId The player ID
     * @param transactionId Unique transaction ID to prevent duplicates
     * @param transaction The transaction to execute
     * @return The result of the transaction
     */
    public <T> T executeTransaction(UUID townId, UUID playerId, String transactionId, 
                                   TransactionCallback<T> transaction) {
        // Check for duplicate transaction
        if (!activeTransactions.add(transactionId)) {
            Pokecobbleclaim.LOGGER.warn("Duplicate transaction attempt: {}", transactionId);
            return transaction.onDuplicate();
        }
        
        ReentrantLock townLock = townLocks.computeIfAbsent(townId, k -> new ReentrantLock(true));
        
        try {
            // Try to acquire lock with timeout to prevent deadlocks
            if (!townLock.tryLock(TRANSACTION_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                Pokecobbleclaim.LOGGER.error("Transaction timeout for town {} player {}: {}", 
                    townId, playerId, transactionId);
                return transaction.onTimeout();
            }
            
            try {
                // Execute the transaction within the lock
                Pokecobbleclaim.LOGGER.debug("Executing transaction: {} for town {} player {}", 
                    transactionId, townId, playerId);
                return transaction.execute();
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Transaction failed: {} for town {} player {}", 
                    transactionId, townId, playerId, e);
                return transaction.onError(e);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            Pokecobbleclaim.LOGGER.error("Transaction interrupted: {} for town {} player {}", 
                transactionId, townId, playerId, e);
            return transaction.onInterrupted();
        } finally {
            if (townLock.isHeldByCurrentThread()) {
                townLock.unlock();
            }
            // Remove from active transactions
            activeTransactions.remove(transactionId);
        }
    }
    
    /**
     * Generates a unique transaction ID.
     */
    public String generateTransactionId(UUID townId, UUID playerId, String operation, long amount) {
        return String.format("%s_%s_%s_%d_%d", 
            operation, townId.toString().substring(0, 8), 
            playerId.toString().substring(0, 8), amount, System.currentTimeMillis());
    }
    
    /**
     * Checks if a transaction is currently active.
     */
    public boolean isTransactionActive(String transactionId) {
        return activeTransactions.contains(transactionId);
    }
    
    /**
     * Gets the number of active transactions.
     */
    public int getActiveTransactionCount() {
        return activeTransactions.size();
    }
    
    /**
     * Cleans up old locks for towns that no longer exist.
     */
    public void cleanupOldLocks() {
        // This could be called periodically to clean up locks for deleted towns
        // For now, we'll keep all locks as towns are rarely deleted
    }
    
    /**
     * Callback interface for transactions.
     */
    public interface TransactionCallback<T> {
        T execute() throws Exception;
        T onError(Exception e);
        T onTimeout();
        T onDuplicate();
        T onInterrupted();
    }
    
    /**
     * Atomic transaction result wrapper.
     */
    public static class AtomicTransactionResult<T> {
        private final boolean success;
        private final T result;
        private final String errorMessage;
        private final Exception exception;
        
        private AtomicTransactionResult(boolean success, T result, String errorMessage, Exception exception) {
            this.success = success;
            this.result = result;
            this.errorMessage = errorMessage;
            this.exception = exception;
        }
        
        public static <T> AtomicTransactionResult<T> success(T result) {
            return new AtomicTransactionResult<>(true, result, null, null);
        }
        
        public static <T> AtomicTransactionResult<T> failure(String errorMessage) {
            return new AtomicTransactionResult<>(false, null, errorMessage, null);
        }
        
        public static <T> AtomicTransactionResult<T> failure(String errorMessage, Exception exception) {
            return new AtomicTransactionResult<>(false, null, errorMessage, exception);
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public T getResult() {
            return result;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public Exception getException() {
            return exception;
        }
    }
    
    /**
     * Transaction state for rollback purposes.
     */
    public static class TransactionState {
        private final long playerBalanceBefore;
        private final long townBankBalanceBefore;
        private final UUID playerId;
        private final UUID townId;
        private final String operation;
        private final long amount;
        private final long timestamp;
        
        public TransactionState(long playerBalanceBefore, long townBankBalanceBefore, 
                               UUID playerId, UUID townId, String operation, long amount) {
            this.playerBalanceBefore = playerBalanceBefore;
            this.townBankBalanceBefore = townBankBalanceBefore;
            this.playerId = playerId;
            this.townId = townId;
            this.operation = operation;
            this.amount = amount;
            this.timestamp = System.currentTimeMillis();
        }
        
        // Getters
        public long getPlayerBalanceBefore() { return playerBalanceBefore; }
        public long getTownBankBalanceBefore() { return townBankBalanceBefore; }
        public UUID getPlayerId() { return playerId; }
        public UUID getTownId() { return townId; }
        public String getOperation() { return operation; }
        public long getAmount() { return amount; }
        public long getTimestamp() { return timestamp; }
    }
}
