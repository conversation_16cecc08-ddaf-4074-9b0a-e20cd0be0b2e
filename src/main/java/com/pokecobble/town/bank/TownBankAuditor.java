package com.pokecobble.town.bank;

import com.pokecobble.Pokecobbleclaim;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.List;
import java.util.Date;

/**
 * Comprehensive audit system for town bank transactions.
 * Tracks all operations, detects anomalies, and provides rollback capabilities.
 */
public class TownBankAuditor {
    private static TownBankAuditor instance;
    
    // Audit log for all transactions
    private final ConcurrentLinkedQueue<AuditEntry> auditLog = new ConcurrentLinkedQueue<>();
    
    // Balance snapshots for verification
    private final ConcurrentHashMap<UUID, BalanceSnapshot> lastKnownBalances = new ConcurrentHashMap<>();
    
    // Maximum audit log size
    private static final int MAX_AUDIT_LOG_SIZE = 10000;
    
    // Anomaly detection thresholds
    private static final long LARGE_TRANSACTION_THRESHOLD = 100000L; // 100k coins
    private static final int RAPID_TRANSACTION_THRESHOLD = 5; // 5 transactions in 1 minute
    
    private TownBankAuditor() {}
    
    public static TownBankAuditor getInstance() {
        if (instance == null) {
            synchronized (TownBankAuditor.class) {
                if (instance == null) {
                    instance = new TownBankAuditor();
                }
            }
        }
        return instance;
    }
    
    /**
     * Records a transaction attempt (before execution).
     */
    public void recordTransactionAttempt(UUID townId, UUID playerId, String operation, 
                                       long amount, String description, String transactionId) {
        AuditEntry entry = new AuditEntry(
            AuditEntry.Type.TRANSACTION_ATTEMPT,
            townId, playerId, operation, amount, description, transactionId,
            System.currentTimeMillis(), null, null
        );
        
        addAuditEntry(entry);
        Pokecobbleclaim.LOGGER.debug("Audit: Transaction attempt - {} {} {} coins for town {}", 
            playerId, operation, amount, townId);
    }
    
    /**
     * Records a successful transaction.
     */
    public void recordTransactionSuccess(UUID townId, UUID playerId, String operation, 
                                       long amount, String description, String transactionId,
                                       long balanceBefore, long balanceAfter) {
        AuditEntry entry = new AuditEntry(
            AuditEntry.Type.TRANSACTION_SUCCESS,
            townId, playerId, operation, amount, description, transactionId,
            System.currentTimeMillis(), balanceBefore, balanceAfter
        );
        
        addAuditEntry(entry);
        
        // Update balance snapshot
        updateBalanceSnapshot(townId, balanceAfter);
        
        // Check for anomalies
        checkForAnomalies(entry);
        
        Pokecobbleclaim.LOGGER.info("Audit: Transaction success - {} {} {} coins for town {} (balance: {} -> {})", 
            playerId, operation, amount, townId, balanceBefore, balanceAfter);
    }
    
    /**
     * Records a failed transaction.
     */
    public void recordTransactionFailure(UUID townId, UUID playerId, String operation, 
                                       long amount, String description, String transactionId,
                                       String failureReason) {
        AuditEntry entry = new AuditEntry(
            AuditEntry.Type.TRANSACTION_FAILURE,
            townId, playerId, operation, amount, description + " [FAILED: " + failureReason + "]", 
            transactionId, System.currentTimeMillis(), null, null
        );
        
        addAuditEntry(entry);
        Pokecobbleclaim.LOGGER.warn("Audit: Transaction failure - {} {} {} coins for town {} - Reason: {}", 
            playerId, operation, amount, townId, failureReason);
    }
    
    /**
     * Records a balance verification.
     */
    public void recordBalanceVerification(UUID townId, long expectedBalance, long actualBalance) {
        if (expectedBalance != actualBalance) {
            AuditEntry entry = new AuditEntry(
                AuditEntry.Type.BALANCE_MISMATCH,
                townId, null, "BALANCE_CHECK", 0, 
                String.format("Expected: %d, Actual: %d, Difference: %d", 
                    expectedBalance, actualBalance, actualBalance - expectedBalance),
                "BALANCE_CHECK_" + System.currentTimeMillis(),
                System.currentTimeMillis(), expectedBalance, actualBalance
            );
            
            addAuditEntry(entry);
            Pokecobbleclaim.LOGGER.error("CRITICAL: Balance mismatch detected for town {} - Expected: {}, Actual: {}", 
                townId, expectedBalance, actualBalance);
        }
    }
    
    /**
     * Verifies the integrity of a town's bank balance.
     */
    public boolean verifyBankIntegrity(UUID townId) {
        TownBank bank = TownBankManager.getInstance().getTownBank(townId);
        List<TownBankTransaction> transactions = bank.getTransactionHistory();
        
        // Calculate expected balance from transaction history
        long calculatedBalance = 0;
        for (TownBankTransaction transaction : transactions) {
            calculatedBalance += transaction.getBalanceChange();
        }
        
        long actualBalance = bank.getBalance();
        
        if (calculatedBalance != actualBalance) {
            recordBalanceVerification(townId, calculatedBalance, actualBalance);
            return false;
        }
        
        return true;
    }
    
    /**
     * Checks for transaction anomalies.
     */
    private void checkForAnomalies(AuditEntry entry) {
        // Check for large transactions
        if (entry.getAmount() >= LARGE_TRANSACTION_THRESHOLD) {
            Pokecobbleclaim.LOGGER.warn("Large transaction detected: {} {} {} coins for town {}", 
                entry.getPlayerId(), entry.getOperation(), entry.getAmount(), entry.getTownId());
        }
        
        // Check for rapid transactions
        checkRapidTransactions(entry);
        
        // Check for impossible balance changes
        if (entry.getBalanceBefore() != null && entry.getBalanceAfter() != null) {
            long expectedChange = entry.getOperation().equals("DEPOSIT") ? entry.getAmount() : -entry.getAmount();
            long actualChange = entry.getBalanceAfter() - entry.getBalanceBefore();
            
            if (expectedChange != actualChange) {
                Pokecobbleclaim.LOGGER.error("CRITICAL: Balance change mismatch - Expected: {}, Actual: {} for transaction {}", 
                    expectedChange, actualChange, entry.getTransactionId());
            }
        }
    }
    
    /**
     * Checks for rapid transactions from the same player.
     */
    private void checkRapidTransactions(AuditEntry entry) {
        long oneMinuteAgo = System.currentTimeMillis() - 60000; // 1 minute ago
        
        long recentTransactions = auditLog.stream()
            .filter(e -> e.getType() == AuditEntry.Type.TRANSACTION_SUCCESS)
            .filter(e -> e.getPlayerId() != null && e.getPlayerId().equals(entry.getPlayerId()))
            .filter(e -> e.getTimestamp() >= oneMinuteAgo)
            .count();
        
        if (recentTransactions >= RAPID_TRANSACTION_THRESHOLD) {
            Pokecobbleclaim.LOGGER.warn("Rapid transactions detected: {} has made {} transactions in the last minute", 
                entry.getPlayerId(), recentTransactions);
        }
    }
    
    /**
     * Updates the balance snapshot for a town.
     */
    private void updateBalanceSnapshot(UUID townId, long balance) {
        lastKnownBalances.put(townId, new BalanceSnapshot(balance, System.currentTimeMillis()));
    }
    
    /**
     * Adds an audit entry and manages log size.
     */
    private void addAuditEntry(AuditEntry entry) {
        auditLog.offer(entry);
        
        // Trim log if it gets too large
        while (auditLog.size() > MAX_AUDIT_LOG_SIZE) {
            auditLog.poll();
        }
    }
    
    /**
     * Gets recent audit entries for a town.
     */
    public List<AuditEntry> getRecentAuditEntries(UUID townId, int limit) {
        return auditLog.stream()
            .filter(entry -> entry.getTownId().equals(townId))
            .sorted((a, b) -> Long.compare(b.getTimestamp(), a.getTimestamp()))
            .limit(limit)
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * Gets all failed transactions for a town.
     */
    public List<AuditEntry> getFailedTransactions(UUID townId) {
        return auditLog.stream()
            .filter(entry -> entry.getTownId().equals(townId))
            .filter(entry -> entry.getType() == AuditEntry.Type.TRANSACTION_FAILURE)
            .sorted((a, b) -> Long.compare(b.getTimestamp(), a.getTimestamp()))
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * Gets audit statistics.
     */
    public AuditStatistics getAuditStatistics() {
        long totalTransactions = auditLog.stream()
            .filter(entry -> entry.getType() == AuditEntry.Type.TRANSACTION_SUCCESS)
            .count();
        
        long failedTransactions = auditLog.stream()
            .filter(entry -> entry.getType() == AuditEntry.Type.TRANSACTION_FAILURE)
            .count();
        
        long balanceMismatches = auditLog.stream()
            .filter(entry -> entry.getType() == AuditEntry.Type.BALANCE_MISMATCH)
            .count();
        
        return new AuditStatistics(totalTransactions, failedTransactions, balanceMismatches);
    }
    
    /**
     * Audit entry class.
     */
    public static class AuditEntry {
        public enum Type {
            TRANSACTION_ATTEMPT,
            TRANSACTION_SUCCESS,
            TRANSACTION_FAILURE,
            BALANCE_MISMATCH
        }
        
        private final Type type;
        private final UUID townId;
        private final UUID playerId;
        private final String operation;
        private final long amount;
        private final String description;
        private final String transactionId;
        private final long timestamp;
        private final Long balanceBefore;
        private final Long balanceAfter;
        
        public AuditEntry(Type type, UUID townId, UUID playerId, String operation, long amount,
                         String description, String transactionId, long timestamp,
                         Long balanceBefore, Long balanceAfter) {
            this.type = type;
            this.townId = townId;
            this.playerId = playerId;
            this.operation = operation;
            this.amount = amount;
            this.description = description;
            this.transactionId = transactionId;
            this.timestamp = timestamp;
            this.balanceBefore = balanceBefore;
            this.balanceAfter = balanceAfter;
        }
        
        // Getters
        public Type getType() { return type; }
        public UUID getTownId() { return townId; }
        public UUID getPlayerId() { return playerId; }
        public String getOperation() { return operation; }
        public long getAmount() { return amount; }
        public String getDescription() { return description; }
        public String getTransactionId() { return transactionId; }
        public long getTimestamp() { return timestamp; }
        public Long getBalanceBefore() { return balanceBefore; }
        public Long getBalanceAfter() { return balanceAfter; }
        public Date getDate() { return new Date(timestamp); }
    }
    
    /**
     * Balance snapshot class.
     */
    private static class BalanceSnapshot {
        private final long balance;
        private final long timestamp;
        
        public BalanceSnapshot(long balance, long timestamp) {
            this.balance = balance;
            this.timestamp = timestamp;
        }
        
        public long getBalance() { return balance; }
        public long getTimestamp() { return timestamp; }
    }
    
    /**
     * Audit statistics class.
     */
    public static class AuditStatistics {
        private final long totalTransactions;
        private final long failedTransactions;
        private final long balanceMismatches;
        
        public AuditStatistics(long totalTransactions, long failedTransactions, long balanceMismatches) {
            this.totalTransactions = totalTransactions;
            this.failedTransactions = failedTransactions;
            this.balanceMismatches = balanceMismatches;
        }
        
        public long getTotalTransactions() { return totalTransactions; }
        public long getFailedTransactions() { return failedTransactions; }
        public long getBalanceMismatches() { return balanceMismatches; }
        public double getFailureRate() { 
            return totalTransactions > 0 ? (double) failedTransactions / totalTransactions : 0.0; 
        }
    }
}
