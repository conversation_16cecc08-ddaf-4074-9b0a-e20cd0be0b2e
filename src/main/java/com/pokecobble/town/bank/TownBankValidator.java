package com.pokecobble.town.bank;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Comprehensive validation and security system for town bank operations.
 * Prevents exploits, validates inputs, and ensures transaction integrity.
 */
public class TownBankValidator {
    
    // Security limits
    private static final long MAX_TRANSACTION_AMOUNT = 1_000_000_000L; // 1 billion max
    private static final long MIN_TRANSACTION_AMOUNT = 1L;
    private static final int MAX_DESCRIPTION_LENGTH = 200;
    private static final long MAX_BANK_BALANCE = 10_000_000_000L; // 10 billion max
    

    
    // Pattern for valid descriptions (alphanumeric, spaces, basic punctuation)
    private static final Pattern VALID_DESCRIPTION_PATTERN = 
        Pattern.compile("^[a-zA-Z0-9\\s.,!?\\-_()]+$");
    
    /**
     * Validates a deposit transaction.
     */
    public static ValidationResult validateDeposit(UUID townId, UUID playerId, long amount, String description) {
        // Basic input validation
        ValidationResult basicValidation = validateBasicInputs(townId, playerId, amount, description);
        if (!basicValidation.isValid()) {
            return basicValidation;
        }
        
        // Check if town exists and player is a member
        ValidationResult membershipValidation = validateTownMembership(townId, playerId);
        if (!membershipValidation.isValid()) {
            return membershipValidation;
        }
        
        // Check deposit permissions
        ValidationResult permissionValidation = validateDepositPermission(townId, playerId);
        if (!permissionValidation.isValid()) {
            return permissionValidation;
        }
        
        // Check if deposit would exceed bank limits
        TownBank bank = TownBankManager.getInstance().getTownBank(townId);
        if (bank.getBalance() + amount > MAX_BANK_BALANCE) {
            return ValidationResult.invalid("Deposit would exceed maximum bank balance limit");
        }
        
        // Check if bank allows deposits
        if (!bank.isDepositsAllowed()) {
            return ValidationResult.invalid("Deposits are currently disabled for this town");
        }
        
        return ValidationResult.valid();
    }
    
    /**
     * Validates a withdrawal transaction.
     */
    public static ValidationResult validateWithdrawal(UUID townId, UUID playerId, long amount, String description) {
        // Basic input validation
        ValidationResult basicValidation = validateBasicInputs(townId, playerId, amount, description);
        if (!basicValidation.isValid()) {
            return basicValidation;
        }
        
        // Check if town exists and player is a member
        ValidationResult membershipValidation = validateTownMembership(townId, playerId);
        if (!membershipValidation.isValid()) {
            return membershipValidation;
        }
        
        // Check withdrawal permissions
        ValidationResult permissionValidation = validateWithdrawalPermission(townId, playerId);
        if (!permissionValidation.isValid()) {
            return permissionValidation;
        }
        
        // Check if bank has sufficient funds
        TownBank bank = TownBankManager.getInstance().getTownBank(townId);
        if (bank.getBalance() < amount) {
            return ValidationResult.invalid("Insufficient funds in town bank");
        }
        
        // Check if bank allows withdrawals
        if (!bank.isWithdrawalsAllowed()) {
            return ValidationResult.invalid("Withdrawals are currently disabled for this town");
        }
        
        // Check daily withdrawal limit
        long dailyWithdrawals = getDailyWithdrawals(bank, playerId);
        if (dailyWithdrawals + amount > bank.getDailyWithdrawLimit()) {
            return ValidationResult.invalid("Daily withdrawal limit exceeded");
        }
        
        return ValidationResult.valid();
    }
    
    /**
     * Validates basic inputs common to all transactions.
     */
    private static ValidationResult validateBasicInputs(UUID townId, UUID playerId, long amount, String description) {
        // Null checks
        if (townId == null) {
            return ValidationResult.invalid("Town ID cannot be null");
        }
        if (playerId == null) {
            return ValidationResult.invalid("Player ID cannot be null");
        }
        if (description == null) {
            return ValidationResult.invalid("Description cannot be null");
        }
        
        // Amount validation
        if (amount < MIN_TRANSACTION_AMOUNT) {
            return ValidationResult.invalid("Amount must be at least " + MIN_TRANSACTION_AMOUNT);
        }
        if (amount > MAX_TRANSACTION_AMOUNT) {
            return ValidationResult.invalid("Amount exceeds maximum limit of " + MAX_TRANSACTION_AMOUNT);
        }
        
        // Description validation
        if (description.trim().isEmpty()) {
            return ValidationResult.invalid("Description cannot be empty");
        }
        if (description.length() > MAX_DESCRIPTION_LENGTH) {
            return ValidationResult.invalid("Description too long (max " + MAX_DESCRIPTION_LENGTH + " characters)");
        }
        if (!VALID_DESCRIPTION_PATTERN.matcher(description).matches()) {
            return ValidationResult.invalid("Description contains invalid characters");
        }
        
        return ValidationResult.valid();
    }
    
    /**
     * Validates town membership.
     */
    private static ValidationResult validateTownMembership(UUID townId, UUID playerId) {
        Town town = TownManager.getInstance().getTown(townId);
        if (town == null) {
            return ValidationResult.invalid("Town not found");
        }
        
        if (!town.getPlayers().contains(playerId)) {
            return ValidationResult.invalid("Player is not a member of this town");
        }
        
        return ValidationResult.valid();
    }
    
    /**
     * Validates deposit permission.
     */
    private static ValidationResult validateDepositPermission(UUID townId, UUID playerId) {
        Town town = TownManager.getInstance().getTown(townId);
        TownPlayer townPlayer = town.getPlayer(playerId);
        
        if (townPlayer == null) {
            return ValidationResult.invalid("Player data not found in town");
        }
        
        if (!hasPermission(townPlayer, "Can deposit money")) {
            return ValidationResult.invalid("Player does not have permission to deposit money");
        }
        
        return ValidationResult.valid();
    }
    
    /**
     * Validates withdrawal permission.
     */
    private static ValidationResult validateWithdrawalPermission(UUID townId, UUID playerId) {
        Town town = TownManager.getInstance().getTown(townId);
        TownPlayer townPlayer = town.getPlayer(playerId);
        
        if (townPlayer == null) {
            return ValidationResult.invalid("Player data not found in town");
        }
        
        if (!hasPermission(townPlayer, "Can withdraw money")) {
            return ValidationResult.invalid("Player does not have permission to withdraw money");
        }
        
        return ValidationResult.valid();
    }
    
    /**
     * Checks if a player has a specific permission.
     */
    private static boolean hasPermission(TownPlayer townPlayer, String permission) {
        return townPlayer.hasPermission("Town Bank", permission);
    }
    
    /**
     * Gets daily withdrawals for a player.
     */
    private static long getDailyWithdrawals(TownBank bank, UUID playerId) {
        java.util.Calendar today = java.util.Calendar.getInstance();
        today.set(java.util.Calendar.HOUR_OF_DAY, 0);
        today.set(java.util.Calendar.MINUTE, 0);
        today.set(java.util.Calendar.SECOND, 0);
        today.set(java.util.Calendar.MILLISECOND, 0);
        long todayStart = today.getTimeInMillis();
        
        return bank.getTransactionHistory().stream()
            .filter(t -> t.getType() == TownBankTransaction.TransactionType.WITHDRAWAL)
            .filter(t -> t.getPlayerId() != null && t.getPlayerId().equals(playerId))
            .filter(t -> t.getTimestamp() >= todayStart)
            .mapToLong(TownBankTransaction::getAmount)
            .sum();
    }
    
    /**
     * Validates that a balance is within acceptable limits.
     */
    public static ValidationResult validateBalance(long balance) {
        if (balance < 0) {
            return ValidationResult.invalid("Balance cannot be negative");
        }
        if (balance > MAX_BANK_BALANCE) {
            return ValidationResult.invalid("Balance exceeds maximum limit");
        }
        return ValidationResult.valid();
    }
    
    /**
     * Sanitizes a description string.
     */
    public static String sanitizeDescription(String description) {
        if (description == null) {
            return "No description";
        }
        
        // Trim and limit length
        String sanitized = description.trim();
        if (sanitized.length() > MAX_DESCRIPTION_LENGTH) {
            sanitized = sanitized.substring(0, MAX_DESCRIPTION_LENGTH - 3) + "...";
        }
        
        // Remove any potentially dangerous characters
        sanitized = sanitized.replaceAll("[<>\"'&]", "");
        
        return sanitized.isEmpty() ? "No description" : sanitized;
    }
    
    /**
     * Validation result class.
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        
        private ValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }
        
        public static ValidationResult valid() {
            return new ValidationResult(true, null);
        }
        
        public static ValidationResult invalid(String errorMessage) {
            return new ValidationResult(false, errorMessage);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
    }
}
