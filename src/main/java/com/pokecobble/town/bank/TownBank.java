package com.pokecobble.town.bank;

import java.util.*;

/**
 * Represents a town's bank account with balance and transaction history.
 * Each town has its own bank account that can be used for deposits, withdrawals,
 * and other financial operations.
 */
public class TownBank {
    private final UUID townId;
    private long balance;
    private final List<TownBankTransaction> transactionHistory;
    private final Date creationDate;
    private Date lastTransactionDate;
    private int dataVersion;
    
    // Bank settings
    private boolean allowDeposits;
    private boolean allowWithdrawals;
    private long dailyWithdrawLimit;
    private long maxBalance;
    
    // Constants
    private static final int MAX_TRANSACTION_HISTORY = 1000;
    private static final long DEFAULT_DAILY_WITHDRAW_LIMIT = 10000L;
    private static final long DEFAULT_MAX_BALANCE = 1000000L;
    
    /**
     * Creates a new town bank for the specified town.
     *
     * @param townId The UUID of the town this bank belongs to
     */
    public TownBank(UUID townId) {
        this.townId = townId;
        this.balance = 0L;
        this.transactionHistory = new ArrayList<>();
        this.creationDate = new Date();
        this.lastTransactionDate = null;
        this.dataVersion = 1;
        
        // Initialize default settings
        this.allowDeposits = true;
        this.allowWithdrawals = true;
        this.dailyWithdrawLimit = DEFAULT_DAILY_WITHDRAW_LIMIT;
        this.maxBalance = DEFAULT_MAX_BALANCE;
    }
    
    /**
     * Creates a town bank with existing data (for loading from storage).
     */
    public TownBank(UUID townId, long balance, List<TownBankTransaction> transactionHistory, 
                   Date creationDate, Date lastTransactionDate, int dataVersion,
                   boolean allowDeposits, boolean allowWithdrawals, 
                   long dailyWithdrawLimit, long maxBalance) {
        this.townId = townId;
        this.balance = balance;
        this.transactionHistory = new ArrayList<>(transactionHistory);
        this.creationDate = creationDate;
        this.lastTransactionDate = lastTransactionDate;
        this.dataVersion = dataVersion;
        this.allowDeposits = allowDeposits;
        this.allowWithdrawals = allowWithdrawals;
        this.dailyWithdrawLimit = dailyWithdrawLimit;
        this.maxBalance = maxBalance;
    }
    
    /**
     * Gets the town ID this bank belongs to.
     */
    public UUID getTownId() {
        return townId;
    }
    
    /**
     * Gets the current balance.
     */
    public long getBalance() {
        return balance;
    }
    
    /**
     * Sets the balance (used for loading from storage).
     */
    public void setBalance(long balance) {
        this.balance = balance;
        incrementVersion();
    }
    
    /**
     * Adds money to the bank balance.
     *
     * @param amount The amount to add
     * @param playerId The player making the deposit
     * @param description Transaction description
     * @return true if successful, false if deposit not allowed or would exceed max balance
     */
    public boolean deposit(long amount, UUID playerId, String description) {
        if (!allowDeposits || amount <= 0) {
            return false;
        }
        
        if (balance + amount > maxBalance) {
            return false; // Would exceed maximum balance
        }
        
        long oldBalance = balance;
        balance += amount;
        
        TownBankTransaction transaction = new TownBankTransaction(
            TownBankTransaction.TransactionType.DEPOSIT,
            amount,
            playerId,
            description,
            oldBalance,
            balance
        );
        
        addTransaction(transaction);
        return true;
    }
    
    /**
     * Withdraws money from the bank balance.
     *
     * @param amount The amount to withdraw
     * @param playerId The player making the withdrawal
     * @param description Transaction description
     * @return true if successful, false if insufficient funds or withdrawal not allowed
     */
    public boolean withdraw(long amount, UUID playerId, String description) {
        if (!allowWithdrawals || amount <= 0 || balance < amount) {
            return false;
        }
        
        // Check daily withdrawal limit
        if (getDailyWithdrawals(playerId) + amount > dailyWithdrawLimit) {
            return false;
        }
        
        long oldBalance = balance;
        balance -= amount;
        
        TownBankTransaction transaction = new TownBankTransaction(
            TownBankTransaction.TransactionType.WITHDRAWAL,
            amount,
            playerId,
            description,
            oldBalance,
            balance
        );
        
        addTransaction(transaction);
        return true;
    }
    
    /**
     * Adds a transaction to the history.
     */
    private void addTransaction(TownBankTransaction transaction) {
        transactionHistory.add(0, transaction); // Add to beginning for newest first
        
        // Limit history size
        if (transactionHistory.size() > MAX_TRANSACTION_HISTORY) {
            transactionHistory.subList(MAX_TRANSACTION_HISTORY, transactionHistory.size()).clear();
        }
        
        lastTransactionDate = new Date();
        incrementVersion();
    }
    
    /**
     * Gets the transaction history.
     */
    public List<TownBankTransaction> getTransactionHistory() {
        return new ArrayList<>(transactionHistory);
    }

    /**
     * Sets the transaction history (used for client-side synchronization).
     */
    public void setTransactionHistory(List<TownBankTransaction> transactions) {
        this.transactionHistory.clear();
        this.transactionHistory.addAll(transactions);
        incrementVersion();
    }
    
    /**
     * Gets recent transactions for graph display.
     *
     * @param days Number of days to look back
     * @return List of transactions within the specified timeframe
     */
    public List<TownBankTransaction> getRecentTransactions(int days) {
        long cutoffTime = System.currentTimeMillis() - (days * 24L * 60L * 60L * 1000L);
        
        return transactionHistory.stream()
            .filter(t -> t.getTimestamp() >= cutoffTime)
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * Gets daily withdrawals for a specific player today.
     */
    private long getDailyWithdrawals(UUID playerId) {
        Calendar today = Calendar.getInstance();
        today.set(Calendar.HOUR_OF_DAY, 0);
        today.set(Calendar.MINUTE, 0);
        today.set(Calendar.SECOND, 0);
        today.set(Calendar.MILLISECOND, 0);
        long todayStart = today.getTimeInMillis();
        
        return transactionHistory.stream()
            .filter(t -> t.getType() == TownBankTransaction.TransactionType.WITHDRAWAL)
            .filter(t -> t.getPlayerId().equals(playerId))
            .filter(t -> t.getTimestamp() >= todayStart)
            .mapToLong(TownBankTransaction::getAmount)
            .sum();
    }
    
    /**
     * Gets the creation date.
     */
    public Date getCreationDate() {
        return creationDate;
    }
    
    /**
     * Gets the last transaction date.
     */
    public Date getLastTransactionDate() {
        return lastTransactionDate;
    }
    
    /**
     * Gets the data version for synchronization.
     */
    public int getDataVersion() {
        return dataVersion;
    }
    
    /**
     * Increments the data version.
     */
    private void incrementVersion() {
        dataVersion++;
    }
    
    // Bank settings getters and setters
    public boolean isDepositsAllowed() {
        return allowDeposits;
    }
    
    public void setDepositsAllowed(boolean allowDeposits) {
        this.allowDeposits = allowDeposits;
        incrementVersion();
    }
    
    public boolean isWithdrawalsAllowed() {
        return allowWithdrawals;
    }
    
    public void setWithdrawalsAllowed(boolean allowWithdrawals) {
        this.allowWithdrawals = allowWithdrawals;
        incrementVersion();
    }
    
    public long getDailyWithdrawLimit() {
        return dailyWithdrawLimit;
    }
    
    public void setDailyWithdrawLimit(long dailyWithdrawLimit) {
        this.dailyWithdrawLimit = dailyWithdrawLimit;
        incrementVersion();
    }
    
    public long getMaxBalance() {
        return maxBalance;
    }
    
    public void setMaxBalance(long maxBalance) {
        this.maxBalance = maxBalance;
        incrementVersion();
    }
}
