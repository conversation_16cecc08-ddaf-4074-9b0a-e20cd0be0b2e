package com.pokecobble.town.bank;

import java.util.Date;
import java.util.UUID;

/**
 * Represents a transaction in a town bank.
 * This includes deposits, withdrawals, and other financial operations.
 */
public class TownBankTransaction {
    private final TransactionType type;
    private final long amount;
    private final UUID playerId;
    private final String description;
    private final long balanceBefore;
    private final long balanceAfter;
    private final long timestamp;
    private final UUID transactionId;
    
    /**
     * Types of town bank transactions.
     */
    public enum TransactionType {
        DEPOSIT("Deposit"),
        WITHDRAWAL("Withdrawal"),
        TRANSFER_IN("Transfer In"),
        TRANSFER_OUT("Transfer Out"),
        ADMIN_ADD("Admin Add"),
        ADMIN_REMOVE("Admin Remove"),
        JOB_PAYMENT("Job Payment"),
        TAX_COLLECTION("Tax Collection"),
        FINE("Fine"),
        REFUND("Refund");
        
        private final String displayName;
        
        TransactionType(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    /**
     * Creates a new town bank transaction.
     *
     * @param type The type of transaction
     * @param amount The amount involved in the transaction
     * @param playerId The player who initiated the transaction (can be null for system transactions)
     * @param description A description of the transaction
     * @param balanceBefore The balance before the transaction
     * @param balanceAfter The balance after the transaction
     */
    public TownBankTransaction(TransactionType type, long amount, UUID playerId, 
                              String description, long balanceBefore, long balanceAfter) {
        this.type = type;
        this.amount = amount;
        this.playerId = playerId;
        this.description = description;
        this.balanceBefore = balanceBefore;
        this.balanceAfter = balanceAfter;
        this.timestamp = System.currentTimeMillis();
        this.transactionId = UUID.randomUUID();
    }
    
    /**
     * Creates a transaction with existing data (for loading from storage).
     */
    public TownBankTransaction(TransactionType type, long amount, UUID playerId, 
                              String description, long balanceBefore, long balanceAfter,
                              long timestamp, UUID transactionId) {
        this.type = type;
        this.amount = amount;
        this.playerId = playerId;
        this.description = description;
        this.balanceBefore = balanceBefore;
        this.balanceAfter = balanceAfter;
        this.timestamp = timestamp;
        this.transactionId = transactionId;
    }
    
    /**
     * Gets the transaction type.
     */
    public TransactionType getType() {
        return type;
    }
    
    /**
     * Gets the transaction amount.
     */
    public long getAmount() {
        return amount;
    }
    
    /**
     * Gets the player who initiated the transaction.
     */
    public UUID getPlayerId() {
        return playerId;
    }
    
    /**
     * Gets the transaction description.
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Gets the balance before the transaction.
     */
    public long getBalanceBefore() {
        return balanceBefore;
    }
    
    /**
     * Gets the balance after the transaction.
     */
    public long getBalanceAfter() {
        return balanceAfter;
    }
    
    /**
     * Gets the transaction timestamp.
     */
    public long getTimestamp() {
        return timestamp;
    }
    
    /**
     * Gets the transaction timestamp as a Date.
     */
    public Date getDate() {
        return new Date(timestamp);
    }
    
    /**
     * Gets the unique transaction ID.
     */
    public UUID getTransactionId() {
        return transactionId;
    }
    
    /**
     * Gets the change in balance (positive for increases, negative for decreases).
     */
    public long getBalanceChange() {
        return balanceAfter - balanceBefore;
    }
    
    /**
     * Checks if this transaction increased the balance.
     */
    public boolean isIncrease() {
        return getBalanceChange() > 0;
    }
    
    /**
     * Checks if this transaction decreased the balance.
     */
    public boolean isDecrease() {
        return getBalanceChange() < 0;
    }
    
    /**
     * Gets a formatted string representation of the transaction.
     */
    public String getFormattedDescription() {
        String prefix = isIncrease() ? "+" : "";
        return String.format("%s%d coins - %s", prefix, getBalanceChange(), description);
    }
    
    /**
     * Gets a short label for graph display.
     */
    public String getGraphLabel() {
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTimeInMillis(timestamp);
        return String.format("%02d/%02d", cal.get(java.util.Calendar.MONTH) + 1, cal.get(java.util.Calendar.DAY_OF_MONTH));
    }
    
    @Override
    public String toString() {
        return String.format("TownBankTransaction{type=%s, amount=%d, playerId=%s, description='%s', timestamp=%d}", 
                           type, amount, playerId, description, timestamp);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        TownBankTransaction that = (TownBankTransaction) obj;
        return transactionId.equals(that.transactionId);
    }
    
    @Override
    public int hashCode() {
        return transactionId.hashCode();
    }
}
