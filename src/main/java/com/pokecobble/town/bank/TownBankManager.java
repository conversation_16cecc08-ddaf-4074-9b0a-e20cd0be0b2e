package com.pokecobble.town.bank;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.economy.api.EconomyAPI;
import com.pokecobble.util.MoneyAPI;
import com.pokecobble.Pokecobbleclaim;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.server.MinecraftServer;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages town bank operations including balance management, transactions,
 * and integration with the economy system.
 */
public class TownBankManager {
    private static TownBankManager instance;
    private final Map<UUID, TownBank> townBanks = new ConcurrentHashMap<>();
    private MinecraftServer server;
    private boolean initialized = false;

    // Integrity check timer
    private java.util.concurrent.ScheduledExecutorService integrityCheckExecutor;
    private static final long INTEGRITY_CHECK_INTERVAL_MINUTES = 30;
    
    private TownBankManager() {}
    
    /**
     * Gets the singleton instance.
     */
    public static TownBankManager getInstance() {
        if (instance == null) {
            instance = new TownBankManager();
        }
        return instance;
    }
    
    /**
     * Initializes the town bank manager.
     */
    public void initialize(MinecraftServer server) {
        if (initialized) {
            Pokecobbleclaim.LOGGER.warn("TownBankManager already initialized");
            return;
        }
        
        this.server = server;
        this.initialized = true;

        // Initialize banks for existing towns
        initializeExistingTownBanks();

        // Start periodic integrity checks
        startIntegrityCheckTimer();

        Pokecobbleclaim.LOGGER.info("TownBankManager initialized successfully");
    }
    
    /**
     * Creates banks for all existing towns that don't have one.
     */
    private void initializeExistingTownBanks() {
        Collection<Town> towns = TownManager.getInstance().getAllTowns();
        for (Town town : towns) {
            if (!townBanks.containsKey(town.getId())) {
                TownBank bank = new TownBank(town.getId());
                townBanks.put(town.getId(), bank);
                Pokecobbleclaim.LOGGER.debug("Created bank for existing town: " + town.getName());
            }
        }
    }
    
    /**
     * Creates a new town bank for a town.
     */
    public TownBank createTownBank(UUID townId) {
        if (townBanks.containsKey(townId)) {
            return townBanks.get(townId);
        }
        
        TownBank bank = new TownBank(townId);
        townBanks.put(townId, bank);
        
        Pokecobbleclaim.LOGGER.debug("Created new town bank for town ID: " + townId);
        return bank;
    }
    
    /**
     * Gets a town bank by town ID.
     */
    public TownBank getTownBank(UUID townId) {
        return townBanks.computeIfAbsent(townId, TownBank::new);
    }
    
    /**
     * Deposits money into a town bank from a player with full safety measures.
     */
    public BankOperationResult deposit(UUID townId, UUID playerId, long amount, String description) {
        if (!initialized) {
            return BankOperationResult.failure("Bank system not initialized");
        }

        // Sanitize description
        final String sanitizedDescription = TownBankValidator.sanitizeDescription(description);

        // Comprehensive validation
        TownBankValidator.ValidationResult validation = TownBankValidator.validateDeposit(townId, playerId, amount, sanitizedDescription);
        if (!validation.isValid()) {
            return BankOperationResult.failure(validation.getErrorMessage());
        }

        // Generate unique transaction ID
        String transactionId = TownBankTransactionManager.getInstance()
            .generateTransactionId(townId, playerId, "DEPOSIT", amount);

        // Record transaction attempt
        TownBankAuditor.getInstance().recordTransactionAttempt(townId, playerId, "DEPOSIT", amount, sanitizedDescription, transactionId);

        // Execute transaction with atomic safety
        return TownBankTransactionManager.getInstance().executeTransaction(townId, playerId, transactionId,
            new TownBankTransactionManager.TransactionCallback<BankOperationResult>() {
                @Override
                public BankOperationResult execute() throws Exception {
                    return executeDepositTransaction(townId, playerId, amount, sanitizedDescription, transactionId);
                }

                @Override
                public BankOperationResult onError(Exception e) {
                    TownBankAuditor.getInstance().recordTransactionFailure(townId, playerId, "DEPOSIT",
                        amount, sanitizedDescription, transactionId, "Exception: " + e.getMessage());
                    return BankOperationResult.failure("Transaction failed due to error: " + e.getMessage());
                }

                @Override
                public BankOperationResult onTimeout() {
                    TownBankAuditor.getInstance().recordTransactionFailure(townId, playerId, "DEPOSIT",
                        amount, sanitizedDescription, transactionId, "Transaction timeout");
                    return BankOperationResult.failure("Transaction timed out");
                }

                @Override
                public BankOperationResult onDuplicate() {
                    return BankOperationResult.failure("Duplicate transaction detected");
                }

                @Override
                public BankOperationResult onInterrupted() {
                    TownBankAuditor.getInstance().recordTransactionFailure(townId, playerId, "DEPOSIT",
                        amount, sanitizedDescription, transactionId, "Transaction interrupted");
                    return BankOperationResult.failure("Transaction was interrupted");
                }
            });
    }

    /**
     * Executes the actual deposit transaction with full rollback safety.
     */
    private BankOperationResult executeDepositTransaction(UUID townId, UUID playerId, long amount,
                                                         String description, String transactionId) {
        try {
            // Get current balances for rollback purposes
            long playerBalanceBefore = getPlayerBalance(playerId);
            TownBank bank = getTownBank(townId);
            long bankBalanceBefore = bank.getBalance();

            // Double-check player has sufficient funds (race condition protection)
            if (playerBalanceBefore < amount) {
                TownBankAuditor.getInstance().recordTransactionFailure(townId, playerId, "DEPOSIT",
                    amount, description, transactionId, "Insufficient player funds at execution time");
                return BankOperationResult.failure("Insufficient funds");
            }

            // Step 1: Deduct money from player first (fail fast if this doesn't work)
            if (!deductPlayerMoney(playerId, amount)) {
                TownBankAuditor.getInstance().recordTransactionFailure(townId, playerId, "DEPOSIT",
                    amount, description, transactionId, "Failed to deduct player money");
                return BankOperationResult.failure("Failed to deduct money from player account");
            }

            // Step 2: Add money to town bank
            if (!bank.deposit(amount, playerId, description)) {
                // CRITICAL: Rollback player money deduction
                if (!addPlayerMoney(playerId, amount)) {
                    // CRITICAL ERROR: Money lost! Log and alert
                    Pokecobbleclaim.LOGGER.error("CRITICAL ERROR: Failed to rollback player money after bank deposit failure! " +
                        "Player {} lost {} coins. Transaction: {}", playerId, amount, transactionId);
                    TownBankAuditor.getInstance().recordTransactionFailure(townId, playerId, "DEPOSIT",
                        amount, description, transactionId, "CRITICAL: Money lost during rollback");
                }
                return BankOperationResult.failure("Bank deposit failed");
            }

            // Step 3: Verify balances are correct
            long playerBalanceAfter = getPlayerBalance(playerId);
            long bankBalanceAfter = bank.getBalance();

            // Verify player balance decreased by exactly the amount
            if (playerBalanceAfter != playerBalanceBefore - amount) {
                Pokecobbleclaim.LOGGER.error("Player balance verification failed! Expected: {}, Actual: {}",
                    playerBalanceBefore - amount, playerBalanceAfter);
            }

            // Verify bank balance increased by exactly the amount
            if (bankBalanceAfter != bankBalanceBefore + amount) {
                Pokecobbleclaim.LOGGER.error("Bank balance verification failed! Expected: {}, Actual: {}",
                    bankBalanceBefore + amount, bankBalanceAfter);
            }

            // Step 4: Save and sync
            saveTownBank(bank);
            syncTownBankToClients(townId);

            // Step 5: Record successful transaction
            TownBankAuditor.getInstance().recordTransactionSuccess(townId, playerId, "DEPOSIT",
                amount, description, transactionId, bankBalanceBefore, bankBalanceAfter);

            return BankOperationResult.success("Deposited " + amount + " coins successfully");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Unexpected error in deposit transaction", e);
            throw e; // Re-throw to be handled by transaction manager
        }
    }

    /**
     * Withdraws money from a town bank to a player with full safety measures.
     */
    public BankOperationResult withdraw(UUID townId, UUID playerId, long amount, String description) {
        if (!initialized) {
            return BankOperationResult.failure("Bank system not initialized");
        }

        // Sanitize description
        final String sanitizedDescription = TownBankValidator.sanitizeDescription(description);

        // Comprehensive validation
        TownBankValidator.ValidationResult validation = TownBankValidator.validateWithdrawal(townId, playerId, amount, sanitizedDescription);
        if (!validation.isValid()) {
            return BankOperationResult.failure(validation.getErrorMessage());
        }

        // Generate unique transaction ID
        String transactionId = TownBankTransactionManager.getInstance()
            .generateTransactionId(townId, playerId, "WITHDRAWAL", amount);

        // Record transaction attempt
        TownBankAuditor.getInstance().recordTransactionAttempt(townId, playerId, "WITHDRAWAL", amount, sanitizedDescription, transactionId);

        // Execute transaction with atomic safety
        return TownBankTransactionManager.getInstance().executeTransaction(townId, playerId, transactionId,
            new TownBankTransactionManager.TransactionCallback<BankOperationResult>() {
                @Override
                public BankOperationResult execute() throws Exception {
                    return executeWithdrawalTransaction(townId, playerId, amount, sanitizedDescription, transactionId);
                }

                @Override
                public BankOperationResult onError(Exception e) {
                    TownBankAuditor.getInstance().recordTransactionFailure(townId, playerId, "WITHDRAWAL",
                        amount, sanitizedDescription, transactionId, "Exception: " + e.getMessage());
                    return BankOperationResult.failure("Transaction failed due to error: " + e.getMessage());
                }

                @Override
                public BankOperationResult onTimeout() {
                    TownBankAuditor.getInstance().recordTransactionFailure(townId, playerId, "WITHDRAWAL",
                        amount, sanitizedDescription, transactionId, "Transaction timeout");
                    return BankOperationResult.failure("Transaction timed out");
                }

                @Override
                public BankOperationResult onDuplicate() {
                    return BankOperationResult.failure("Duplicate transaction detected");
                }

                @Override
                public BankOperationResult onInterrupted() {
                    TownBankAuditor.getInstance().recordTransactionFailure(townId, playerId, "WITHDRAWAL",
                        amount, sanitizedDescription, transactionId, "Transaction interrupted");
                    return BankOperationResult.failure("Transaction was interrupted");
                }
            });
    }

    /**
     * Executes the actual withdrawal transaction with full rollback safety.
     */
    private BankOperationResult executeWithdrawalTransaction(UUID townId, UUID playerId, long amount,
                                                           String description, String transactionId) {
        try {
            // Get current balances for rollback purposes
            long playerBalanceBefore = getPlayerBalance(playerId);
            TownBank bank = getTownBank(townId);
            long bankBalanceBefore = bank.getBalance();

            // Double-check bank has sufficient funds (race condition protection)
            if (bankBalanceBefore < amount) {
                TownBankAuditor.getInstance().recordTransactionFailure(townId, playerId, "WITHDRAWAL",
                    amount, description, transactionId, "Insufficient bank funds at execution time");
                return BankOperationResult.failure("Insufficient funds in town bank");
            }

            // Step 1: Withdraw money from bank first (fail fast if this doesn't work)
            if (!bank.withdraw(amount, playerId, description)) {
                TownBankAuditor.getInstance().recordTransactionFailure(townId, playerId, "WITHDRAWAL",
                    amount, description, transactionId, "Bank withdrawal failed");
                return BankOperationResult.failure("Bank withdrawal failed");
            }

            // Step 2: Add money to player
            if (!addPlayerMoney(playerId, amount)) {
                // CRITICAL: Rollback bank withdrawal
                if (!bank.deposit(amount, playerId, "Rollback failed withdrawal")) {
                    // CRITICAL ERROR: Money lost! Log and alert
                    Pokecobbleclaim.LOGGER.error("CRITICAL ERROR: Failed to rollback bank withdrawal after player add failure! " +
                        "Town bank lost {} coins. Transaction: {}", amount, transactionId);
                    TownBankAuditor.getInstance().recordTransactionFailure(townId, playerId, "WITHDRAWAL",
                        amount, description, transactionId, "CRITICAL: Money lost during rollback");
                }
                return BankOperationResult.failure("Failed to add money to player account");
            }

            // Step 3: Verify balances are correct
            long playerBalanceAfter = getPlayerBalance(playerId);
            long bankBalanceAfter = bank.getBalance();

            // Verify player balance increased by exactly the amount
            if (playerBalanceAfter != playerBalanceBefore + amount) {
                Pokecobbleclaim.LOGGER.error("Player balance verification failed! Expected: {}, Actual: {}",
                    playerBalanceBefore + amount, playerBalanceAfter);
            }

            // Verify bank balance decreased by exactly the amount
            if (bankBalanceAfter != bankBalanceBefore - amount) {
                Pokecobbleclaim.LOGGER.error("Bank balance verification failed! Expected: {}, Actual: {}",
                    bankBalanceBefore - amount, bankBalanceAfter);
            }

            // Step 4: Save and sync
            saveTownBank(bank);
            syncTownBankToClients(townId);

            // Step 5: Record successful transaction
            TownBankAuditor.getInstance().recordTransactionSuccess(townId, playerId, "WITHDRAWAL",
                amount, description, transactionId, bankBalanceBefore, bankBalanceAfter);

            return BankOperationResult.success("Withdrew " + amount + " coins successfully");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Unexpected error in withdrawal transaction", e);
            throw e; // Re-throw to be handled by transaction manager
        }
    }


    
    /**
     * Gets a player's balance using the economy system.
     */
    private long getPlayerBalance(UUID playerId) {
        try {
            EconomyAPI economyAPI = EconomyAPI.getInstance();
            if (economyAPI.isAvailable()) {
                return economyAPI.getBalance(playerId);
            } else {
                // Fallback to old system
                ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
                if (player != null) {
                    return MoneyAPI.getBalance(player);
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error getting player balance: " + e.getMessage());
        }
        return 0;
    }
    
    /**
     * Deducts money from a player using the economy system.
     */
    private boolean deductPlayerMoney(UUID playerId, long amount) {
        try {
            EconomyAPI economyAPI = EconomyAPI.getInstance();
            if (economyAPI.isAvailable()) {
                return economyAPI.subtractMoney(playerId, amount, "Town bank deposit");
            } else {
                // Fallback to old system
                ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
                if (player != null) {
                    return MoneyAPI.removeMoney(player, amount);
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error deducting player money: " + e.getMessage());
        }
        return false;
    }
    
    /**
     * Adds money to a player using the economy system.
     */
    private boolean addPlayerMoney(UUID playerId, long amount) {
        try {
            EconomyAPI economyAPI = EconomyAPI.getInstance();
            if (economyAPI.isAvailable()) {
                return economyAPI.addMoney(playerId, amount, "Town bank withdrawal");
            } else {
                // Fallback to old system
                ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
                if (player != null) {
                    return MoneyAPI.addMoney(player, amount);
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error adding player money: " + e.getMessage());
        }
        return false;
    }
    
    /**
     * Saves town bank data to persistent storage.
     */
    private void saveTownBank(TownBank bank) {
        // Save the entire town data which includes the bank data
        Town town = TownManager.getInstance().getTown(bank.getTownId());
        if (town != null) {
            TownManager.getInstance().saveTown(town);
        }
    }
    
    /**
     * Syncs town bank data to all town members.
     */
    private void syncTownBankToClients(UUID townId) {
        com.pokecobble.town.network.bank.TownBankNetworkHandler.syncTownBankBalance(townId);
    }
    
    /**
     * Gets all town banks (for data persistence).
     */
    public Map<UUID, TownBank> getAllTownBanks() {
        return new HashMap<>(townBanks);
    }
    
    /**
     * Loads a town bank from storage.
     */
    public void loadTownBank(TownBank bank) {
        townBanks.put(bank.getTownId(), bank);
    }
    
    /**
     * Result of a bank operation.
     */
    public static class BankOperationResult {
        private final boolean success;
        private final String message;
        
        private BankOperationResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        public static BankOperationResult success(String message) {
            return new BankOperationResult(true, message);
        }
        
        public static BankOperationResult failure(String message) {
            return new BankOperationResult(false, message);
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getMessage() {
            return message;
        }
    }

    /**
     * Starts the periodic integrity check timer.
     */
    private void startIntegrityCheckTimer() {
        integrityCheckExecutor = java.util.concurrent.Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "TownBankIntegrityCheck");
            t.setDaemon(true);
            return t;
        });

        integrityCheckExecutor.scheduleAtFixedRate(
            this::performIntegrityCheck,
            INTEGRITY_CHECK_INTERVAL_MINUTES,
            INTEGRITY_CHECK_INTERVAL_MINUTES,
            java.util.concurrent.TimeUnit.MINUTES
        );

        Pokecobbleclaim.LOGGER.info("Started town bank integrity check timer (every {} minutes)", INTEGRITY_CHECK_INTERVAL_MINUTES);
    }

    /**
     * Performs integrity checks on all town banks.
     */
    private void performIntegrityCheck() {
        try {
            Pokecobbleclaim.LOGGER.debug("Starting periodic town bank integrity check");

            int checkedBanks = 0;
            int failedBanks = 0;

            for (UUID townId : townBanks.keySet()) {
                try {
                    if (!TownBankAuditor.getInstance().verifyBankIntegrity(townId)) {
                        failedBanks++;
                        Pokecobbleclaim.LOGGER.error("Integrity check failed for town bank: {}", townId);
                    }
                    checkedBanks++;
                } catch (Exception e) {
                    failedBanks++;
                    Pokecobbleclaim.LOGGER.error("Error during integrity check for town bank: {}", townId, e);
                }
            }

            if (failedBanks > 0) {
                Pokecobbleclaim.LOGGER.warn("Integrity check completed: {}/{} banks failed checks", failedBanks, checkedBanks);
            } else {
                Pokecobbleclaim.LOGGER.debug("Integrity check completed: All {} banks passed", checkedBanks);
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during periodic integrity check", e);
        }
    }

    /**
     * Shuts down the integrity check timer.
     */
    public void shutdown() {
        if (integrityCheckExecutor != null && !integrityCheckExecutor.isShutdown()) {
            integrityCheckExecutor.shutdown();
            try {
                if (!integrityCheckExecutor.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                    integrityCheckExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                integrityCheckExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
