package com.pokecobble.town.network.bank;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.bank.TownBank;
import com.pokecobble.town.bank.TownBankManager;
import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.UUID;

/**
 * Handles network communication for town bank operations with deduplication.
 */
public class TownBankNetworkHandler {

    // Track recent packets to prevent duplicates
    private static final long PACKET_DEDUP_TIME_MS = 5000; // 5 seconds
    private static final java.util.concurrent.ConcurrentHashMap<String, Long> packetTimestamps = new java.util.concurrent.ConcurrentHashMap<>();
    
    // Network identifiers
    public static final Identifier TOWN_BANK_DEPOSIT = new Identifier("pokecobbleclaim", "town_bank_deposit");
    public static final Identifier TOWN_BANK_WITHDRAW = new Identifier("pokecobbleclaim", "town_bank_withdraw");
    public static final Identifier TOWN_BANK_BALANCE_SYNC = new Identifier("pokecobbleclaim", "town_bank_balance_sync");
    public static final Identifier TOWN_BANK_BALANCE_REQUEST = new Identifier("pokecobbleclaim", "town_bank_balance_request");
    public static final Identifier TOWN_BANK_TRANSACTION_NOTIFY = new Identifier("pokecobbleclaim", "town_bank_transaction_notify");
    public static final Identifier TOWN_BANK_FULL_SYNC = new Identifier("pokecobbleclaim", "town_bank_full_sync");
    
    /**
     * Registers server-side network handlers.
     */
    public static void registerServerHandlers() {
        // Handle deposit requests
        ServerPlayNetworking.registerGlobalReceiver(TOWN_BANK_DEPOSIT, (server, player, handler, buf, responseSender) -> {
            try {
                UUID townId = buf.readUuid();
                long amount = buf.readLong();
                String description = buf.readString();
                
                server.execute(() -> {
                    handleDepositRequest(player, townId, amount, description);
                });
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling town bank deposit request", e);
            }
        });
        
        // Handle withdrawal requests
        ServerPlayNetworking.registerGlobalReceiver(TOWN_BANK_WITHDRAW, (server, player, handler, buf, responseSender) -> {
            try {
                UUID townId = buf.readUuid();
                long amount = buf.readLong();
                String description = buf.readString();

                server.execute(() -> {
                    handleWithdrawRequest(player, townId, amount, description);
                });
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling town bank withdraw request", e);
            }
        });

        // Handle balance sync requests
        ServerPlayNetworking.registerGlobalReceiver(TOWN_BANK_BALANCE_REQUEST, (server, player, handler, buf, responseSender) -> {
            try {
                UUID townId = buf.readUuid();

                server.execute(() -> {
                    handleBalanceRequest(player, townId);
                });
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling town bank balance request", e);
            }
        });
    }
    
    /**
     * Registers client-side network handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Handle balance sync from server
        ClientPlayNetworking.registerGlobalReceiver(TOWN_BANK_BALANCE_SYNC, (client, handler, buf, responseSender) -> {
            try {
                UUID townId = buf.readUuid();
                long balance = buf.readLong();
                
                client.execute(() -> {
                    handleBalanceSync(townId, balance);
                });
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling town bank balance sync", e);
            }
        });
        
        // Handle transaction notifications
        ClientPlayNetworking.registerGlobalReceiver(TOWN_BANK_TRANSACTION_NOTIFY, (client, handler, buf, responseSender) -> {
            try {
                UUID townId = buf.readUuid();
                String transactionType = buf.readString();
                long amount = buf.readLong();
                String playerName = buf.readString();
                String description = buf.readString();

                client.execute(() -> {
                    handleTransactionNotification(townId, transactionType, amount, playerName, description);
                });
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling town bank transaction notification", e);
            }
        });

        // Handle full bank sync (balance + transaction history)
        ClientPlayNetworking.registerGlobalReceiver(TOWN_BANK_FULL_SYNC, (client, handler, buf, responseSender) -> {
            try {
                UUID townId = buf.readUuid();
                long balance = buf.readLong();
                int transactionCount = buf.readInt();

                // Read all transaction data from the buffer before passing to client thread
                java.util.List<TransactionData> transactions = new java.util.ArrayList<>();
                for (int i = 0; i < transactionCount; i++) {
                    String typeName = buf.readString();
                    long amount = buf.readLong();
                    UUID playerId = buf.readUuid();
                    String description = buf.readString();
                    long balanceBefore = buf.readLong();
                    long balanceAfter = buf.readLong();
                    long timestamp = buf.readLong();
                    UUID transactionId = buf.readUuid();

                    transactions.add(new TransactionData(typeName, amount, playerId, description,
                                                       balanceBefore, balanceAfter, timestamp, transactionId));
                }

                client.execute(() -> {
                    handleFullBankSync(townId, balance, transactions);
                });
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling town bank full sync", e);
            }
        });
    }
    
    /**
     * Checks if a packet is a duplicate and should be ignored.
     */
    private static boolean isDuplicatePacket(UUID playerId, String operation, long amount) {
        String packetKey = playerId + "_" + operation + "_" + amount;
        long currentTime = System.currentTimeMillis();

        // Clean old entries
        packetTimestamps.entrySet().removeIf(entry -> currentTime - entry.getValue() > PACKET_DEDUP_TIME_MS);

        // Check if this is a duplicate
        Long lastTime = packetTimestamps.get(packetKey);
        if (lastTime != null && currentTime - lastTime < PACKET_DEDUP_TIME_MS) {
            return true; // Duplicate packet
        }

        // Record this packet
        packetTimestamps.put(packetKey, currentTime);
        return false;
    }

    /**
     * Handles deposit requests on the server.
     */
    private static void handleDepositRequest(ServerPlayerEntity player, UUID townId, long amount, String description) {
        try {
            // Check for duplicate packets
            if (isDuplicatePacket(player.getUuid(), "DEPOSIT", amount)) {
                Pokecobbleclaim.LOGGER.warn("Ignoring duplicate deposit packet from player {}", player.getName().getString());
                return;
            }

            TownBankManager.BankOperationResult result = TownBankManager.getInstance()
                .deposit(townId, player.getUuid(), amount, description);
            
            if (result.isSuccess()) {
                // Sync balance to all town members
                syncTownBankBalance(townId);
                
                // Notify all town members of the transaction
                notifyTownMembers(townId, "DEPOSIT", amount, player.getName().getString(), description);
                
                Pokecobbleclaim.LOGGER.debug("Town bank deposit successful: {} deposited {} to town {}", 
                    player.getName().getString(), amount, townId);
            } else {
                Pokecobbleclaim.LOGGER.debug("Town bank deposit failed: {}", result.getMessage());
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error processing town bank deposit", e);
        }
    }
    
    /**
     * Handles withdrawal requests on the server.
     */
    private static void handleWithdrawRequest(ServerPlayerEntity player, UUID townId, long amount, String description) {
        try {
            // Check for duplicate packets
            if (isDuplicatePacket(player.getUuid(), "WITHDRAWAL", amount)) {
                Pokecobbleclaim.LOGGER.warn("Ignoring duplicate withdrawal packet from player {}", player.getName().getString());
                return;
            }

            TownBankManager.BankOperationResult result = TownBankManager.getInstance()
                .withdraw(townId, player.getUuid(), amount, description);
            
            if (result.isSuccess()) {
                // Sync balance to all town members
                syncTownBankBalance(townId);
                
                // Notify all town members of the transaction
                notifyTownMembers(townId, "WITHDRAWAL", amount, player.getName().getString(), description);
                
                Pokecobbleclaim.LOGGER.debug("Town bank withdrawal successful: {} withdrew {} from town {}", 
                    player.getName().getString(), amount, townId);
            } else {
                Pokecobbleclaim.LOGGER.debug("Town bank withdrawal failed: {}", result.getMessage());
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error processing town bank withdrawal", e);
        }
    }

    /**
     * Handles balance sync requests from clients.
     */
    private static void handleBalanceRequest(ServerPlayerEntity player, UUID townId) {
        try {
            // Verify player is a member of the town
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null || !town.getPlayers().contains(player.getUuid())) {
                return; // Player not authorized to view this town's balance
            }

            // Send full bank data (balance + transaction history) to the requesting player
            sendFullBankSync(player, townId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling balance request", e);
        }
    }

    /**
     * Syncs town bank balance and transaction history to all town members.
     */
    public static void syncTownBankBalance(UUID townId) {
        try {
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) return;

            // Send full bank sync to all online town members
            for (UUID playerId : town.getPlayers()) {
                ServerPlayerEntity player = TownManager.getInstance().getServer().getPlayerManager().getPlayer(playerId);
                if (player != null) {
                    sendFullBankSync(player, townId);
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing town bank balance", e);
        }
    }
    
    /**
     * Notifies all town members of a transaction.
     */
    private static void notifyTownMembers(UUID townId, String transactionType, long amount, String playerName, String description) {
        try {
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) return;
            
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(townId);
            buf.writeString(transactionType);
            buf.writeLong(amount);
            buf.writeString(playerName);
            buf.writeString(description);
            
            // Send to all online town members
            for (UUID playerId : town.getPlayers()) {
                ServerPlayerEntity player = TownManager.getInstance().getServer().getPlayerManager().getPlayer(playerId);
                if (player != null) {
                    ServerPlayNetworking.send(player, TOWN_BANK_TRANSACTION_NOTIFY, buf);
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error notifying town members of transaction", e);
        }
    }
    
    /**
     * Handles balance sync on the client.
     */
    @Environment(EnvType.CLIENT)
    private static void handleBalanceSync(UUID townId, long balance) {
        // Update the client-side bank balance
        TownBank bank = TownBankManager.getInstance().getTownBank(townId);
        if (bank != null) {
            bank.setBalance(balance); // Update the balance directly
        }

        // Update any open town screens to refresh the balance display
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.currentScreen instanceof com.pokecobble.town.gui.MyTownScreen) {
            com.pokecobble.town.gui.MyTownScreen screen = (com.pokecobble.town.gui.MyTownScreen) client.currentScreen;
            screen.clearGraphDataCache(); // Force refresh of graph data
        }
    }
    
    /**
     * Handles transaction notifications on the client.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTransactionNotification(UUID townId, String transactionType, long amount, String playerName, String description) {
        // Request fresh balance sync to ensure client is up to date
        // This will trigger handleBalanceSync which updates the client-side balance
        requestBalanceSync(townId);

        // Update any open town screens to refresh the graph data
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.currentScreen instanceof com.pokecobble.town.gui.MyTownScreen) {
            com.pokecobble.town.gui.MyTownScreen screen = (com.pokecobble.town.gui.MyTownScreen) client.currentScreen;
            screen.clearGraphDataCache(); // Force refresh of graph data
        }

        // Log the transaction for debugging
        Pokecobbleclaim.LOGGER.debug("Town bank transaction: {} {} {} coins - {}",
            playerName, transactionType.toLowerCase(), amount, description);
    }
    
    /**
     * Requests a balance sync from the server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestBalanceSync(UUID townId) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(townId);

            ClientPlayNetworking.send(TOWN_BANK_BALANCE_REQUEST, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting balance sync", e);
        }
    }

    /**
     * Sends full bank data (balance + transaction history) to a specific player.
     */
    private static void sendFullBankSync(ServerPlayerEntity player, UUID townId) {
        try {
            TownBank bank = TownBankManager.getInstance().getTownBank(townId);
            if (bank == null) return;

            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(townId);
            buf.writeLong(bank.getBalance());

            // Get recent transactions (last 30 days for graph display)
            java.util.List<com.pokecobble.town.bank.TownBankTransaction> transactions = bank.getRecentTransactions(30);
            buf.writeInt(transactions.size());

            // Write transaction data
            for (com.pokecobble.town.bank.TownBankTransaction transaction : transactions) {
                buf.writeString(transaction.getType().name());
                buf.writeLong(transaction.getAmount());
                buf.writeUuid(transaction.getPlayerId() != null ? transaction.getPlayerId() : new UUID(0, 0));
                buf.writeString(transaction.getDescription());
                buf.writeLong(transaction.getBalanceBefore());
                buf.writeLong(transaction.getBalanceAfter());
                buf.writeLong(transaction.getTimestamp());
                buf.writeUuid(transaction.getTransactionId());
            }

            ServerPlayNetworking.send(player, TOWN_BANK_FULL_SYNC, buf);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending full bank sync", e);
        }
    }

    /**
     * Helper class to hold transaction data for network transfer.
     */
    private static class TransactionData {
        final String typeName;
        final long amount;
        final UUID playerId;
        final String description;
        final long balanceBefore;
        final long balanceAfter;
        final long timestamp;
        final UUID transactionId;

        TransactionData(String typeName, long amount, UUID playerId, String description,
                       long balanceBefore, long balanceAfter, long timestamp, UUID transactionId) {
            this.typeName = typeName;
            this.amount = amount;
            this.playerId = playerId;
            this.description = description;
            this.balanceBefore = balanceBefore;
            this.balanceAfter = balanceAfter;
            this.timestamp = timestamp;
            this.transactionId = transactionId;
        }
    }

    /**
     * Handles full bank sync on the client.
     */
    @Environment(EnvType.CLIENT)
    private static void handleFullBankSync(UUID townId, long balance, java.util.List<TransactionData> transactionDataList) {
        try {
            // Update the client-side bank balance
            TownBank bank = TownBankManager.getInstance().getTownBank(townId);
            if (bank == null) {
                // Create a new client-side bank if it doesn't exist
                bank = new TownBank(townId);
                TownBankManager.getInstance().loadTownBank(bank);
            }

            bank.setBalance(balance);

            // Convert transaction data to transaction objects
            java.util.List<com.pokecobble.town.bank.TownBankTransaction> transactions = new java.util.ArrayList<>();
            for (TransactionData data : transactionDataList) {
                // Handle null player ID
                UUID playerId = data.playerId;
                if (playerId.equals(new UUID(0, 0))) {
                    playerId = null;
                }

                com.pokecobble.town.bank.TownBankTransaction.TransactionType type =
                    com.pokecobble.town.bank.TownBankTransaction.TransactionType.valueOf(data.typeName);

                com.pokecobble.town.bank.TownBankTransaction transaction =
                    new com.pokecobble.town.bank.TownBankTransaction(type, data.amount, playerId, data.description,
                                                                   data.balanceBefore, data.balanceAfter, data.timestamp, data.transactionId);
                transactions.add(transaction);
            }

            // Set the transaction history on the client-side bank
            bank.setTransactionHistory(transactions);

            // Update any open town screens to refresh the balance display and graph
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.currentScreen instanceof com.pokecobble.town.gui.MyTownScreen) {
                com.pokecobble.town.gui.MyTownScreen screen = (com.pokecobble.town.gui.MyTownScreen) client.currentScreen;
                screen.clearGraphDataCache(); // Force refresh of graph data
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling full bank sync", e);
        }
    }

    /**
     * Sends a deposit request to the server.
     */
    @Environment(EnvType.CLIENT)
    public static void sendDepositRequest(UUID townId, long amount, String description) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(townId);
            buf.writeLong(amount);
            buf.writeString(description);
            
            ClientPlayNetworking.send(TOWN_BANK_DEPOSIT, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending deposit request", e);
        }
    }
    
    /**
     * Sends a withdrawal request to the server.
     */
    @Environment(EnvType.CLIENT)
    public static void sendWithdrawRequest(UUID townId, long amount, String description) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(townId);
            buf.writeLong(amount);
            buf.writeString(description);
            
            ClientPlayNetworking.send(TOWN_BANK_WITHDRAW, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending withdraw request", e);
        }
    }
}
