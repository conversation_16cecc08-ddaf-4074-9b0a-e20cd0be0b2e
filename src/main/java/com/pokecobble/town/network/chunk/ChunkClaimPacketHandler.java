package com.pokecobble.town.network.chunk;

import com.pokecobble.town.chunk.ServerTownChunkRegistry;
import com.pokecobble.town.Town;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.PacketValidator;
import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.math.ChunkPos;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Handles client-server communication for chunk claiming operations.
 * This class processes chunk claim requests and sends responses back to clients.
 */
public class ChunkClaimPacketHandler {
    
    /**
     * Initializes the chunk claim packet handlers.
     */
    public static void initialize() {
        Pokecobbleclaim.LOGGER.info("ChunkClaimPacketHandler: Starting initialization...");

        // Register server-side packet handlers
        Pokecobbleclaim.LOGGER.info("ChunkClaimPacketHandler: Registering server-side handler for CHUNK_CLAIM_REQUEST: {}",
            NetworkConstants.CHUNK_CLAIM_REQUEST);
        ServerPlayNetworking.registerGlobalReceiver(NetworkConstants.CHUNK_CLAIM_REQUEST,
            ChunkClaimPacketHandler::handleChunkClaimRequest);
        Pokecobbleclaim.LOGGER.info("ChunkClaimPacketHandler: Successfully registered CHUNK_CLAIM_REQUEST handler");

        ServerPlayNetworking.registerGlobalReceiver(NetworkConstants.CHUNK_UNCLAIM_REQUEST,
            ChunkClaimPacketHandler::handleChunkUnclaimRequest);
        ServerPlayNetworking.registerGlobalReceiver(NetworkConstants.CHUNK_TAG_UPDATE_REQUEST,
            ChunkClaimPacketHandler::handleChunkTagUpdateRequest);

        // Register client-side packet handlers
        if (Pokecobbleclaim.isClient()) {
            registerClientHandlers();
        }

        Pokecobbleclaim.LOGGER.info("Chunk claim packet handlers initialized");
    }
    
    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    private static void registerClientHandlers() {
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
            NetworkConstants.CHUNK_CLAIM_RESPONSE, ChunkClaimPacketHandler::handleChunkClaimResponse);
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
            NetworkConstants.CHUNK_UNCLAIM_RESPONSE, ChunkClaimPacketHandler::handleChunkUnclaimResponse);
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
            NetworkConstants.CHUNK_TAG_UPDATE_RESPONSE, ChunkClaimPacketHandler::handleChunkTagUpdateResponse);
    }
    
    /**
     * Sends a chunk claim request to the server.
     * This is called when the player uses the claim tool to save selected chunks.
     */
    @Environment(EnvType.CLIENT)
    public static void requestChunkClaim(Map<ChunkPos, ClaimTag> chunksToTags) {
        Pokecobbleclaim.LOGGER.info("ChunkClaimPacketHandler: requestChunkClaim called with {} chunks",
            chunksToTags != null ? chunksToTags.size() : 0);
        try {
            // Validate parameters
            if (chunksToTags == null || chunksToTags.isEmpty()) {
                Pokecobbleclaim.LOGGER.warn("Cannot send chunk claim request: no chunks to claim");
                return;
            }
            
            // Create packet buffer
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Add player UUID for validation
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player != null) {
                buf.writeUuid(client.player.getUuid());
            } else {
                Pokecobbleclaim.LOGGER.error("Cannot send chunk claim request: no player");
                return;
            }
            
            // Write number of chunks
            int chunkCount = chunksToTags.size();
            buf.writeInt(chunkCount);
            Pokecobbleclaim.LOGGER.info("CLIENT: Writing chunk count: " + chunkCount + ", buffer size: " + buf.writerIndex());

            // Write chunk data
            for (Map.Entry<ChunkPos, ClaimTag> entry : chunksToTags.entrySet()) {
                ChunkPos chunkPos = entry.getKey();
                ClaimTag claimTag = entry.getValue();

                // Write chunk position
                buf.writeInt(chunkPos.x);
                buf.writeInt(chunkPos.z);
                Pokecobbleclaim.LOGGER.debug("CLIENT: Writing chunk position: " + chunkPos);

                // Write claim tag
                writeClaimTagToBuffer(buf, claimTag);
                Pokecobbleclaim.LOGGER.debug("CLIENT: Wrote claim tag: " + claimTag.getName() + " (ID: " + claimTag.getId() + ")");
            }
            
            // Send packet to server
            Pokecobbleclaim.LOGGER.info("ChunkClaimPacketHandler: About to send CHUNK_CLAIM_REQUEST packet to server for {} chunks", chunksToTags.size());
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                NetworkConstants.CHUNK_CLAIM_REQUEST, buf);
            Pokecobbleclaim.LOGGER.info("ChunkClaimPacketHandler: Successfully sent CHUNK_CLAIM_REQUEST packet to server for {} chunks", chunksToTags.size());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending chunk claim request: " + e.getMessage(), e);
        }
    }

    /**
     * Sends a chunk tag update request to the server.
     * This is called when the player uses the claim tool to update tags on existing claimed chunks.
     */
    @Environment(EnvType.CLIENT)
    public static void requestChunkTagUpdate(UUID townId, Map<ChunkPos, ClaimTag> chunksToUpdate) {
        try {
            // Validate parameters
            if (townId == null || chunksToUpdate == null || chunksToUpdate.isEmpty()) {
                Pokecobbleclaim.LOGGER.warn("Cannot send chunk tag update request: invalid parameters");
                return;
            }

            // Create packet buffer
            PacketByteBuf buf = PacketByteBufs.create();

            // Add player UUID for validation
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player != null) {
                buf.writeUuid(client.player.getUuid());
            } else {
                Pokecobbleclaim.LOGGER.error("Cannot send chunk tag update request: no player");
                return;
            }

            // Write town ID
            buf.writeUuid(townId);

            // Write number of chunks
            int chunkCount = chunksToUpdate.size();
            buf.writeInt(chunkCount);
            Pokecobbleclaim.LOGGER.info("CLIENT: Writing chunk tag update count: " + chunkCount);

            // Write chunk data
            for (Map.Entry<ChunkPos, ClaimTag> entry : chunksToUpdate.entrySet()) {
                ChunkPos chunkPos = entry.getKey();
                ClaimTag claimTag = entry.getValue();

                // Write chunk position
                buf.writeInt(chunkPos.x);
                buf.writeInt(chunkPos.z);
                Pokecobbleclaim.LOGGER.debug("CLIENT: Writing chunk position for tag update: " + chunkPos);

                // Write claim tag
                writeClaimTagToBuffer(buf, claimTag);
                Pokecobbleclaim.LOGGER.debug("CLIENT: Wrote claim tag for update: " + claimTag.getName() + " (ID: " + claimTag.getId() + ")");
            }

            // Send packet to server
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                NetworkConstants.CHUNK_TAG_UPDATE_REQUEST, buf);

            Pokecobbleclaim.LOGGER.info("CLIENT: Sent chunk tag update request for " + chunkCount + " chunks");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending chunk tag update request: " + e.getMessage(), e);
        }
    }

    /**
     * Sends a chunk unclaim request to the server.
     * This is called when the player uses the claim tool to unclaim selected chunks.
     */
    @Environment(EnvType.CLIENT)
    public static void requestChunkUnclaim(List<ChunkPos> chunksToUnclaim) {
        try {
            // Validate parameters
            if (chunksToUnclaim == null || chunksToUnclaim.isEmpty()) {
                Pokecobbleclaim.LOGGER.warn("Cannot send chunk unclaim request: no chunks to unclaim");
                return;
            }

            // Create packet buffer
            PacketByteBuf buf = PacketByteBufs.create();

            // Add player UUID for validation
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player != null) {
                buf.writeUuid(client.player.getUuid());
            } else {
                Pokecobbleclaim.LOGGER.error("Cannot send chunk unclaim request: no player");
                return;
            }

            // Write number of chunks to unclaim
            buf.writeInt(chunksToUnclaim.size());

            // Write chunk positions
            for (ChunkPos chunkPos : chunksToUnclaim) {
                buf.writeInt(chunkPos.x);
                buf.writeInt(chunkPos.z);
            }

            // Send packet to server
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                NetworkConstants.CHUNK_UNCLAIM_REQUEST, buf);

            Pokecobbleclaim.LOGGER.debug("Sent chunk unclaim request for {} chunks", chunksToUnclaim.size());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending chunk unclaim request: " + e.getMessage(), e);
        }
    }
    
    /**
     * Handles chunk claim requests from clients on the server side.
     */
    private static void handleChunkClaimRequest(MinecraftServer server, ServerPlayerEntity player,
                                               ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                               PacketSender responseSender) {
        try {
            Pokecobbleclaim.LOGGER.error("=== CHUNK CLAIM HANDLER CALLED ===");
            Pokecobbleclaim.LOGGER.error("ChunkClaimPacketHandler: handleChunkClaimRequest called for player {}",
                player != null ? player.getName().getString() : "null");
            Pokecobbleclaim.LOGGER.error("=== CHUNK CLAIM HANDLER CALLED ===");

            // Validate packet size
            PacketValidator.validatePacketSize(buf);
            
            // Read player UUID
            UUID playerId = buf.readUuid();
            
            // Validate that the requesting player matches the packet sender
            if (!playerId.equals(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player UUID mismatch in chunk claim request");
                sendChunkClaimResponse(player, false, "Invalid player", null);
                return;
            }
            
            // Read number of chunks
            int chunkCount = buf.readInt();
            Pokecobbleclaim.LOGGER.info("SERVER: Reading chunk count: " + chunkCount + ", readable bytes: " + buf.readableBytes());

            // Validate chunk count using PacketValidator
            try {
                com.pokecobble.town.network.PacketValidator.validateListSize(chunkCount, 64);
            } catch (IllegalArgumentException e) {
                Pokecobbleclaim.LOGGER.error("Invalid chunk count in claim request: " + chunkCount + " - " + e.getMessage());
                sendChunkClaimResponse(player, false, "Invalid chunk count: " + e.getMessage(), null);
                return;
            }
            
            // Read chunk data
            Map<ChunkPos, ClaimTag> chunksToTags = new HashMap<>();
            try {
                for (int i = 0; i < chunkCount; i++) {
                    // Read chunk position
                    int chunkX = buf.readInt();
                    int chunkZ = buf.readInt();
                    ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);
                    Pokecobbleclaim.LOGGER.debug("SERVER: Reading chunk position: " + chunkPos);

                    // Read claim tag
                    ClaimTag claimTag = readClaimTagFromBuffer(buf);
                    if (claimTag == null) {
                        Pokecobbleclaim.LOGGER.error("SERVER: Failed to read claim tag for chunk: " + chunkPos + " - skipping this chunk");
                        continue; // Skip this chunk and continue with the next one
                    }
                    Pokecobbleclaim.LOGGER.debug("SERVER: Successfully read claim tag for chunk: " + chunkPos);

                    chunksToTags.put(chunkPos, claimTag);
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error reading chunk data: " + e.getMessage(), e);
                sendChunkClaimResponse(player, false, "Error reading chunk data: " + e.getMessage(), null);
                return;
            }
            
            // Get player's town
            com.pokecobble.town.Town playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(player.getUuid());
            if (playerTown == null) {
                sendChunkClaimResponse(player, false, "You must be in a town to claim chunks.", null);
                return;
            }

            // Process the claim request using the new system with player information for history
            Pokecobbleclaim.LOGGER.info("ChunkClaimPacketHandler: Processing claim request for {} chunks by player {} in town {}",
                chunksToTags.size(), player.getName().getString(), playerTown.getName());
            Pokecobbleclaim.LOGGER.info("ChunkClaimPacketHandler: About to call ServerTownChunkRegistry.claimChunksForTown with playerId={}, playerName={}",
                player.getUuid(), player.getName().getString());
            ServerTownChunkRegistry.ClaimResult result = ServerTownChunkRegistry.getInstance()
                .claimChunksForTown(playerTown.getId(), chunksToTags.keySet(), chunksToTags, player.getServer(),
                                  player.getUuid(), player.getName().getString());
            Pokecobbleclaim.LOGGER.info("ChunkClaimPacketHandler: ServerTownChunkRegistry.claimChunksForTown returned: success={}, message={}",
                result.success, result.message);

            // Send response
            sendChunkClaimResponse(player, result.success, result.message,
                result.success ? chunksToTags.keySet().toArray(new ChunkPos[0]) : null);

            Pokecobbleclaim.LOGGER.info("Processed chunk claim request from {}: {} - {}",
                player.getName().getString(), result.success ? "SUCCESS" : "FAILED", result.message);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("ChunkClaimPacketHandler: EXCEPTION in handleChunkClaimRequest: " + e.getMessage(), e);
            Pokecobbleclaim.LOGGER.error("ChunkClaimPacketHandler: Exception occurred for player: {}",
                player != null ? player.getName().getString() : "null");
            sendChunkClaimResponse(player, false, "Server error", null);
        }
    }

    /**
     * Handles chunk unclaim requests from clients on the server side.
     */
    private static void handleChunkUnclaimRequest(MinecraftServer server, ServerPlayerEntity player,
                                                 ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                                 PacketSender responseSender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID
            UUID playerId = buf.readUuid();

            // Validate that the requesting player matches the packet sender
            if (!playerId.equals(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player UUID mismatch in chunk unclaim request");
                sendChunkUnclaimResponse(player, false, "Invalid player", null);
                return;
            }

            // Read number of chunks
            int chunkCount = buf.readInt();
            Pokecobbleclaim.LOGGER.info("SERVER: Reading unclaim chunk count: " + chunkCount + ", readable bytes: " + buf.readableBytes());

            // Validate chunk count using PacketValidator
            try {
                com.pokecobble.town.network.PacketValidator.validateListSize(chunkCount, 64);
            } catch (IllegalArgumentException e) {
                Pokecobbleclaim.LOGGER.error("Invalid chunk count in unclaim request: " + chunkCount + " - " + e.getMessage());
                sendChunkUnclaimResponse(player, false, "Invalid chunk count: " + e.getMessage(), null);
                return;
            }

            // Read chunk positions
            List<ChunkPos> chunksToUnclaim = new ArrayList<>();
            try {
                for (int i = 0; i < chunkCount; i++) {
                    // Read chunk position
                    int chunkX = buf.readInt();
                    int chunkZ = buf.readInt();
                    ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);
                    Pokecobbleclaim.LOGGER.debug("SERVER: Reading unclaim chunk position: " + chunkPos);

                    chunksToUnclaim.add(chunkPos);
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error reading unclaim chunk data: " + e.getMessage(), e);
                sendChunkUnclaimResponse(player, false, "Error reading chunk data: " + e.getMessage(), null);
                return;
            }

            // Process the unclaim requests
            List<ChunkPos> successfullyUnclaimed = new ArrayList<>();
            StringBuilder errorMessages = new StringBuilder();

            // Get player's town
            com.pokecobble.town.Town playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(player.getUuid());
            if (playerTown == null) {
                sendChunkUnclaimResponse(player, false, "You must be in a town to unclaim chunks.", null);
                return;
            }



            // Process unclaim using the new system (batch operation) with player information for history
            Pokecobbleclaim.LOGGER.info("ChunkClaimPacketHandler: Processing unclaim request for {} chunks by player {} in town {}",
                chunksToUnclaim.size(), player.getName().getString(), playerTown.getName());
            ServerTownChunkRegistry.ClaimResult result = ServerTownChunkRegistry.getInstance()
                .unclaimChunksFromTown(playerTown.getId(), chunksToUnclaim, player.getServer(),
                                     player.getUuid(), player.getName().getString());

            if (result.success) {
                successfullyUnclaimed.addAll(chunksToUnclaim);
            } else {
                errorMessages.append(result.message);
            }

            // Sync updated town data if any chunks were successfully unclaimed
            if (!successfullyUnclaimed.isEmpty()) {
                // Sync updated town data to all players (including updated claim count)
                com.pokecobble.town.network.town.TownDataSynchronizer.syncTownData(player.getServer(), playerTown);
            }

            // Send response
            if (!successfullyUnclaimed.isEmpty()) {
                String message = String.format("Successfully unclaimed %d chunks.", successfullyUnclaimed.size());
                if (errorMessages.length() > 0) {
                    message += " Errors: " + errorMessages.toString();
                }
                sendChunkUnclaimResponse(player, true, message,
                    successfullyUnclaimed.toArray(new ChunkPos[0]));
            } else {
                sendChunkUnclaimResponse(player, false,
                    errorMessages.length() > 0 ? errorMessages.toString() : "No chunks were unclaimed", null);
            }

            Pokecobbleclaim.LOGGER.info("Processed chunk unclaim request from {}: {} chunks unclaimed",
                player.getName().getString(), successfullyUnclaimed.size());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chunk unclaim request: " + e.getMessage(), e);
            sendChunkUnclaimResponse(player, false, "Server error", null);
        }
    }

    /**
     * Sends a chunk claim response to a player.
     */
    private static void sendChunkClaimResponse(ServerPlayerEntity player, boolean success, 
                                              String message, ChunkPos[] claimedChunks) {
        try {
            PacketByteBuf responseBuf = PacketByteBufs.create();
            responseBuf.writeBoolean(success);
            responseBuf.writeString(message);
            
            // Write claimed chunks if successful
            if (success && claimedChunks != null) {
                responseBuf.writeInt(claimedChunks.length);
                for (ChunkPos chunkPos : claimedChunks) {
                    responseBuf.writeInt(chunkPos.x);
                    responseBuf.writeInt(chunkPos.z);
                }
            } else {
                responseBuf.writeInt(0);
            }
            
            ServerPlayNetworking.send(player, NetworkConstants.CHUNK_CLAIM_RESPONSE, responseBuf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending chunk claim response: " + e.getMessage(), e);
        }
    }

    /**
     * Sends a chunk unclaim response to a player.
     */
    private static void sendChunkUnclaimResponse(ServerPlayerEntity player, boolean success,
                                                String message, ChunkPos[] unclaimedChunks) {
        try {
            PacketByteBuf responseBuf = PacketByteBufs.create();
            responseBuf.writeBoolean(success);
            responseBuf.writeString(message);

            // Write unclaimed chunks if successful
            if (success && unclaimedChunks != null) {
                responseBuf.writeInt(unclaimedChunks.length);
                for (ChunkPos chunkPos : unclaimedChunks) {
                    responseBuf.writeInt(chunkPos.x);
                    responseBuf.writeInt(chunkPos.z);
                }
            } else {
                responseBuf.writeInt(0);
            }

            ServerPlayNetworking.send(player, NetworkConstants.CHUNK_UNCLAIM_RESPONSE, responseBuf);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending chunk unclaim response: " + e.getMessage(), e);
        }
    }

    /**
     * Sends a chunk tag update response to a player.
     */
    private static void sendChunkTagUpdateResponse(ServerPlayerEntity player, boolean success, String message) {
        try {
            PacketByteBuf responseBuf = PacketByteBufs.create();
            responseBuf.writeBoolean(success);
            responseBuf.writeString(message);

            ServerPlayNetworking.send(player, NetworkConstants.CHUNK_TAG_UPDATE_RESPONSE, responseBuf);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending chunk tag update response: " + e.getMessage(), e);
        }
    }

    /**
     * Handles chunk claim responses on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleChunkClaimResponse(MinecraftClient client, 
                                                net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                                PacketByteBuf buf, PacketSender responseSender) {
        try {
            boolean success = buf.readBoolean();
            String message = buf.readString();
            
            // Read claimed chunks
            int claimedCount = buf.readInt();
            ChunkPos[] claimedChunks = new ChunkPos[claimedCount];
            for (int i = 0; i < claimedCount; i++) {
                int chunkX = buf.readInt();
                int chunkZ = buf.readInt();
                claimedChunks[i] = new ChunkPos(chunkX, chunkZ);
            }
            
            if (success) {
                Pokecobbleclaim.LOGGER.info("Chunk claim successful: " + message);

                // Schedule the UI updates for the next tick to ensure chunk synchronization packets
                // are processed first, avoiding race conditions
                client.execute(() -> {
                    // Update local claim tool state
                    if (client.player != null) {
                        com.pokecobble.town.claim.ClaimTool claimTool = com.pokecobble.town.claim.ClaimTool.getInstance();
                        if (claimTool != null) {
                            // Get the player's town for immediate updates
                            Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();

                            // IMMEDIATELY update the claim tool with the newly claimed chunks
                            if (playerTown != null && claimTool.isActive()) {
                                for (ChunkPos chunkPos : claimedChunks) {
                                    // Mark chunk as claimed by the player's town
                                    // Note: Chunk claim status is now managed by ClientTownChunkManager and GlobalChunkClaimRegistry
                                    Pokecobbleclaim.LOGGER.info("Immediately updated claim tool: chunk {} now claimed by {}",
                                        chunkPos, playerTown.getName());
                                }
                            }

                            // Note: clearNewSelections will be called after chunk data is updated
                        }

                        // Update town data to reflect new claim count (use client-side town manager)
                        Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
                        if (playerTown != null) {
                            // Fire town update event to refresh MyTownScreen
                            com.pokecobble.town.client.ClientSyncManager.getInstance().onTownUpdated(playerTown.getId(),
                                playerTown.getDataVersion() + 1);

                            // Request updated town data from server to ensure client has latest data
                            com.pokecobble.town.network.town.TownNetworkHandler.requestTownData();

                            // No need to request claim history - handled by SimpleClaimHistoryManager

                            // Request updated chunk claim data to ensure synchronization
                            com.pokecobble.town.network.chunk.ChunkClaimSyncHandler.requestChunkClaimSync(playerTown.getId());

                            // Claim count will be synced by SimpleClaimCountSync from server

                            // Refresh MyTownScreen claims subcategory if it's currently open
                            refreshMyTownScreenClaimsSubcategory();
                        } else {
                            // If we can't get the player town from client cache, request fresh data from server
                            Pokecobbleclaim.LOGGER.warn("Player town not found in client cache after chunk claim, requesting fresh data");
                            com.pokecobble.town.network.town.TownNetworkHandler.requestTownData();
                        }

                        // Fire chunk update events for all claimed chunks
                        for (ChunkPos chunkPos : claimedChunks) {
                            com.pokecobble.town.client.ClientSyncManager.getInstance().onChunkUpdated(chunkPos, 1);
                        }

                        // Request fresh town chunk data from server to update the optimized system
                        // Add a small delay to ensure server has finished processing
                        MinecraftClient.getInstance().execute(() -> {
                            try {
                                Thread.sleep(100); // Small delay to ensure server sync completes
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }

                            com.pokecobble.town.client.ClientTownManager clientTownManager =
                                com.pokecobble.town.client.ClientTownManager.getInstance();
                            UUID playerTownId = clientTownManager.getPlayerTownId();
                            if (playerTownId != null) {
                                // Update the ClientTownChunkManager cache (without requesting new data)
                                com.pokecobble.town.client.ClientTownChunkManager chunkManager =
                                    com.pokecobble.town.client.ClientTownChunkManager.getInstance();
                                chunkManager.updateTownChunkCache(playerTownId);

                                // Now that chunk data is updated, clear only new selections while keeping claimed chunks selected
                                com.pokecobble.town.claim.ClaimTool claimToolInstance = com.pokecobble.town.claim.ClaimTool.getInstance();
                                if (claimToolInstance.isActive()) {
                                    Set<ChunkPos> newlyClaimedSet = new HashSet<>(Arrays.asList(claimedChunks));
                                    claimToolInstance.clearNewSelections(newlyClaimedSet);
                                    Pokecobbleclaim.LOGGER.debug("CLIENT: Cleared new selections after chunk data update, keeping newly claimed chunks selected");
                                }
                            }
                        });

                        // Force refresh of UI components
                        com.pokecobble.ui.UIDataRefreshManager.getInstance().forceRefreshAll();

                        // Force refresh of the claim tool HUD
                        com.pokecobble.town.client.ModernClaimToolHud.requestRefresh();
                    }
                });

                // Show success notification
                com.pokecobble.town.client.NotificationRenderer.addSuccessNotification(message);
            } else {
                Pokecobbleclaim.LOGGER.warn("Chunk claim failed: " + message);
                
                // Show error notification
                com.pokecobble.town.client.NotificationRenderer.addErrorNotification(message);
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chunk claim response: " + e.getMessage(), e);
        }
    }

    /**
     * Handles chunk unclaim responses on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleChunkUnclaimResponse(MinecraftClient client,
                                                  net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                                  PacketByteBuf buf, PacketSender responseSender) {
        try {
            boolean success = buf.readBoolean();
            String message = buf.readString();

            // Read unclaimed chunks
            int unclaimedCount = buf.readInt();
            ChunkPos[] unclaimedChunks = new ChunkPos[unclaimedCount];
            for (int i = 0; i < unclaimedCount; i++) {
                int chunkX = buf.readInt();
                int chunkZ = buf.readInt();
                unclaimedChunks[i] = new ChunkPos(chunkX, chunkZ);
            }

            if (success) {
                Pokecobbleclaim.LOGGER.info("Chunk unclaim successful: " + message);

                // Schedule the UI updates for the next tick to ensure chunk synchronization packets
                // are processed first, avoiding race conditions
                client.execute(() -> {
                    // Update local claim tool state
                    if (client.player != null) {
                        com.pokecobble.town.claim.ClaimTool claimTool = com.pokecobble.town.claim.ClaimTool.getInstance();

                        // Clear the claim tool's selection state after successful unclaim
                        // This ensures the tool reflects the new state after unclaiming
                        claimTool.clearSelection();

                        // Note: Chunk unclaim status is now managed by ClientTownChunkManager and GlobalChunkClaimRegistry
                        // No need to manually clear chunks here

                        // Get the player's town for history updates
                        Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
                        if (playerTown != null) {
                            // No need to request claim history - handled by SimpleClaimHistoryManager

                            // Request updated chunk claim data to ensure synchronization
                            com.pokecobble.town.network.chunk.ChunkClaimSyncHandler.requestChunkClaimSync(playerTown.getId());

                            // Claim count will be synced by SimpleClaimCountSync from server

                            // Refresh MyTownScreen claims subcategory if it's currently open
                            refreshMyTownScreenClaimsSubcategory();
                        } else {
                            // If we can't get the player town from client cache, request fresh data from server
                            Pokecobbleclaim.LOGGER.warn("Player town not found in client cache after chunk unclaim, requesting fresh data");
                            com.pokecobble.town.network.town.TownNetworkHandler.requestTownData();
                        }

                        // Fire chunk update events for all unclaimed chunks
                        for (ChunkPos chunkPos : unclaimedChunks) {
                            com.pokecobble.town.client.ClientSyncManager.getInstance().onChunkUpdated(chunkPos, 1);
                        }

                        // Request fresh town chunk data from server to update the optimized system
                        // Add a small delay to ensure server has finished processing
                        MinecraftClient.getInstance().execute(() -> {
                            try {
                                Thread.sleep(100); // Small delay to ensure server sync completes
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }

                            com.pokecobble.town.client.ClientTownManager clientTownManager =
                                com.pokecobble.town.client.ClientTownManager.getInstance();
                            UUID playerTownId = clientTownManager.getPlayerTownId();
                            if (playerTownId != null) {
                                Pokecobbleclaim.LOGGER.info("CLIENT: Requesting fresh chunk data after successful unclaim for town {}", playerTownId);
                                com.pokecobble.town.network.chunk.ClientTownChunkSyncHandler.requestTownChunkSync(playerTownId);

                                // Also update the ClientTownChunkManager
                                com.pokecobble.town.client.ClientTownChunkManager chunkManager =
                                    com.pokecobble.town.client.ClientTownChunkManager.getInstance();
                                chunkManager.updateTownChunkCache(playerTownId);
                            }
                        });

                        // Force refresh of UI components
                        com.pokecobble.ui.UIDataRefreshManager.getInstance().forceRefreshAll();

                        // Force refresh of the claim tool HUD
                        com.pokecobble.town.client.ModernClaimToolHud.requestRefresh();
                    }
                });

                // Show success notification
                com.pokecobble.town.client.NotificationRenderer.addSuccessNotification(message);
            } else {
                Pokecobbleclaim.LOGGER.warn("Chunk unclaim failed: " + message);

                // Show error notification
                com.pokecobble.town.client.NotificationRenderer.addErrorNotification(message);
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chunk unclaim response: " + e.getMessage(), e);
        }
    }

    /**
     * Handles chunk tag update responses on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleChunkTagUpdateResponse(MinecraftClient client,
                                                    net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                                    PacketByteBuf buf, PacketSender responseSender) {
        try {
            boolean success = buf.readBoolean();
            String message = buf.readString();

            Pokecobbleclaim.LOGGER.info("CLIENT: Received chunk tag update response: {} - {}",
                success ? "SUCCESS" : "FAILED", message);

            // Show notification to player
            if (success) {
                com.pokecobble.town.client.NotificationRenderer.addSuccessNotification(message);

                // Force refresh of chunk data after successful tag update
                client.execute(() -> {
                    try {
                        // Request fresh chunk data from server to ensure client is in sync
                        Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
                        if (playerTown != null) {
                            Pokecobbleclaim.LOGGER.info("Requesting fresh chunk data after successful tag update");
                            com.pokecobble.town.network.chunk.ChunkClaimSyncHandler.requestChunkClaimSync(playerTown.getId());
                        }

                        // Force refresh of UI components
                        com.pokecobble.ui.UIDataRefreshManager.getInstance().forceRefreshAll();

                    } catch (Exception e) {
                        Pokecobbleclaim.LOGGER.error("Error refreshing chunk data after tag update: " + e.getMessage(), e);
                    }
                });
            } else {
                com.pokecobble.town.client.NotificationRenderer.addErrorNotification(message);
            }

            // If successful, request updated chunk data to refresh the claim tool
            if (success) {
                client.execute(() -> {
                    if (client.player != null) {
                        Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
                        if (playerTown != null) {
                            // Request updated chunk claim data to ensure synchronization
                            com.pokecobble.town.network.chunk.ChunkClaimSyncHandler.requestChunkClaimSync(playerTown.getId());

                            // Force refresh of UI components
                            com.pokecobble.ui.UIDataRefreshManager.getInstance().forceRefreshAll();

                            // Force refresh of the claim tool HUD
                            com.pokecobble.town.client.ModernClaimToolHud.requestRefresh();

                            // Refresh MyTownScreen claims subcategory if it's currently open
                            refreshMyTownScreenClaimsSubcategory();
                        }
                    }
                });
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chunk tag update response: " + e.getMessage(), e);
        }
    }

    /**
     * Handles chunk tag update requests from clients on the server side.
     */
    private static void handleChunkTagUpdateRequest(MinecraftServer server, ServerPlayerEntity player,
                                                   ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                                   PacketSender responseSender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID
            UUID playerId = buf.readUuid();

            // Validate that the requesting player matches the packet sender
            if (!playerId.equals(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player UUID mismatch in chunk tag update request");
                sendChunkTagUpdateResponse(player, false, "Invalid player");
                return;
            }

            // Read town ID
            UUID townId = buf.readUuid();

            // Read number of chunks
            int chunkCount = buf.readInt();
            Pokecobbleclaim.LOGGER.info("SERVER: Processing chunk tag update request for " + chunkCount + " chunks");

            // Read chunk data
            Map<ChunkPos, ClaimTag> chunksToUpdate = new HashMap<>();
            for (int i = 0; i < chunkCount; i++) {
                int chunkX = buf.readInt();
                int chunkZ = buf.readInt();
                ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);

                ClaimTag claimTag = readClaimTagFromBuffer(buf);
                if (claimTag == null) {
                    Pokecobbleclaim.LOGGER.error("SERVER: Failed to read claim tag for chunk update: " + chunkPos + " - skipping this chunk");
                    continue; // Skip this chunk and continue with the next one
                }
                chunksToUpdate.put(chunkPos, claimTag);

                Pokecobbleclaim.LOGGER.debug("SERVER: Read chunk tag update: " + chunkPos + " tag: " + claimTag.getName());
            }

            // Get player's town
            com.pokecobble.town.Town playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(player.getUuid());
            if (playerTown == null) {
                sendChunkTagUpdateResponse(player, false, "You must be in a town to update chunk tags.");
                return;
            }

            // Verify the town ID matches
            if (!playerTown.getId().equals(townId)) {
                sendChunkTagUpdateResponse(player, false, "Town ID mismatch.");
                return;
            }

            // Debug logging
            Pokecobbleclaim.LOGGER.info("=== SERVER CHUNK TAG UPDATE DEBUG ===");
            Pokecobbleclaim.LOGGER.info("Player: {}", player.getName().getString());
            Pokecobbleclaim.LOGGER.info("Town: {} ({})", playerTown.getName(), townId);
            Pokecobbleclaim.LOGGER.info("Chunks to update: {}", chunksToUpdate.size());
            for (Map.Entry<ChunkPos, ClaimTag> entry : chunksToUpdate.entrySet()) {
                Pokecobbleclaim.LOGGER.info("  Chunk: {} -> Tag: {}", entry.getKey(), entry.getValue().getName());
            }



            // Update chunk tags using the registry with player information for history
            Pokecobbleclaim.LOGGER.info("ChunkClaimPacketHandler: Processing tag update request for {} chunks by player {} in town {}",
                chunksToUpdate.size(), player.getName().getString(), playerTown.getName());
            boolean success = com.pokecobble.town.chunk.ServerTownChunkRegistry.getInstance()
                .updateTownChunkTags(townId, chunksToUpdate, server, player.getUuid(), player.getName().getString());

            Pokecobbleclaim.LOGGER.info("Update result: {}", success);
            Pokecobbleclaim.LOGGER.info("=== END SERVER CHUNK TAG UPDATE DEBUG ===");

            if (success) {
                sendChunkTagUpdateResponse(player, true, "Chunk tags updated successfully.");
                Pokecobbleclaim.LOGGER.info("Updated tags for " + chunksToUpdate.size() + " chunks for town " + playerTown.getName());
            } else {
                sendChunkTagUpdateResponse(player, false, "Failed to update chunk tags.");
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chunk tag update request: " + e.getMessage(), e);
            sendChunkTagUpdateResponse(player, false, "Server error occurred.");
        }
    }

    /**
     * Writes a ClaimTag to a packet buffer.
     */
    private static void writeClaimTagToBuffer(PacketByteBuf buf, ClaimTag tag) {
        int startSize = buf.writerIndex();
        Pokecobbleclaim.LOGGER.debug("CLIENT: Starting to write claim tag '" + tag.getName() + "', buffer size: " + startSize);

        buf.writeUuid(tag.getId());
        Pokecobbleclaim.LOGGER.debug("CLIENT: Wrote UUID, buffer size: " + buf.writerIndex());

        buf.writeString(tag.getName(), com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
        Pokecobbleclaim.LOGGER.debug("CLIENT: Wrote name '" + tag.getName() + "', buffer size: " + buf.writerIndex());

        buf.writeString(tag.getDescription(), com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
        Pokecobbleclaim.LOGGER.debug("CLIENT: Wrote description '" + tag.getDescription() + "', buffer size: " + buf.writerIndex());

        buf.writeInt(tag.getColor());
        Pokecobbleclaim.LOGGER.debug("CLIENT: Wrote color " + tag.getColor() + ", buffer size: " + buf.writerIndex());

        // Write rank permissions
        int rankCount = com.pokecobble.town.TownPlayerRank.values().length;
        Pokecobbleclaim.LOGGER.debug("CLIENT: About to write permissions for " + rankCount + " ranks, buffer size: " + buf.writerIndex());

        for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
            boolean[] permissions = tag.getRankPermissions().getPermissions(rank);
            Pokecobbleclaim.LOGGER.debug("CLIENT: Writing permissions for rank " + rank + ", permissions length: " + permissions.length + ", buffer size: " + buf.writerIndex());
            for (boolean permission : permissions) {
                buf.writeBoolean(permission);
            }
        }

        // Write non-member permissions
        boolean[] nonMemberPermissions = tag.getRankPermissions().getPermissions(null);
        Pokecobbleclaim.LOGGER.debug("CLIENT: Writing non-member permissions, length: " + nonMemberPermissions.length + ", buffer size: " + buf.writerIndex());
        for (boolean permission : nonMemberPermissions) {
            buf.writeBoolean(permission);
        }

        int finalSize = buf.writerIndex();
        int totalBytesWritten = finalSize - startSize;

        // Calculate expected size for verification (Note: This is approximate due to VarInt encoding)
        int expectedSize = 16; // UUID
        // Strings use VarInt for length encoding (1-5 bytes), not fixed 4 bytes
        // For short strings (< 128 chars), VarInt is typically 1 byte
        byte[] nameBytes = tag.getName().getBytes(java.nio.charset.StandardCharsets.UTF_8);
        byte[] descBytes = tag.getDescription().getBytes(java.nio.charset.StandardCharsets.UTF_8);
        expectedSize += getVarIntSize(nameBytes.length) + nameBytes.length; // String with VarInt length prefix
        expectedSize += getVarIntSize(descBytes.length) + descBytes.length; // String with VarInt length prefix
        expectedSize += 4; // Color int
        expectedSize += (com.pokecobble.town.TownPlayerRank.values().length * 8) + 8; // Permissions (6 ranks * 8 + 8 non-member = 56 booleans)

        Pokecobbleclaim.LOGGER.debug("CLIENT: Finished writing claim tag '" + tag.getName() + "', total bytes written: " + totalBytesWritten + ", expected: " + expectedSize + ", final buffer size: " + finalSize);

        if (totalBytesWritten != expectedSize) {
            Pokecobbleclaim.LOGGER.debug("CLIENT: Size difference: " + totalBytesWritten + " written vs " + expectedSize + " expected (difference: " + (totalBytesWritten - expectedSize) + ") - This is normal due to VarInt encoding");
        }
    }

    /**
     * Calculates the size of a VarInt encoding for the given value.
     * VarInts use 1-5 bytes depending on the value.
     */
    private static int getVarIntSize(int value) {
        if (value < 0) return 5; // Negative numbers always use 5 bytes
        if (value < 128) return 1;
        if (value < 16384) return 2;
        if (value < 2097152) return 3;
        if (value < 268435456) return 4;
        return 5;
    }



    /**
     * Reads a ClaimTag from a packet buffer.
     * FIXED: Now properly preserves UUID to maintain tag identity across network sync.
     * Enhanced with buffer underrun protection for backward compatibility.
     */
    private static ClaimTag readClaimTagFromBuffer(PacketByteBuf buf) {
        try {
            int initialBytes = buf.readableBytes();
            Pokecobbleclaim.LOGGER.debug("SERVER: Starting to read claim tag, buffer readable bytes: " + initialBytes);

            // Calculate expected minimum size
            // UUID (16) + 2 strings (variable) + int (4) + permissions (6 ranks * 8 + 8 non-member = 56 booleans)
            int expectedPermissionBytes = (com.pokecobble.town.TownPlayerRank.values().length * 8) + 8;
            Pokecobbleclaim.LOGGER.debug("SERVER: Expected permission bytes: " + expectedPermissionBytes);

            if (buf.readableBytes() < 20) { // Minimum for UUID + int
                Pokecobbleclaim.LOGGER.error("SERVER: Buffer too small to contain basic claim tag data. Available: " + buf.readableBytes() + " bytes");
                return null;
            }

            UUID id = buf.readUuid();
            Pokecobbleclaim.LOGGER.debug("SERVER: Read tag UUID: " + id + ", remaining bytes: " + buf.readableBytes());

            String name = buf.readString(com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
            Pokecobbleclaim.LOGGER.debug("SERVER: Read tag name: '" + name + "' (length: " + name.length() + "), remaining bytes: " + buf.readableBytes());

            String description = buf.readString(com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
            Pokecobbleclaim.LOGGER.debug("SERVER: Read tag description: '" + description + "' (length: " + description.length() + "), remaining bytes: " + buf.readableBytes());

            int color = buf.readInt();
            Pokecobbleclaim.LOGGER.debug("SERVER: Read tag color: " + color + ", remaining bytes: " + buf.readableBytes());

            // Check if we have enough bytes for permissions
            if (buf.readableBytes() < expectedPermissionBytes) {
                Pokecobbleclaim.LOGGER.error("SERVER: Not enough bytes for permissions. Expected: " + expectedPermissionBytes + ", available: " + buf.readableBytes());
                Pokecobbleclaim.LOGGER.error("SERVER: This suggests a format mismatch - data may have been written in v2 efficient format but being read in standard format");
                // Try to create a tag with default permissions
                return new ClaimTag(id, name, description, color);
            }

            // CRITICAL FIX: Use the UUID constructor to preserve tag identity
            ClaimTag tag = new ClaimTag(id, name, description, color);

            // Read rank permissions
            int rankCount = com.pokecobble.town.TownPlayerRank.values().length;
            Pokecobbleclaim.LOGGER.debug("SERVER: About to read permissions for " + rankCount + " ranks, remaining bytes: " + buf.readableBytes());

            for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
                Pokecobbleclaim.LOGGER.debug("SERVER: Reading permissions for rank " + rank + ", remaining bytes: " + buf.readableBytes());
                for (int i = 0; i < 8; i++) {
                    if (buf.readableBytes() < 1) {
                        Pokecobbleclaim.LOGGER.warn("SERVER: Buffer underrun while reading rank permissions for tag: " + name + " at rank " + rank + " permission " + i + ". Using default permissions.");
                        return tag; // Return tag with default permissions
                    }
                    boolean permission = buf.readBoolean();
                    tag.getRankPermissions().setPermission(rank, i, permission);
                }
            }

            // Read non-member permissions (with backward compatibility check)
            Pokecobbleclaim.LOGGER.debug("SERVER: About to read non-member permissions, remaining bytes: " + buf.readableBytes());
            for (int i = 0; i < 8; i++) {
                if (buf.readableBytes() < 1) {
                    Pokecobbleclaim.LOGGER.warn("SERVER: Buffer underrun while reading non-member permissions for tag: " + name + " at permission " + i + ". Using default permissions. Remaining bytes: " + buf.readableBytes());
                    return tag; // Return tag with default non-member permissions
                }
                boolean permission = buf.readBoolean();
                tag.getRankPermissions().setPermission(null, i, permission);
            }

            Pokecobbleclaim.LOGGER.debug("SERVER: Successfully read claim tag: " + name + ", final remaining bytes: " + buf.readableBytes());
            return tag;

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error reading claim tag from buffer: " + e.getMessage() + ", remaining bytes: " + buf.readableBytes(), e);
            // Instead of throwing, return null to prevent crashes
            return null;
        }
    }

    /**
     * Updates the client town cache immediately after claim operations to ensure UI shows correct data.
     * This provides immediate feedback while waiting for server synchronization.
     */
    @Environment(EnvType.CLIENT)
    private static void updateClientTownCacheAfterClaimOperation(Town town, int claimCountChange) {
        try {
            if (town != null) {
                // Update the claim count in the local town object
                int oldClaimCount = town.getClaimCount();
                int newClaimCount = Math.max(0, oldClaimCount + claimCountChange);
                town.setClaimCount(newClaimCount);

                Pokecobbleclaim.LOGGER.info("CLIENT: Updated claim count for town {} from {} to {} (change: {})",
                    town.getName(), oldClaimCount, newClaimCount, claimCountChange);

                // Update the client town manager cache
                com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(town, town.getDataVersion() + 1);

                // Fire town update event to refresh UI components
                com.pokecobble.town.client.ClientSyncManager.getInstance().onTownUpdated(town.getId(), town.getDataVersion());

                Pokecobbleclaim.LOGGER.debug("Updated client town cache: {} claims -> {} claims",
                    town.getClaimCount() - claimCountChange, newClaimCount);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating client town cache after claim operation: " + e.getMessage(), e);
        }
    }

    /**
     * Refreshes the MyTownScreen claims subcategory if it's currently open.
     * This ensures the claim history and claim counter are updated immediately after claim operations.
     */
    @Environment(EnvType.CLIENT)
    private static void refreshMyTownScreenClaimsSubcategory() {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.currentScreen instanceof com.pokecobble.town.gui.MyTownScreen) {
                com.pokecobble.town.gui.MyTownScreen myTownScreen =
                    (com.pokecobble.town.gui.MyTownScreen) client.currentScreen;

                // Use the specific claims data refresh method for better performance
                myTownScreen.refreshClaimsData();

                Pokecobbleclaim.LOGGER.debug("Refreshed MyTownScreen claims subcategory after claim operation");
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error refreshing MyTownScreen claims subcategory: " + e.getMessage(), e);
        }
    }
}
