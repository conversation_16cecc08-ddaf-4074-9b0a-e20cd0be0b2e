package com.pokecobble.town.network.chunk;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.PacketValidator;
import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.math.ChunkPos;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Simple chunk permission verification system.
 * Verifies if a player can perform actions in chunks based on their town rank and claim tag settings.
 */
public class ClaimTagSyncHandler {

    // Rate limiting for claim tag requests (per player per town)
    private static final Map<String, Long> lastTagRequestTime = new ConcurrentHashMap<>();
    private static final long TAG_REQUEST_COOLDOWN_MS = 2000; // 2 second cooldown

    // Permission types - these map directly to the action indices in ClaimTag
    public static final int PERMISSION_BUILD = 0;      // Building, breaking blocks
    public static final int PERMISSION_INTERACT = 1;   // Doors, buttons, levers
    public static final int PERMISSION_CONTAINERS = 2; // Chests, furnaces, etc.
    public static final int PERMISSION_REDSTONE = 3;   // Redstone components
    public static final int PERMISSION_DOORS = 4;      // Door access
    public static final int PERMISSION_CROPS = 5;      // Farming
    public static final int PERMISSION_ANIMALS = 6;    // Animal interaction
    public static final int PERMISSION_VILLAGERS = 7;  // Villager trading

    /**
     * Main permission verification method.
     * This is the single entry point for all permission checks.
     *
     * @param player The player attempting the action
     * @param chunkPos The chunk position where the action is taking place
     * @param permissionType The type of permission required (use PERMISSION_* constants)
     * @return true if the action is allowed, false otherwise
     */
    public static boolean hasPermission(ServerPlayerEntity player, ChunkPos chunkPos, int permissionType) {
        if (player == null || chunkPos == null) {
            return false;
        }

        try {
            // Get chunk ownership
            com.pokecobble.town.chunk.TownChunkDataManager dataManager =
                com.pokecobble.town.chunk.TownChunkDataManager.getInstance();
            UUID owningTownId = dataManager.getChunkOwner(chunkPos);

            if (owningTownId == null) {
                // Unclaimed chunk - allow all actions
                return true;
            }

            // Get the town
            Town town = TownManager.getInstance().getTown(owningTownId);
            if (town == null) {
                // Town not found - allow action (shouldn't happen)
                return true;
            }

            // Check if player is a member of the town
            boolean isTownMember = town.getPlayers().contains(player.getUuid());

            if (isTownMember) {
                // Player is a town member - check rank-based permissions
                TownPlayerRank playerRank = town.getPlayerRank(player.getUuid());

                // Admin ranks always have all permissions
                if (playerRank != null && playerRank.hasAdminPermissions()) {
                    return true;
                }

                // Get the claim tag for this chunk using the CORRECT source of truth
                ClaimTag chunkTag = getChunkTagFromCorrectSource(owningTownId, chunkPos);
                if (chunkTag == null) {
                    // No tag set - allow all actions for town members
                    Pokecobbleclaim.LOGGER.info("PERMISSION CHECK: No tag set for chunk {} - allowing action for town member", chunkPos);
                    return true;
                }

                // Check individual rank permissions (not hierarchical)
                boolean hasPermission = chunkTag.getRankPermissions().hasPermission(playerRank, permissionType);
                Pokecobbleclaim.LOGGER.info("PERMISSION CHECK: Player '{}' (rank: {}) permission {} for tag '{}' = {}",
                    player.getName().getString(), playerRank, getPermissionName(permissionType), chunkTag.getName(), hasPermission);

                return hasPermission;

            } else {
                // Player is not a town member - check non-member permissions
                ClaimTag chunkTag = getChunkTagFromCorrectSource(owningTownId, chunkPos);
                if (chunkTag == null) {
                    // No tag set - deny all actions for non-members
                    Pokecobbleclaim.LOGGER.info("PERMISSION CHECK: No tag set for chunk {} - denying action for non-member", chunkPos);
                    return false;
                }

                // Check non-member permissions (rank = null)
                boolean hasPermission = chunkTag.getRankPermissions().hasPermission(null, permissionType);
                Pokecobbleclaim.LOGGER.info("PERMISSION CHECK: Non-member '{}' permission {} for tag '{}' = {}",
                    player.getName().getString(), getPermissionName(permissionType), chunkTag.getName(), hasPermission);

                return hasPermission;
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error checking permission for player {} in chunk {}: {}",
                player.getName().getString(), chunkPos, e.getMessage(), e);
            return false; // Deny on error for security
        }
    }

    /**
     * Gets the claim tag for a chunk using the CORRECT source of truth.
     * This method uses ClaimTagManager (which has the updated permissions)
     * and TownChunkDataManager (which has the chunk-to-tag mapping).
     */
    private static ClaimTag getChunkTagFromCorrectSource(UUID townId, ChunkPos chunkPos) {
        try {
            // Step 1: Get the chunk-to-tag mapping from TownChunkDataManager
            com.pokecobble.town.chunk.TownChunkDataManager dataManager =
                com.pokecobble.town.chunk.TownChunkDataManager.getInstance();
            com.pokecobble.town.claim.TownClaimData townClaimData = dataManager.getTownChunkData(townId);

            if (townClaimData == null) {
                Pokecobbleclaim.LOGGER.info("CORRECT SOURCE: No claim data found for town {}", townId);
                return null;
            }

            // Get the tag ID assigned to this chunk
            ClaimTag oldTag = townClaimData.getChunkTag(chunkPos);
            if (oldTag == null) {
                Pokecobbleclaim.LOGGER.info("CORRECT SOURCE: No tag assigned to chunk {}", chunkPos);
                return null;
            }

            UUID tagId = oldTag.getId();
            Pokecobbleclaim.LOGGER.info("CORRECT SOURCE: Chunk {} is assigned tag ID {}", chunkPos, tagId);

            // Step 2: Get the CURRENT tag data from ClaimTagManager (the correct source of truth)
            com.pokecobble.town.claim.ClaimTagManager tagManager =
                com.pokecobble.town.claim.ClaimTagManager.getInstance();
            List<ClaimTag> currentTags = tagManager.getClaimTags(townId);

            Pokecobbleclaim.LOGGER.info("CORRECT SOURCE: Looking for tag ID {} among {} current tags", tagId, currentTags.size());

            // Debug: Log all current tag IDs
            for (ClaimTag currentTag : currentTags) {
                Pokecobbleclaim.LOGGER.info("CORRECT SOURCE: Available tag '{}' has ID {}", currentTag.getName(), currentTag.getId());
            }

            // Find the tag with the matching ID
            for (ClaimTag currentTag : currentTags) {
                if (currentTag.getId().equals(tagId)) {
                    Pokecobbleclaim.LOGGER.info("CORRECT SOURCE: Found current tag '{}' with updated permissions", currentTag.getName());
                    return currentTag;
                }
            }

            Pokecobbleclaim.LOGGER.warn("CORRECT SOURCE: Tag ID {} not found in current tags for town {}", tagId, townId);
            return null;

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("CORRECT SOURCE: Error getting chunk tag: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * Merges incoming tags with existing tags to preserve IDs.
     * This ensures that chunk-to-tag mappings remain valid when tags are updated.
     */
    private static List<ClaimTag> mergeTagsPreservingIds(UUID townId, List<ClaimTag> incomingTags) {
        try {
            // Get existing tags from ClaimTagManager
            com.pokecobble.town.claim.ClaimTagManager tagManager =
                com.pokecobble.town.claim.ClaimTagManager.getInstance();
            List<ClaimTag> existingTags = tagManager.getClaimTags(townId);

            List<ClaimTag> mergedTags = new ArrayList<>();

            Pokecobbleclaim.LOGGER.info("TAG MERGE: Merging {} incoming tags with {} existing tags",
                incomingTags.size(), existingTags.size());

            // For each incoming tag, try to find a matching existing tag by name
            for (ClaimTag incomingTag : incomingTags) {
                ClaimTag matchingExistingTag = null;

                // Find existing tag with same name
                for (ClaimTag existingTag : existingTags) {
                    if (existingTag.getName().equals(incomingTag.getName())) {
                        matchingExistingTag = existingTag;
                        break;
                    }
                }

                if (matchingExistingTag != null) {
                    // Preserve the existing tag's ID but update its properties
                    ClaimTag mergedTag = new ClaimTag(
                        matchingExistingTag.getId(), // Preserve existing ID
                        incomingTag.getName(),
                        incomingTag.getDescription(),
                        incomingTag.getColor()
                    );

                    // Copy all permissions from incoming tag
                    mergedTag.getRankPermissions().copyFrom(incomingTag.getRankPermissions());

                    mergedTags.add(mergedTag);

                    Pokecobbleclaim.LOGGER.info("TAG MERGE: Preserved ID {} for tag '{}'",
                        matchingExistingTag.getId(), incomingTag.getName());
                } else {
                    // New tag - keep the incoming tag as-is (it will have a new ID)
                    mergedTags.add(incomingTag);

                    Pokecobbleclaim.LOGGER.info("TAG MERGE: New tag '{}' with ID {}",
                        incomingTag.getName(), incomingTag.getId());
                }
            }

            Pokecobbleclaim.LOGGER.info("TAG MERGE: Completed merge with {} final tags", mergedTags.size());
            return mergedTags;

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("TAG MERGE: Error merging tags, using incoming tags as fallback: " + e.getMessage(), e);
            return new ArrayList<>(incomingTags);
        }
    }

    /**
     * Gets a human-readable name for a permission type.
     */
    public static String getPermissionName(int permissionType) {
        return switch (permissionType) {
            case PERMISSION_BUILD -> "Build";
            case PERMISSION_INTERACT -> "Interact";
            case PERMISSION_CONTAINERS -> "Containers";
            case PERMISSION_REDSTONE -> "Redstone";
            case PERMISSION_DOORS -> "Doors";
            case PERMISSION_CROPS -> "Crops";
            case PERMISSION_ANIMALS -> "Animals";
            case PERMISSION_VILLAGERS -> "Villagers";
            default -> "Unknown";
        };
    }

    /**
     * Initializes the claim tag synchronization handlers.
     * This should be called during mod initialization.
     */
    public static void initialize() {
        // Register server-side packet handlers
        ServerPlayNetworking.registerGlobalReceiver(NetworkConstants.CLAIM_TAG_UPDATE_REQUEST,
            ClaimTagSyncHandler::handleClaimTagUpdateRequest);
        ServerPlayNetworking.registerGlobalReceiver(NetworkConstants.CLAIM_TAG_ALL_SYNC_REQUEST,
            ClaimTagSyncHandler::handleAllTagsSyncRequest);
        ServerPlayNetworking.registerGlobalReceiver(NetworkConstants.CLAIM_TAG_DATA_REQUEST,
            ClaimTagSyncHandler::handleClaimTagDataRequest);

        // Register client-side packet handlers
        if (Pokecobbleclaim.isClient()) {
            registerClientHandlers();
        }

        Pokecobbleclaim.LOGGER.info("Claim tag sync handlers initialized");
    }
    
    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    private static void registerClientHandlers() {
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
            NetworkConstants.CLAIM_TAG_UPDATE_RESPONSE, ClaimTagSyncHandler::handleClaimTagUpdateResponse);

        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
            NetworkConstants.CLAIM_TAG_BROADCAST, ClaimTagSyncHandler::handleClaimTagBroadcast);

        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
            NetworkConstants.CLAIM_TAG_ALL_SYNC_BROADCAST, ClaimTagSyncHandler::handleAllTagsBroadcast);

        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
            NetworkConstants.CLAIM_TAG_DATA_RESPONSE, ClaimTagSyncHandler::handleClaimTagDataResponse);
    }
    
    /**
     * Sends a claim tag update request to the server.
     * This is called when the client modifies claim tag settings.
     */
    @Environment(EnvType.CLIENT)
    public static void requestClaimTagUpdate(UUID townId, ClaimTag updatedTag) {
        try {
            // Validate parameters
            PacketValidator.validateUUID(townId);
            if (updatedTag == null) {
                throw new IllegalArgumentException("Updated tag cannot be null");
            }

            // Create packet buffer
            PacketByteBuf buf = PacketByteBufs.create();

            // Add player UUID for validation
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player != null) {
                buf.writeUuid(client.player.getUuid());
            } else {
                Pokecobbleclaim.LOGGER.error("Cannot send claim tag update: no player");
                return;
            }

            // Write town ID
            buf.writeUuid(townId);

            // Write tag data
            writeClaimTagToBuffer(buf, updatedTag);

            // Send packet to server
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                NetworkConstants.CLAIM_TAG_UPDATE_REQUEST, buf);

            Pokecobbleclaim.LOGGER.debug("Sent claim tag update request for tag: " + updatedTag.getName());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending claim tag update request: " + e.getMessage(), e);
        }
    }

    /**
     * Requests claim tag data from the server for the claim tool.
     * This is called when the claim tool is activated to ensure it has the latest tag data.
     */
    @Environment(EnvType.CLIENT)
    public static void requestClaimTagData() {
        Pokecobbleclaim.LOGGER.info("CLIENT: requestClaimTagData() method called");
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                Pokecobbleclaim.LOGGER.error("Cannot request claim tag data: no player");
                return;
            }

            // Get player's town (use client-side town manager)
            Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
            if (playerTown == null) {
                Pokecobbleclaim.LOGGER.warn("Player is not in a town, cannot request claim tag data");
                return;
            }

            requestClaimTagData(playerTown);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("CLIENT: Error requesting claim tag data: " + e.getMessage(), e);
            e.printStackTrace();
        }
    }

    /**
     * Requests claim tag data for a specific town from the server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestClaimTagData(Town town) {
        Pokecobbleclaim.LOGGER.info("CLIENT: requestClaimTagData(Town) method called for town: {}", town.getName());
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                Pokecobbleclaim.LOGGER.error("Cannot request claim tag data: no player");
                return;
            }

            Pokecobbleclaim.LOGGER.info("CLIENT: Player is in town: {}, preparing to send request", town.getName());

            // Create packet buffer
            PacketByteBuf buf = PacketByteBufs.create();

            // Write player UUID for validation
            buf.writeUuid(client.player.getUuid());

            // Write town ID
            buf.writeUuid(town.getId());

            Pokecobbleclaim.LOGGER.info("CLIENT: Sending claim tag data request packet for town: {} (ID: {})",
                town.getName(), town.getId());

            // Send packet to server
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                NetworkConstants.CLAIM_TAG_DATA_REQUEST, buf);

            Pokecobbleclaim.LOGGER.info("CLIENT: Sent claim tag data request for town: {}", town.getName());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("CLIENT: Error requesting claim tag data for town {}: {}",
                town != null ? town.getName() : "null", e.getMessage(), e);
            e.printStackTrace();
        }
    }

    /**
     * Sends all claim tags for a town to the server for synchronization.
     * This ensures all clients receive the complete tag list.
     */
    @Environment(EnvType.CLIENT)
    public static void requestAllTagsSync(UUID townId, List<ClaimTag> allTags) {
        try {
            // Validate parameters
            PacketValidator.validateUUID(townId);
            if (allTags == null) {
                throw new IllegalArgumentException("Tags list cannot be null");
            }

            // Create packet buffer
            PacketByteBuf buf = PacketByteBufs.create();

            // Add player UUID for validation
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player != null) {
                buf.writeUuid(client.player.getUuid());
            } else {
                Pokecobbleclaim.LOGGER.error("Cannot send all tags sync: no player");
                return;
            }

            // Write town ID
            buf.writeUuid(townId);

            // Write number of tags
            buf.writeInt(allTags.size());

            // Write all tag data
            for (ClaimTag tag : allTags) {
                writeClaimTagToBuffer(buf, tag);
            }

            // Send packet to server
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                NetworkConstants.CLAIM_TAG_ALL_SYNC_REQUEST, buf);

            Pokecobbleclaim.LOGGER.debug("Sent all tags sync request for {} tags", allTags.size());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending all tags sync request: " + e.getMessage(), e);
        }
    }
    
    /**
     * Handles claim tag update requests from clients on the server side.
     */
    private static void handleClaimTagUpdateRequest(MinecraftServer server, ServerPlayerEntity player,
                                                   ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                                   PacketSender responseSender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);
            
            // Read player UUID
            UUID playerId = buf.readUuid();
            
            // Validate that the requesting player matches the packet sender
            if (!playerId.equals(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player UUID mismatch in claim tag update request");
                sendClaimTagUpdateResponse(player, false, "Invalid player");
                return;
            }
            
            // Read town ID
            UUID townId = buf.readUuid();
            
            // Read tag data
            ClaimTag updatedTag = readClaimTagFromBuffer(buf);
            
            // Validate permissions
            if (!validateClaimTagUpdatePermission(player, townId)) {
                sendClaimTagUpdateResponse(player, false, "Insufficient permissions");
                return;
            }
            
            // Get the town
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) {
                sendClaimTagUpdateResponse(player, false, "Town not found");
                return;
            }
            
            // Update the tag in the town
            boolean success = updateClaimTagInTown(town, updatedTag);
            
            if (success) {
                // Clear the town's cached data to ensure fresh tag data is loaded
                com.pokecobble.town.chunk.TownChunkDataManager.getInstance().invalidateTownCache(townId);
                Pokecobbleclaim.LOGGER.info("Tag '{}' updated successfully - cleared cache for real-time permission checking", updatedTag.getName());

                // Broadcast the update to all relevant players
                broadcastClaimTagUpdate(server, townId, updatedTag);

                // Send success response
                sendClaimTagUpdateResponse(player, true, "Tag updated successfully");

                Pokecobbleclaim.LOGGER.info("Updated claim tag '{}' for town '{}' by player '{}'",
                    updatedTag.getName(), town.getName(), player.getName().getString());
            } else {
                sendClaimTagUpdateResponse(player, false, "Failed to update tag");
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling claim tag update request: " + e.getMessage(), e);
            sendClaimTagUpdateResponse(player, false, "Server error");
        }
    }

    /**
     * Handles all tags sync requests from clients on the server side.
     */
    private static void handleAllTagsSyncRequest(MinecraftServer server, ServerPlayerEntity player,
                                                ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                                PacketSender responseSender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID
            UUID playerId = buf.readUuid();

            // Validate that the requesting player matches the packet sender
            if (!playerId.equals(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player UUID mismatch in all tags sync request");
                return;
            }

            // Read town ID
            UUID townId = buf.readUuid();

            // Read number of tags
            int tagCount = buf.readInt();

            // Validate tag count
            if (tagCount < 0 || tagCount > 50) { // Reasonable limit
                Pokecobbleclaim.LOGGER.warn("Invalid tag count in all tags sync request: " + tagCount);
                return;
            }

            // Read all tags
            List<ClaimTag> allTags = new ArrayList<>();
            for (int i = 0; i < tagCount; i++) {
                ClaimTag tag = readClaimTagFromBuffer(buf);
                allTags.add(tag);
            }

            // Validate permissions
            if (!validateClaimTagUpdatePermission(player, townId)) {
                Pokecobbleclaim.LOGGER.warn("Player {} lacks permission to sync tags for town {}",
                    player.getName().getString(), townId);
                return;
            }

            // Get the town
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Town not found for all tags sync: " + townId);
                return;
            }

            Pokecobbleclaim.LOGGER.info("BEFORE MERGE: About to merge {} incoming tags for town {}", allTags.size(), townId);

            // Debug: Log incoming tag IDs
            for (ClaimTag incomingTag : allTags) {
                Pokecobbleclaim.LOGGER.info("INCOMING TAG: '{}' has ID {}", incomingTag.getName(), incomingTag.getId());
            }

            // CRITICAL: Merge incoming tags with existing tags to preserve IDs
            List<ClaimTag> mergedTags = mergeTagsPreservingIds(townId, allTags);

            Pokecobbleclaim.LOGGER.info("AFTER MERGE: Got {} merged tags for town {}", mergedTags.size(), townId);

            // CRITICAL: Save to file system FIRST using ClaimTagManager to ensure persistence
            com.pokecobble.town.claim.ClaimTagManager.getInstance().updateClaimTags(townId, mergedTags, server);
            Pokecobbleclaim.LOGGER.info("CLAIM TAG SAVE: Updated claim tags for town {} with {} tags from player {}",
                townId, mergedTags.size(), player.getUuid());

            // Update the town object to keep it in sync (secondary)
            town.updateClaimTags(mergedTags);

            // Save the town data to disk to persist other changes
            TownManager.getInstance().saveTown(town);

            // Clear the town's cached data to ensure fresh tag data is loaded
            com.pokecobble.town.chunk.TownChunkDataManager.getInstance().invalidateTownCache(townId);
            Pokecobbleclaim.LOGGER.info("All tags updated for town {} - cleared cache for real-time permission checking", townId);

            // Broadcast all tags to all town members
            broadcastAllTagsUpdate(server, townId, mergedTags);

            // Sync all chunks with the updated tags using the new system
            // This ensures chunk colors are updated in the world
            com.pokecobble.town.chunk.TownChunkDataManager dataManager =
                com.pokecobble.town.chunk.TownChunkDataManager.getInstance();
            com.pokecobble.town.claim.TownClaimData townClaimData = dataManager.getTownChunkData(townId);

            if (townClaimData != null) {
                Map<ChunkPos, ClaimTag> chunkTags = townClaimData.getAllChunksWithTags();
                if (!chunkTags.isEmpty()) {
                    // Sync all chunks to update their colors
                    com.pokecobble.town.network.chunk.ChunkDataSynchronizer.syncChunks(server, chunkTags.keySet(), town, chunkTags);
                }
            }

            Pokecobbleclaim.LOGGER.info("Synced {} tags for town '{}' by player '{}' and saved to disk",
                allTags.size(), town.getName(), player.getName().getString());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling all tags sync request: " + e.getMessage(), e);
        }
    }

    /**
     * Handles claim tag data requests from clients.
     * This is called when a client (claim tool) requests the latest tag data.
     */
    private static void handleClaimTagDataRequest(MinecraftServer server, ServerPlayerEntity player,
                                                 ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                                 PacketSender responseSender) {
        try {
            // Read player UUID for validation
            UUID playerId = buf.readUuid();
            UUID townId = buf.readUuid();

            // Rate limiting check
            String rateLimitKey = player.getUuid() + ":" + townId;
            long currentTime = System.currentTimeMillis();
            Long lastRequestTime = lastTagRequestTime.get(rateLimitKey);

            if (lastRequestTime != null && (currentTime - lastRequestTime) < TAG_REQUEST_COOLDOWN_MS) {
                Pokecobbleclaim.LOGGER.debug("Rate limiting claim tag request from {} for town {} (too frequent)",
                    player.getName().getString(), townId);
                return; // Skip this request due to rate limiting
            }

            lastTagRequestTime.put(rateLimitKey, currentTime);

            Pokecobbleclaim.LOGGER.debug("SERVER: Received claim tag data request from player '{}' for town '{}'",
                player.getName().getString(), townId);

            // Validate player
            if (!player.getUuid().equals(playerId)) {
                Pokecobbleclaim.LOGGER.warn("Player UUID mismatch in claim tag data request");
                return;
            }

            // Get the town
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Town not found for claim tag data request: " + townId);
                return;
            }

            // Verify player is in the town
            if (!town.getPlayers().contains(playerId)) {
                Pokecobbleclaim.LOGGER.warn("SERVER: Player {} not in town {} for claim tag data request",
                    player.getName().getString(), town.getName());
                Pokecobbleclaim.LOGGER.warn("SERVER: Town '{}' has {} players: {}",
                    town.getName(), town.getPlayers().size(), town.getPlayers());
                Pokecobbleclaim.LOGGER.warn("SERVER: Requested player ID: {}", playerId);
                return;
            }

            // Get all claim tags for the town
            List<ClaimTag> townTags = town.getClaimTags();

            Pokecobbleclaim.LOGGER.debug("SERVER: Town '{}' has {} claim tags", town.getName(), townTags.size());
            for (int i = 0; i < townTags.size(); i++) {
                ClaimTag tag = townTags.get(i);
                Pokecobbleclaim.LOGGER.info("SERVER: Tag {}: '{}' (color: 0x{}, UUID: {})",
                    i, tag.getName(), Integer.toHexString(tag.getColor()).toUpperCase(), tag.getId());
            }

            // Send response with tag data
            sendClaimTagDataResponse(player, townTags);

            Pokecobbleclaim.LOGGER.debug("SERVER: Sent claim tag data response for town '{}' to player '{}' ({} tags)",
                town.getName(), player.getName().getString(), townTags.size());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling claim tag data request: " + e.getMessage(), e);
        }
    }

    /**
     * Validates if a player has permission to update claim tags for a town.
     */
    private static boolean validateClaimTagUpdatePermission(ServerPlayerEntity player, UUID townId) {
        try {
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) {
                return false;
            }
            
            TownPlayer townPlayer = town.getPlayer(player.getUuid());
            if (townPlayer == null) {
                return false;
            }
            
            // Check if player is owner or has claim tool permissions
            return townPlayer.getRank() == TownPlayerRank.OWNER || 
                   townPlayer.hasPermission("Claim Tool", "Can modify claim tags");
                   
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error validating claim tag update permission: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Updates a claim tag in the town's data.
     */
    private static boolean updateClaimTagInTown(Town town, ClaimTag updatedTag) {
        try {
            // Get current tags from the file system (primary source)
            List<ClaimTag> tags = com.pokecobble.town.claim.ClaimTagManager.getInstance().getClaimTags(town.getId());

            boolean tagFound = false;
            for (int i = 0; i < tags.size(); i++) {
                ClaimTag existingTag = tags.get(i);
                if (existingTag.getId().equals(updatedTag.getId())) {
                    // Replace the tag
                    tags.set(i, updatedTag);
                    tagFound = true;
                    break;
                }
            }

            if (!tagFound) {
                // Tag not found - this might be a new tag
                tags.add(updatedTag);
            }

            // CRITICAL: Save to file system FIRST using ClaimTagManager
            com.pokecobble.town.claim.ClaimTagManager.getInstance().updateClaimTags(town.getId(), tags, null);

            // Update the town object to keep it in sync (secondary)
            town.updateClaimTags(tags);

            // Save the town data to disk to persist other changes
            TownManager.getInstance().saveTown(town);

            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating claim tag in town: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Broadcasts a claim tag update to all relevant players.
     */
    private static void broadcastClaimTagUpdate(MinecraftServer server, UUID townId, ClaimTag updatedTag) {
        try {
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) {
                return;
            }
            
            // Create broadcast packet
            PacketByteBuf broadcastBuf = PacketByteBufs.create();
            broadcastBuf.writeUuid(townId);
            writeClaimTagToBuffer(broadcastBuf, updatedTag);
            
            // Send to all town members
            for (UUID playerId : town.getPlayers()) {
                ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
                if (player != null) {
                    ServerPlayNetworking.send(player, NetworkConstants.CLAIM_TAG_BROADCAST, broadcastBuf);
                }
            }
            
            Pokecobbleclaim.LOGGER.debug("Broadcasted claim tag update for tag: " + updatedTag.getName());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error broadcasting claim tag update: " + e.getMessage(), e);
        }
    }

    /**
     * Broadcasts all claim tags to all relevant players.
     */
    private static void broadcastAllTagsUpdate(MinecraftServer server, UUID townId, List<ClaimTag> allTags) {
        try {
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) {
                return;
            }

            // Create broadcast packet
            PacketByteBuf broadcastBuf = PacketByteBufs.create();
            broadcastBuf.writeUuid(townId);

            // Write number of tags
            broadcastBuf.writeInt(allTags.size());

            // Write all tag data
            for (ClaimTag tag : allTags) {
                writeClaimTagToBuffer(broadcastBuf, tag);
            }

            // Send to all town members
            for (UUID playerId : town.getPlayers()) {
                ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
                if (player != null) {
                    ServerPlayNetworking.send(player, NetworkConstants.CLAIM_TAG_ALL_SYNC_BROADCAST, broadcastBuf);
                }
            }

            Pokecobbleclaim.LOGGER.debug("Broadcasted all tags update for {} tags", allTags.size());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error broadcasting all tags update: " + e.getMessage(), e);
        }
    }

    /**
     * Sends claim tag data response to a specific player.
     */
    public static void sendClaimTagDataResponse(ServerPlayerEntity player, List<ClaimTag> tags) {
        try {
            PacketByteBuf responseBuf = PacketByteBufs.create();

            // FIXED: Write town ID first to match client expectation
            // Get the player's town to include the town ID
            com.pokecobble.town.Town playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(player.getUuid());
            if (playerTown != null) {
                responseBuf.writeUuid(playerTown.getId());
            } else {
                // If no town, write a null UUID
                responseBuf.writeUuid(new java.util.UUID(0, 0));
            }

            // Write number of tags
            responseBuf.writeInt(tags.size());

            // Write all tag data
            for (ClaimTag tag : tags) {
                writeClaimTagToBuffer(responseBuf, tag);
            }

            // Send response to player
            ServerPlayNetworking.send(player, NetworkConstants.CLAIM_TAG_DATA_RESPONSE, responseBuf);

            Pokecobbleclaim.LOGGER.debug("Sent claim tag data response with {} tags for town {}", tags.size(),
                playerTown != null ? playerTown.getId() : "null");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending claim tag data response: " + e.getMessage(), e);
        }
    }

    /**
     * Sends a claim tag update response to a player.
     */
    private static void sendClaimTagUpdateResponse(ServerPlayerEntity player, boolean success, String message) {
        try {
            PacketByteBuf responseBuf = PacketByteBufs.create();
            responseBuf.writeBoolean(success);
            responseBuf.writeString(message);
            
            ServerPlayNetworking.send(player, NetworkConstants.CLAIM_TAG_UPDATE_RESPONSE, responseBuf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending claim tag update response: " + e.getMessage(), e);
        }
    }
    
    /**
     * Handles claim tag update responses on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleClaimTagUpdateResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                                    PacketByteBuf buf, PacketSender responseSender) {
        try {
            boolean success = buf.readBoolean();
            String message = buf.readString();
            
            if (success) {
                Pokecobbleclaim.LOGGER.info("Claim tag update successful: " + message);
                // Optionally show success notification to player
            } else {
                Pokecobbleclaim.LOGGER.warn("Claim tag update failed: " + message);
                // Optionally show error notification to player
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling claim tag update response: " + e.getMessage(), e);
        }
    }
    
    /**
     * Handles claim tag broadcasts on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleClaimTagBroadcast(MinecraftClient client, ClientPlayNetworkHandler handler,
                                               PacketByteBuf buf, PacketSender responseSender) {
        try {
            UUID townId = buf.readUuid();
            ClaimTag updatedTag = readClaimTagFromBuffer(buf);
            
            // Update local town data
            Town town = TownManager.getInstance().getTown(townId);
            if (town != null) {
                updateLocalClaimTag(town, updatedTag);

                // Force refresh of chunk rendering to show updated colors
                refreshChunkRendering();

                Pokecobbleclaim.LOGGER.debug("Updated local claim tag: " + updatedTag.getName());
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling claim tag broadcast: " + e.getMessage(), e);
        }
    }

    /**
     * Handles all tags broadcasts on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleAllTagsBroadcast(MinecraftClient client, ClientPlayNetworkHandler handler,
                                              PacketByteBuf buf, PacketSender responseSender) {
        try {
            UUID townId = buf.readUuid();

            // Read number of tags
            int tagCount = buf.readInt();

            // Read all tags
            List<ClaimTag> allTags = new ArrayList<>();
            for (int i = 0; i < tagCount; i++) {
                ClaimTag tag = readClaimTagFromBuffer(buf);
                allTags.add(tag);
            }

            // Update local town data
            Town town = TownManager.getInstance().getTown(townId);
            if (town != null) {
                town.updateClaimTags(allTags);

                // Update claim tool if active
                updateClaimToolWithNewTags(allTags);

                // Force refresh of chunk rendering to show updated colors
                refreshChunkRendering();

                Pokecobbleclaim.LOGGER.debug("Updated local town with {} tags", allTags.size());
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling all tags broadcast: " + e.getMessage(), e);
        }
    }

    /**
     * Handles claim tag data responses on the client side.
     * This is called when the server responds to a claim tag data request.
     */
    @Environment(EnvType.CLIENT)
    private static void handleClaimTagDataResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                                  PacketByteBuf buf, PacketSender responseSender) {
        try {
            // FIXED: Read town ID first to match server sending format
            UUID townId = buf.readUuid();

            // Read number of tags
            int tagCount = buf.readInt();

            // Read all tags
            List<ClaimTag> tags = new ArrayList<>();
            for (int i = 0; i < tagCount; i++) {
                ClaimTag tag = readClaimTagFromBuffer(buf);
                tags.add(tag);
            }

            Pokecobbleclaim.LOGGER.info("CLAIM TAG RESPONSE: Received {} tags for town {}", tags.size(), townId);
            if (!tags.isEmpty()) {
                Pokecobbleclaim.LOGGER.info("CLAIM TAG RESPONSE: First tag name: '{}', color: {}",
                    tags.get(0).getName(), tags.get(0).getColor());
            }

            Pokecobbleclaim.LOGGER.info("Received claim tag data response for town: {} with {} tags", townId, tags.size());

            // First, update the player's town data with the received tags
            if (client.player != null) {
                // Use ClientTownManager for client-side town data
                Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
                if (playerTown != null && playerTown.getId().equals(townId)) {
                    // Update the town's claim tags with the server data
                    playerTown.updateClaimTags(tags);
                    Pokecobbleclaim.LOGGER.info("CLIENT: Updated town '{}' with {} tags from server",
                        playerTown.getName(), tags.size());
                } else {
                    Pokecobbleclaim.LOGGER.warn("CLIENT: Player not in matching town (player town: {}, response town: {}), cannot update town data with received tags",
                        playerTown != null ? playerTown.getId() : "null", townId);
                }
            }

            // Then update claim tool with the received tags
            updateClaimToolWithNewTags(tags);

            // Notify any open ClaimTagScreen that new data has been received
            if (client.currentScreen instanceof com.pokecobble.town.gui.ClaimTagScreen) {
                com.pokecobble.town.gui.ClaimTagScreen screen = (com.pokecobble.town.gui.ClaimTagScreen) client.currentScreen;
                screen.onServerDataReceived();
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling claim tag data response: " + e.getMessage(), e);
        }
    }

    /**
     * Updates the claim tool with new tags if it's currently active.
     */
    @Environment(EnvType.CLIENT)
    private static void updateClaimToolWithNewTags(List<ClaimTag> newTags) {
        try {
            com.pokecobble.town.claim.ClaimTool claimTool = com.pokecobble.town.claim.ClaimTool.getInstance();
            if (claimTool != null) {
                // Check if we have valid tags
                if (newTags != null && !newTags.isEmpty()) {
                    // Always update the claim tool tags, whether it's active or not
                    // This ensures the tags are ready when the tool becomes active
                    claimTool.updateTags(newTags);
                    Pokecobbleclaim.LOGGER.info("CLIENT: Updated claim tool with {} new tags (active: {})",
                        newTags.size(), claimTool.isActive());

                    // If claim tool is active, sync tag updates to other town members
                    if (claimTool.isActive()) {
                        com.pokecobble.town.claim.ChunkSelectionManager selectionManager =
                            com.pokecobble.town.claim.ChunkSelectionManager.getInstance();
                        selectionManager.syncTagUpdatesToTownMembers();
                        Pokecobbleclaim.LOGGER.info("CLIENT: Synced tag updates to town members after server update");
                    }
                } else {
                    Pokecobbleclaim.LOGGER.warn("CLIENT: Received empty or null tag list from server, claim tool not updated");
                    // Don't update the claim tool with empty tags - keep existing tags
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating claim tool with new tags: " + e.getMessage(), e);
        }
    }

    /**
     * Updates a claim tag in the local town data.
     */
    @Environment(EnvType.CLIENT)
    private static void updateLocalClaimTag(Town town, ClaimTag updatedTag) {
        List<ClaimTag> tags = town.getClaimTags();
        for (int i = 0; i < tags.size(); i++) {
            ClaimTag existingTag = tags.get(i);
            if (existingTag.getId().equals(updatedTag.getId())) {
                tags.set(i, updatedTag);
                town.updateClaimTags(tags);
                return;
            }
        }

        // Tag not found - add as new
        tags.add(updatedTag);
        town.updateClaimTags(tags);
    }

    /**
     * Forces a refresh of chunk rendering to show updated colors.
     */
    @Environment(EnvType.CLIENT)
    private static void refreshChunkRendering() {
        try {
            // Get the player's town
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player != null) {
                Town playerTown = TownManager.getInstance().getPlayerTown(client.player.getUuid());
                if (playerTown != null) {
                    // Fire town update event to refresh MyTownScreen
                    com.pokecobble.town.client.ClientSyncManager.getInstance().onTownUpdated(
                        playerTown.getId(), playerTown.getDataVersion() + 1);

                    // Fire chunk update event to refresh chunk rendering
                    com.pokecobble.town.client.ClientSyncManager.getInstance().fireEvent(
                        com.pokecobble.town.client.ClientSyncManager.EVENT_CHUNK_UPDATED, null);

                    // Request updated chunk claim data
                    com.pokecobble.town.network.chunk.ChunkClaimSyncHandler.requestChunkClaimSync(playerTown.getId());
                }
            }

            // Claim tool will be updated automatically via ChunkClaimSyncHandler.requestChunkClaimSync() above

            // Force refresh of UI components that might display chunk data
            com.pokecobble.ui.UIDataRefreshManager.getInstance().forceRefreshAll();

            Pokecobbleclaim.LOGGER.debug("Refreshed chunk rendering after tag update");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error refreshing chunk rendering: " + e.getMessage(), e);
        }
    }
    
    /**
     * Writes a ClaimTag to a packet buffer.
     */
    private static void writeClaimTagToBuffer(PacketByteBuf buf, ClaimTag tag) {
        buf.writeUuid(tag.getId());
        buf.writeString(tag.getName(), com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
        buf.writeString(tag.getDescription(), com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
        buf.writeInt(tag.getColor());
        
        // Write rank permissions
        for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
            boolean[] permissions = tag.getRankPermissions().getPermissions(rank);
            for (boolean permission : permissions) {
                buf.writeBoolean(permission);
            }
        }
        
        // Write non-member permissions
        boolean[] nonMemberPermissions = tag.getRankPermissions().getPermissions(null);
        for (boolean permission : nonMemberPermissions) {
            buf.writeBoolean(permission);
        }
    }
    
    /**
     * Reads a ClaimTag from a packet buffer.
     * FIXED: Now properly preserves UUID to maintain tag identity across network sync.
     * Enhanced with error handling for buffer underruns and backward compatibility.
     */
    private static ClaimTag readClaimTagFromBuffer(PacketByteBuf buf) {
        try {
            UUID id = buf.readUuid();
            String name = buf.readString(com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
            String description = buf.readString(com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);
            int color = buf.readInt();

            // CRITICAL FIX: Use the UUID constructor to preserve tag identity
            ClaimTag tag = new ClaimTag(id, name, description, color);

            // Read rank permissions
            com.pokecobble.Pokecobbleclaim.LOGGER.info("NETWORK READ DEBUG: Reading permissions for tag '{}', buffer has {} bytes", name, buf.readableBytes());

            for (com.pokecobble.town.TownPlayerRank rank : com.pokecobble.town.TownPlayerRank.values()) {
                boolean[] rankPermissions = new boolean[8];
                for (int i = 0; i < 8; i++) {
                    if (buf.readableBytes() < 1) {
                        com.pokecobble.Pokecobbleclaim.LOGGER.error("NETWORK READ ERROR: Buffer underrun while reading rank permissions for tag '{}' rank {} permission {}. Buffer has {} bytes. Using default permissions.",
                            name, rank, i, buf.readableBytes());
                        return tag; // Return tag with default permissions
                    }
                    boolean permission = buf.readBoolean();
                    rankPermissions[i] = permission;
                    tag.getRankPermissions().setPermission(rank, i, permission);
                }
                com.pokecobble.Pokecobbleclaim.LOGGER.info("NETWORK READ DEBUG: Read permissions for rank {} ({}): {}",
                    rank, rank.getDisplayName(), java.util.Arrays.toString(rankPermissions));
            }

            // Read non-member permissions (with backward compatibility check)
            boolean[] nonMemberPermissions = new boolean[8];
            for (int i = 0; i < 8; i++) {
                if (buf.readableBytes() < 1) {
                    com.pokecobble.Pokecobbleclaim.LOGGER.error("NETWORK READ ERROR: Buffer underrun while reading non-member permissions for tag '{}' permission {}. Buffer has {} bytes. Using default permissions.",
                        name, i, buf.readableBytes());
                    return tag; // Return tag with default non-member permissions
                }
                boolean permission = buf.readBoolean();
                nonMemberPermissions[i] = permission;
                tag.getRankPermissions().setPermission(null, i, permission);
            }
            com.pokecobble.Pokecobbleclaim.LOGGER.info("NETWORK READ DEBUG: Read non-member permissions: {}",
                java.util.Arrays.toString(nonMemberPermissions));

            com.pokecobble.Pokecobbleclaim.LOGGER.info("NETWORK READ DEBUG: Successfully read tag '{}' with all permissions", name);

            return tag;
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error reading claim tag from buffer: " + e.getMessage());
            return null;
        }
    }
}
