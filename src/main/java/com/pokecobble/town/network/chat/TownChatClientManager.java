package com.pokecobble.town.network.chat;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.chat.TownChatMessage;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Client-side manager for town chat messages.
 * Handles local caching and UI updates.
 */
@Environment(EnvType.CLIENT)
public class TownChatClientManager {
    private static TownChatClientManager instance;
    
    // Client-side cache of chat messages
    private final Map<UUID, List<ClientChatMessage>> townChatHistory = new ConcurrentHashMap<>();
    private final Set<TownChatUpdateListener> updateListeners = ConcurrentHashMap.newKeySet();
    
    // Configuration
    private static final int MAX_CACHED_MESSAGES = 100;
    
    private TownChatClientManager() {
        Pokecobbleclaim.LOGGER.debug("TownChatClientManager initialized");
    }
    
    public static TownChatClientManager getInstance() {
        if (instance == null) {
            instance = new TownChatClientManager();
        }
        return instance;
    }
    
    /**
     * Client-side representation of a chat message.
     */
    public static class ClientChatMessage {
        private final UUID messageId;
        private final UUID townId;
        private final UUID playerId;
        private final String playerName;
        private final String message;
        private final Instant timestamp;
        private final TownChatMessage.MessageType type;
        
        public ClientChatMessage(UUID messageId, UUID townId, UUID playerId, String playerName, 
                               String message, Instant timestamp, TownChatMessage.MessageType type) {
            this.messageId = messageId;
            this.townId = townId;
            this.playerId = playerId;
            this.playerName = playerName;
            this.message = message;
            this.timestamp = timestamp;
            this.type = type;
        }
        
        // Getters
        public UUID getMessageId() { return messageId; }
        public UUID getTownId() { return townId; }
        public UUID getPlayerId() { return playerId; }
        public String getPlayerName() { return playerName; }
        public String getMessage() { return message; }
        public Instant getTimestamp() { return timestamp; }
        public TownChatMessage.MessageType getType() { return type; }
        
        public String getFormattedTime() {
            return String.format("%02d:%02d", 
                timestamp.atZone(java.time.ZoneId.systemDefault()).getHour(),
                timestamp.atZone(java.time.ZoneId.systemDefault()).getMinute());
        }
        
        public String getDisplayName() {
            switch (type) {
                case SYSTEM_MESSAGE:
                    return "System";
                case ANNOUNCEMENT:
                    return playerName + " (Announcement)";
                case PLAYER_MESSAGE:
                default:
                    return playerName;
            }
        }
        
        public boolean isSystemMessage() {
            return type == TownChatMessage.MessageType.SYSTEM_MESSAGE || 
                   type == TownChatMessage.MessageType.ANNOUNCEMENT;
        }
    }
    
    /**
     * Interface for listening to chat updates.
     */
    public interface TownChatUpdateListener {
        void onChatMessageAdded(UUID townId, ClientChatMessage message);
        void onChatHistoryUpdated(UUID townId, List<ClientChatMessage> history);
    }
    
    /**
     * Adds a message to the client-side cache.
     */
    public void addMessage(UUID townId, UUID messageId, UUID playerId, String playerName, 
                          String message, Instant timestamp, TownChatMessage.MessageType type) {
        ClientChatMessage chatMessage = new ClientChatMessage(messageId, townId, playerId, 
            playerName, message, timestamp, type);
        
        List<ClientChatMessage> history = townChatHistory.computeIfAbsent(townId, k -> new CopyOnWriteArrayList<>());
        history.add(chatMessage);
        
        // Limit cache size
        if (history.size() > MAX_CACHED_MESSAGES) {
            history.remove(0);
        }
        
        // Notify listeners
        for (TownChatUpdateListener listener : updateListeners) {
            try {
                listener.onChatMessageAdded(townId, chatMessage);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error notifying chat update listener: " + e.getMessage());
            }
        }
        
        Pokecobbleclaim.LOGGER.debug("Added chat message to client cache: {} - {}", playerName, message);
    }
    
    /**
     * Gets the chat history for a town.
     */
    public List<ClientChatMessage> getChatHistory(UUID townId) {
        List<ClientChatMessage> history = townChatHistory.get(townId);
        return history != null ? new ArrayList<>(history) : new ArrayList<>();
    }
    
    /**
     * Clears chat history for a town (used when receiving fresh history from server).
     */
    public void clearHistory(UUID townId) {
        townChatHistory.remove(townId);
        Pokecobbleclaim.LOGGER.debug("Cleared client chat history for town {}", townId);
    }
    
    /**
     * Gets recent messages for a town.
     */
    public List<ClientChatMessage> getRecentMessages(UUID townId, int count) {
        List<ClientChatMessage> history = getChatHistory(townId);
        if (history.size() <= count) {
            return history;
        }
        return history.subList(history.size() - count, history.size());
    }
    
    /**
     * Registers a listener for chat updates.
     */
    public void addUpdateListener(TownChatUpdateListener listener) {
        updateListeners.add(listener);
    }
    
    /**
     * Unregisters a listener for chat updates.
     */
    public void removeUpdateListener(TownChatUpdateListener listener) {
        updateListeners.remove(listener);
    }
    
    /**
     * Sends a chat message to the server.
     */
    public void sendMessage(UUID townId, String message) {
        if (message == null || message.trim().isEmpty()) {
            return;
        }
        
        TownChatNetworkHandler.sendChatMessage(townId, message.trim());
    }
    
    /**
     * Requests chat history from the server.
     */
    public void requestHistory(UUID townId) {
        TownChatNetworkHandler.requestChatHistory(townId);
    }

    /**
     * Forces a refresh of chat history from the server, clearing existing cache.
     * Use this when you need to ensure you have the latest server state.
     */
    public void forceRefreshHistory(UUID townId) {
        clearHistory(townId);
        TownChatNetworkHandler.requestChatHistory(townId);
    }
    
    /**
     * Registers as a chat viewer for a town.
     */
    public void registerViewer(UUID townId) {
        TownChatNetworkHandler.registerChatViewer(townId);
    }
    
    /**
     * Unregisters as a chat viewer for a town.
     */
    public void unregisterViewer(UUID townId) {
        TownChatNetworkHandler.unregisterChatViewer(townId);
    }
    
    /**
     * Clears all cached chat data.
     */
    public void clearAllData() {
        townChatHistory.clear();
        Pokecobbleclaim.LOGGER.debug("Cleared all client chat data");
    }
    
    /**
     * Gets the number of cached messages for a town.
     */
    public int getMessageCount(UUID townId) {
        List<ClientChatMessage> history = townChatHistory.get(townId);
        return history != null ? history.size() : 0;
    }
    
    /**
     * Checks if there are any cached messages for a town.
     */
    public boolean hasMessages(UUID townId) {
        return getMessageCount(townId) > 0;
    }

    /**
     * Merges chat history from server with existing client cache.
     * This prevents losing messages that were received while the screen was closed.
     */
    public void mergeHistoryFromServer(UUID townId, net.minecraft.network.PacketByteBuf buf, int messageCount) {
        try {
            // Get existing messages
            List<ClientChatMessage> existingMessages = getChatHistory(townId);
            Set<UUID> existingMessageIds = new HashSet<>();

            // Create a set of existing message IDs for deduplication
            for (ClientChatMessage msg : existingMessages) {
                existingMessageIds.add(msg.getMessageId());
            }

            // Read server messages and add only new ones
            List<ClientChatMessage> serverMessages = new ArrayList<>();
            for (int i = 0; i < messageCount; i++) {
                try {
                    UUID messageId = buf.readUuid();
                    UUID playerId = buf.readUuid();
                    String playerName = buf.readString();
                    String message = buf.readString();
                    long timestampMillis = buf.readLong();
                    com.pokecobble.town.chat.TownChatMessage.MessageType type =
                        com.pokecobble.town.chat.TownChatMessage.MessageType.valueOf(buf.readString());

                    // Only add if we don't already have this message
                    if (!existingMessageIds.contains(messageId)) {
                        ClientChatMessage chatMessage = new ClientChatMessage(messageId, townId, playerId,
                            playerName, message, java.time.Instant.ofEpochMilli(timestampMillis), type);
                        serverMessages.add(chatMessage);
                    }
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error reading server chat message: " + e.getMessage());
                    break;
                }
            }

            // Merge messages: combine existing and new server messages, sort by timestamp
            List<ClientChatMessage> allMessages = new ArrayList<>(existingMessages);
            allMessages.addAll(serverMessages);

            // Sort by timestamp to maintain chronological order
            allMessages.sort((a, b) -> a.getTimestamp().compareTo(b.getTimestamp()));

            // Limit total messages
            if (allMessages.size() > MAX_CACHED_MESSAGES) {
                allMessages = allMessages.subList(allMessages.size() - MAX_CACHED_MESSAGES, allMessages.size());
            }

            // Replace the town's chat history with merged messages
            townChatHistory.put(townId, new java.util.concurrent.CopyOnWriteArrayList<>(allMessages));

            // Notify listeners about the updated history
            for (TownChatUpdateListener listener : updateListeners) {
                try {
                    listener.onChatHistoryUpdated(townId, allMessages);
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error notifying chat history update listener: " + e.getMessage());
                }
            }

            Pokecobbleclaim.LOGGER.debug("Merged chat history for town {}: {} existing + {} server = {} total",
                townId, existingMessages.size(), serverMessages.size(), allMessages.size());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error merging chat history from server: " + e.getMessage());
            // Fallback: clear and reload from server
            clearHistory(townId);
            // Re-read the buffer is not possible since it's already consumed, so we'll just log the error
        }
    }
}
