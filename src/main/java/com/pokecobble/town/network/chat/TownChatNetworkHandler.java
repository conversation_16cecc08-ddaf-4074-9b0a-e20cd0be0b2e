package com.pokecobble.town.network.chat;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.chat.TownChatManager;
import com.pokecobble.town.chat.TownChatMessage;
import com.pokecobble.town.network.NetworkConstants;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Handles network communication for town chat system.
 */
public class TownChatNetworkHandler {

    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register chat message receive handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_CHAT_MESSAGE_RECEIVE,
                TownChatNetworkHandler::handleChatMessageReceive
        );

        // Register chat history response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_CHAT_HISTORY_RESPONSE,
                TownChatNetworkHandler::handleChatHistoryResponse
        );
    }

    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register chat message send handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_CHAT_MESSAGE_SEND,
                TownChatNetworkHandler::handleChatMessageSend
        );

        // Register chat history request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_CHAT_HISTORY_REQUEST,
                TownChatNetworkHandler::handleChatHistoryRequest
        );

        // Register chat viewer registration handlers
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_CHAT_VIEWER_REGISTER,
                TownChatNetworkHandler::handleChatViewerRegister
        );

        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_CHAT_VIEWER_UNREGISTER,
                TownChatNetworkHandler::handleChatViewerUnregister
        );
    }

    // Client-side methods

    /**
     * Sends a chat message to the server.
     */
    @Environment(EnvType.CLIENT)
    public static void sendChatMessage(UUID townId, String message) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeUuid(townId);
        buf.writeString(message);

        ClientPlayNetworking.send(NetworkConstants.TOWN_CHAT_MESSAGE_SEND, buf);
    }

    /**
     * Requests chat history from the server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestChatHistory(UUID townId) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeUuid(townId);

        ClientPlayNetworking.send(NetworkConstants.TOWN_CHAT_HISTORY_REQUEST, buf);
    }

    /**
     * Registers as a chat viewer.
     */
    @Environment(EnvType.CLIENT)
    public static void registerChatViewer(UUID townId) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeUuid(townId);

        ClientPlayNetworking.send(NetworkConstants.TOWN_CHAT_VIEWER_REGISTER, buf);
    }

    /**
     * Unregisters as a chat viewer.
     */
    @Environment(EnvType.CLIENT)
    public static void unregisterChatViewer(UUID townId) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeUuid(townId);

        ClientPlayNetworking.send(NetworkConstants.TOWN_CHAT_VIEWER_UNREGISTER, buf);
    }

    // Client-side handlers

    @Environment(EnvType.CLIENT)
    private static void handleChatMessageReceive(MinecraftClient client, ClientPlayNetworkHandler handler,
                                                PacketByteBuf buf, PacketSender sender) {
        try {
            UUID townId = buf.readUuid();
            UUID messageId = buf.readUuid();
            UUID playerId = buf.readUuid();
            String playerName = buf.readString();
            String message = buf.readString();
            long timestampMillis = buf.readLong();
            TownChatMessage.MessageType type = TownChatMessage.MessageType.valueOf(buf.readString());

            // Handle on main thread
            client.execute(() -> {
                // Notify client chat manager about new message
                TownChatClientManager.getInstance().addMessage(townId, messageId, playerId,
                    playerName, message, Instant.ofEpochMilli(timestampMillis), type);
                Pokecobbleclaim.LOGGER.info("Client received chat message: {} - {}", playerName, message);
            });
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chat message receive: " + e.getMessage());
        }
    }

    @Environment(EnvType.CLIENT)
    private static void handleChatHistoryResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                                 PacketByteBuf buf, PacketSender sender) {
        try {
            UUID townId = buf.readUuid();
            int messageCount = buf.readInt();

            // Handle on main thread
            client.execute(() -> {
                // Instead of clearing history, merge with existing messages
                TownChatClientManager.getInstance().mergeHistoryFromServer(townId, buf, messageCount);
            });
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chat history response: " + e.getMessage());
        }
    }

    // Server-side handlers

    private static void handleChatMessageSend(MinecraftServer server, ServerPlayerEntity player,
                                            ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                            PacketSender sender) {
        try {
            UUID townId = buf.readUuid();
            String message = buf.readString();

            server.execute(() -> {
                // Verify player is in the town
                Town town = TownManager.getInstance().getTownById(townId);
                if (town == null || !town.getPlayerIds().contains(player.getUuid())) {
                    Pokecobbleclaim.LOGGER.warn("Player {} tried to send message to town {} but is not a member", 
                        player.getName().getString(), townId);
                    return;
                }

                // Add message to chat manager
                TownChatMessage chatMessage = TownChatManager.getInstance().addMessage(
                    townId, player.getUuid(), player.getName().getString(), message);

                if (chatMessage != null) {
                    // Broadcast to all online town members
                    Pokecobbleclaim.LOGGER.info("Server broadcasting chat message: {} - {}",
                        player.getName().getString(), message);
                    broadcastChatMessage(server, chatMessage);
                } else {
                    Pokecobbleclaim.LOGGER.warn("Failed to create chat message for player {} in town {}",
                        player.getName().getString(), townId);
                }
            });
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chat message send: " + e.getMessage());
        }
    }

    private static void handleChatHistoryRequest(MinecraftServer server, ServerPlayerEntity player,
                                               ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                               PacketSender sender) {
        try {
            UUID townId = buf.readUuid();

            server.execute(() -> {
                // Verify player is in the town
                Town town = TownManager.getInstance().getTownById(townId);
                if (town == null || !town.getPlayerIds().contains(player.getUuid())) {
                    return;
                }

                // Send chat history
                List<TownChatMessage> history = TownChatManager.getInstance().getRecentMessages(townId, 50);
                sendChatHistory(player, townId, history);
            });
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chat history request: " + e.getMessage());
        }
    }

    private static void handleChatViewerRegister(MinecraftServer server, ServerPlayerEntity player,
                                                ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                                PacketSender sender) {
        try {
            UUID townId = buf.readUuid();

            server.execute(() -> {
                TownChatManager.getInstance().registerChatViewer(townId, player.getUuid());
            });
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chat viewer register: " + e.getMessage());
        }
    }

    private static void handleChatViewerUnregister(MinecraftServer server, ServerPlayerEntity player,
                                                  ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                                  PacketSender sender) {
        try {
            UUID townId = buf.readUuid();

            server.execute(() -> {
                TownChatManager.getInstance().unregisterChatViewer(townId, player.getUuid());
            });
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chat viewer unregister: " + e.getMessage());
        }
    }

    // Helper methods

    /**
     * Broadcasts a chat message to all online town members.
     */
    private static void broadcastChatMessage(MinecraftServer server, TownChatMessage message) {
        List<ServerPlayerEntity> onlineMembers = TownChatManager.getInstance()
            .getOnlineTownMembers(server, message.getTownId());

        // Create a separate buffer for each player since PacketByteBuf can only be read once
        for (ServerPlayerEntity member : onlineMembers) {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(message.getTownId());
            buf.writeUuid(message.getMessageId());
            buf.writeUuid(message.getPlayerId() != null ? message.getPlayerId() : new UUID(0, 0));
            buf.writeString(message.getPlayerName());
            buf.writeString(message.getMessage());
            buf.writeLong(message.getTimestamp().toEpochMilli());
            buf.writeString(message.getType().name());

            ServerPlayNetworking.send(member, NetworkConstants.TOWN_CHAT_MESSAGE_RECEIVE, buf);
        }
    }

    /**
     * Sends chat history to a specific player.
     */
    private static void sendChatHistory(ServerPlayerEntity player, UUID townId, List<TownChatMessage> history) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeUuid(townId);
        buf.writeInt(history.size());

        for (TownChatMessage message : history) {
            buf.writeUuid(message.getMessageId());
            buf.writeUuid(message.getPlayerId() != null ? message.getPlayerId() : new UUID(0, 0));
            buf.writeString(message.getPlayerName());
            buf.writeString(message.getMessage());
            buf.writeLong(message.getTimestamp().toEpochMilli());
            buf.writeString(message.getType().name());
        }

        ServerPlayNetworking.send(player, NetworkConstants.TOWN_CHAT_HISTORY_RESPONSE, buf);
    }
}
