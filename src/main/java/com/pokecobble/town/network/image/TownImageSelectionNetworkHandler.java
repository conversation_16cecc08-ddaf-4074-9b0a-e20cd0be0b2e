package com.pokecobble.town.network.image;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.image.TownImageSelectionManager;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.UUID;

/**
 * Dedicated network handler for town image selection.
 * This system is completely independent from TownSettingsManager to avoid interference.
 */
public class TownImageSelectionNetworkHandler {
    
    /**
     * Registers all network handlers for image selection.
     */
    public static void registerHandlers() {
        // Handle slot image updates (when user uploads to a specific slot)
        ServerPlayNetworking.registerGlobalReceiver(NetworkConstants.TOWN_IMAGE_SLOT_UPDATE, 
            TownImageSelectionNetworkHandler::handleSlotUpdate);
            
        // Handle slot selection (when user selects which slot is active)
        ServerPlayNetworking.registerGlobalReceiver(NetworkConstants.TOWN_IMAGE_SLOT_SELECT, 
            TownImageSelectionNetworkHandler::handleSlotSelect);
            
        // Handle selection data requests
        ServerPlayNetworking.registerGlobalReceiver(NetworkConstants.TOWN_IMAGE_SELECTION_REQUEST, 
            TownImageSelectionNetworkHandler::handleSelectionRequest);
    }
    
    /**
     * Handles slot image update requests.
     */
    private static void handleSlotUpdate(MinecraftServer server, ServerPlayerEntity player,
                                       net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                       PacketByteBuf buf, net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID townId = buf.readUuid();
            int slot = buf.readInt();
            String imageName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            float scale = buf.readFloat();
            int offsetX = buf.readInt();
            int offsetY = buf.readInt();
            
            // Validate slot number
            if (slot < 0 || slot > 2) {
                Pokecobbleclaim.LOGGER.warn("Invalid slot number: " + slot);
                return;
            }
            
            // Validate town ownership
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Town not found: " + townId);
                return;
            }
            
            if (!town.getPlayers().contains(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " not in town " + town.getName());
                return;
            }
            
            // Update slot image and settings
            TownImageSelectionManager.setSlotImage(townId, slot, imageName);
            TownImageSelectionManager.updateSlotSettings(townId, slot, scale, offsetX, offsetY);

            // Update town object for compatibility with existing systems
            String selectedImageName = TownImageSelectionManager.getSelectedImageName(townId);
            if (selectedImageName != null) {
                town.setImage(selectedImageName);
                town.markChanged(Town.ASPECT_IMAGE);
            }

            // Sync to all town members
            syncSelectionToTownMembers(server, town);
            
            Pokecobbleclaim.LOGGER.info("Updated slot " + slot + " for town " + town.getName() + " with image: " + imageName);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling slot update: " + e.getMessage());
        }
    }
    
    /**
     * Handles slot selection requests.
     */
    private static void handleSlotSelect(MinecraftServer server, ServerPlayerEntity player,
                                       net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                       PacketByteBuf buf, net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID townId = buf.readUuid();
            int slot = buf.readInt();
            
            // Validate slot number (-1 means no selection)
            if (slot < -1 || slot > 2) {
                Pokecobbleclaim.LOGGER.warn("Invalid slot number: " + slot);
                return;
            }
            
            // Validate town ownership
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Town not found: " + townId);
                return;
            }
            
            if (!town.getPlayers().contains(player.getUuid())) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " not in town " + town.getName());
                return;
            }
            
            // Update selected slot
            TownImageSelectionManager.selectSlot(townId, slot);
            
            // Update town object for compatibility with existing systems
            String selectedImageName = TownImageSelectionManager.getSelectedImageName(townId);
            town.setImage(selectedImageName);
            town.markChanged(Town.ASPECT_IMAGE);
            
            // Sync to all town members
            syncSelectionToTownMembers(server, town);
            
            Pokecobbleclaim.LOGGER.info("Selected slot " + slot + " for town " + town.getName());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling slot selection: " + e.getMessage());
        }
    }
    
    /**
     * Handles selection data requests.
     */
    private static void handleSelectionRequest(MinecraftServer server, ServerPlayerEntity player,
                                             net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                             PacketByteBuf buf, net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID townId = buf.readUuid();
            
            // Validate town access
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                return;
            }
            
            // Send selection data to requesting player
            sendSelectionData(player, townId);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling selection request: " + e.getMessage());
        }
    }
    
    /**
     * Sends selection data to a specific player.
     */
    public static void sendSelectionData(ServerPlayerEntity player, UUID townId) {
        try {
            TownImageSelectionManager.TownImageSlotData data = TownImageSelectionManager.loadTownImageSelection(townId);
            
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(townId);
            buf.writeInt(data.selectedSlot);
            
            // Write slot data
            for (int i = 0; i < 3; i++) {
                String imageName = data.getSlotImage(i);
                buf.writeBoolean(imageName != null);
                if (imageName != null) {
                    buf.writeString(imageName, NetworkConstants.MAX_STRING_LENGTH);
                    
                    TownImageSelectionManager.TownImageSlotData.ImageSlotSettings settings = data.getSlotSettings(i);
                    buf.writeFloat(settings.scale);
                    buf.writeInt(settings.offsetX);
                    buf.writeInt(settings.offsetY);
                }
            }
            
            NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_IMAGE_SELECTION_SYNC, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending selection data: " + e.getMessage());
        }
    }
    
    /**
     * Syncs selection data to all town members.
     */
    public static void syncSelectionToTownMembers(MinecraftServer server, Town town) {
        try {
            for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
                if (town.getPlayers().contains(player.getUuid())) {
                    sendSelectionData(player, town.getId());
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing selection to town members: " + e.getMessage());
        }
    }
    
    /**
     * Syncs selection data to all online players (for public towns or when image is visible).
     */
    public static void syncSelectionToAllPlayers(MinecraftServer server, UUID townId) {
        try {
            for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
                sendSelectionData(player, townId);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing selection to all players: " + e.getMessage());
        }
    }
}
