package com.pokecobble.town.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.town.claim.v2.PermissionSystemV2;
import com.pokecobble.Pokecobbleclaim;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.math.ChunkPos;
import java.util.UUID;

/**
 * Debug command for testing permission system functionality.
 * Use this to test cache clearing and permission updates.
 */
public class PermissionDebugCommand {
    
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher, CommandRegistryAccess registryAccess) {
        dispatcher.register(CommandManager.literal("permission-debug")
            .requires(source -> source.hasPermissionLevel(2)) // Require OP level
            .then(CommandManager.literal("clear-cache")
                .executes(PermissionDebugCommand::clearCache))
            .then(CommandManager.literal("clear-player-cache")
                .executes(PermissionDebugCommand::clearPlayerCache))
            .then(CommandManager.literal("status")
                .executes(PermissionDebugCommand::showStatus))
            .then(CommandManager.literal("verify")
                .executes(PermissionDebugCommand::verifySystem))
            .then(CommandManager.literal("check-membership")
                .executes(PermissionDebugCommand::checkMembership))
            .then(CommandManager.literal("reload-chunk-data")
                .executes(PermissionDebugCommand::reloadChunkData))
            .then(CommandManager.literal("check-chunk-ownership")
                .executes(PermissionDebugCommand::checkChunkOwnership))
        );
    }
    
    private static int clearCache(CommandContext<ServerCommandSource> context) {
        try {
            if (PermissionSystemV2.isInitialized()) {
                PermissionSystemV2.getInstance().getPermissionEngine().clearCache();
                context.getSource().sendFeedback(() -> Text.literal("§a✓ Cleared all permission caches"), false);
                Pokecobbleclaim.LOGGER.info("Permission cache cleared via debug command");
            } else {
                context.getSource().sendFeedback(() -> Text.literal("§c✗ Permission System v2 is not initialized"), false);
            }
            return 1;
        } catch (Exception e) {
            context.getSource().sendFeedback(() -> Text.literal("§c✗ Error clearing cache: " + e.getMessage()), false);
            Pokecobbleclaim.LOGGER.error("Error in permission debug command", e);
            return 0;
        }
    }
    
    private static int clearPlayerCache(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity player = context.getSource().getPlayerOrThrow();
            ChunkPos chunkPos = new ChunkPos(player.getBlockPos());
            
            if (PermissionSystemV2.isInitialized()) {
                PermissionSystemV2.getInstance().getPermissionEngine()
                    .forceRefreshPlayerPermissions(player.getUuid(), chunkPos);
                context.getSource().sendFeedback(() -> Text.literal(
                    "§a✓ Cleared permission cache for player in chunk " + chunkPos), false);
                Pokecobbleclaim.LOGGER.info("Player permission cache cleared for {} in chunk {}", 
                    player.getName().getString(), chunkPos);
            } else {
                context.getSource().sendFeedback(() -> Text.literal("§c✗ Permission System v2 is not initialized"), false);
            }
            return 1;
        } catch (Exception e) {
            context.getSource().sendFeedback(() -> Text.literal("§c✗ Error clearing player cache: " + e.getMessage()), false);
            Pokecobbleclaim.LOGGER.error("Error in permission debug command", e);
            return 0;
        }
    }
    
    private static int showStatus(CommandContext<ServerCommandSource> context) {
        try {
            boolean v2Initialized = PermissionSystemV2.isInitialized();
            
            context.getSource().sendFeedback(() -> Text.literal("§6=== Permission System Status ==="), false);
            context.getSource().sendFeedback(() -> Text.literal(
                "Permission System v2: " + (v2Initialized ? "§a✓ Initialized" : "§c✗ Not Initialized")), false);
            
            if (v2Initialized) {
                String cacheStats = PermissionSystemV2.getInstance().getPermissionEngine().getCacheStats();
                context.getSource().sendFeedback(() -> Text.literal("Cache Stats: " + cacheStats), false);
                
                boolean healthCheck = PermissionSystemV2.getInstance().performHealthCheck();
                context.getSource().sendFeedback(() -> Text.literal(
                    "Health Check: " + (healthCheck ? "§a✓ Healthy" : "§c✗ Issues Detected")), false);
            }
            
            return 1;
        } catch (Exception e) {
            context.getSource().sendFeedback(() -> Text.literal("§c✗ Error getting status: " + e.getMessage()), false);
            Pokecobbleclaim.LOGGER.error("Error in permission debug command", e);
            return 0;
        }
    }
    
    private static int verifySystem(CommandContext<ServerCommandSource> context) {
        try {
            context.getSource().sendFeedback(() -> Text.literal("§6Running permission system verification..."), false);
            
            boolean verificationPassed = com.pokecobble.town.claim.PermissionSystemVerifier.verifyPermissionSystemFixes();
            
            if (verificationPassed) {
                context.getSource().sendFeedback(() -> Text.literal("§a✓ All permission system tests PASSED"), false);
            } else {
                context.getSource().sendFeedback(() -> Text.literal("§c✗ Some permission system tests FAILED - check server logs"), false);
            }
            
            return verificationPassed ? 1 : 0;
        } catch (Exception e) {
            context.getSource().sendFeedback(() -> Text.literal("§c✗ Error running verification: " + e.getMessage()), false);
            Pokecobbleclaim.LOGGER.error("Error in permission debug command", e);
            return 0;
        }
    }

    private static int checkMembership(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity player = context.getSource().getPlayerOrThrow();
            ChunkPos chunkPos = new ChunkPos(player.getBlockPos());

            context.getSource().sendFeedback(() -> Text.literal("§6=== Town Membership Check ==="), false);
            context.getSource().sendFeedback(() -> Text.literal("Player: " + player.getName().getString()), false);
            context.getSource().sendFeedback(() -> Text.literal("UUID: " + player.getUuid()), false);
            context.getSource().sendFeedback(() -> Text.literal("Current Chunk: " + chunkPos), false);

            // Check chunk ownership
            if (PermissionSystemV2.isInitialized()) {
                UUID owningTownId = PermissionSystemV2.getInstance().getPermissionEngine().getChunkOwner(chunkPos);

                context.getSource().sendFeedback(() -> Text.literal("Chunk Owner: " + (owningTownId != null ? owningTownId : "§cUnclaimed")), false);

                if (owningTownId != null) {
                    var townManager = com.pokecobble.town.TownManager.getInstance();
                    var town = townManager.getTown(owningTownId);

                    if (town != null) {
                        context.getSource().sendFeedback(() -> Text.literal("Town Name: " + town.getName()), false);
                        context.getSource().sendFeedback(() -> Text.literal("Town Players (" + town.getPlayers().size() + "): " + town.getPlayers()), false);

                        boolean isMember = town.getPlayers().contains(player.getUuid());
                        context.getSource().sendFeedback(() -> Text.literal("Is Member: " + (isMember ? "§a✓ YES" : "§c✗ NO")), false);

                        if (isMember) {
                            var playerRank = town.getPlayerRank(player.getUuid());
                            context.getSource().sendFeedback(() -> Text.literal("Player Rank: " + (playerRank != null ? playerRank : "§c✗ NULL")), false);
                        }

                        // Check all towns the player might be in
                        context.getSource().sendFeedback(() -> Text.literal("§6--- Checking All Towns ---"), false);
                        var allTowns = townManager.getAllTowns();
                        boolean foundInAnyTown = false;

                        for (var checkTown : allTowns) {
                            if (checkTown.getPlayers().contains(player.getUuid())) {
                                foundInAnyTown = true;
                                var rank = checkTown.getPlayerRank(player.getUuid());
                                context.getSource().sendFeedback(() -> Text.literal("§a✓ Found in town: " + checkTown.getName() + " (Rank: " + rank + ")"), false);
                            }
                        }

                        if (!foundInAnyTown) {
                            context.getSource().sendFeedback(() -> Text.literal("§c✗ Player not found in ANY town"), false);
                        }

                    } else {
                        context.getSource().sendFeedback(() -> Text.literal("§c✗ Town data not found for ID: " + owningTownId), false);
                    }
                } else {
                    context.getSource().sendFeedback(() -> Text.literal("§7Chunk is unclaimed"), false);
                }
            } else {
                context.getSource().sendFeedback(() -> Text.literal("§c✗ Permission System v2 not initialized"), false);
            }

            return 1;
        } catch (Exception e) {
            context.getSource().sendFeedback(() -> Text.literal("§c✗ Error checking membership: " + e.getMessage()), false);
            Pokecobbleclaim.LOGGER.error("Error in membership check command", e);
            return 0;
        }
    }

    private static int reloadChunkData(CommandContext<ServerCommandSource> context) {
        try {
            context.getSource().sendFeedback(() -> Text.literal("§6Reloading chunk ownership data..."), false);

            // Force reload all town chunk data
            com.pokecobble.town.chunk.TownChunkDataManager dataManager =
                com.pokecobble.town.chunk.TownChunkDataManager.getInstance();
            dataManager.loadAllTownData();

            context.getSource().sendFeedback(() -> Text.literal("§a✓ Chunk ownership data reloaded"), false);

            // Also clear permission caches
            if (PermissionSystemV2.isInitialized()) {
                PermissionSystemV2.getInstance().getPermissionEngine().clearCache();
                context.getSource().sendFeedback(() -> Text.literal("§a✓ Permission caches cleared"), false);
            }

            Pokecobbleclaim.LOGGER.info("Chunk ownership data reloaded via debug command");
            return 1;
        } catch (Exception e) {
            context.getSource().sendFeedback(() -> Text.literal("§c✗ Error reloading chunk data: " + e.getMessage()), false);
            Pokecobbleclaim.LOGGER.error("Error in chunk data reload command", e);
            return 0;
        }
    }

    private static int checkChunkOwnership(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity player = context.getSource().getPlayerOrThrow();
            ChunkPos chunkPos = new ChunkPos(player.getBlockPos());

            context.getSource().sendFeedback(() -> Text.literal("§6=== Chunk Ownership Debug ==="), false);
            context.getSource().sendFeedback(() -> Text.literal("Current Chunk: " + chunkPos), false);

            // Check chunk ownership through different systems
            com.pokecobble.town.chunk.TownChunkDataManager dataManager =
                com.pokecobble.town.chunk.TownChunkDataManager.getInstance();

            UUID ownerFromDataManager = dataManager.getChunkOwner(chunkPos);
            context.getSource().sendFeedback(() -> Text.literal("Owner from TownChunkDataManager: " +
                (ownerFromDataManager != null ? ownerFromDataManager : "§c✗ NULL")), false);

            if (PermissionSystemV2.isInitialized()) {
                UUID ownerFromPermissionSystem = PermissionSystemV2.getInstance().getPermissionEngine().getChunkOwner(chunkPos);
                context.getSource().sendFeedback(() -> Text.literal("Owner from Permission System v2: " +
                    (ownerFromPermissionSystem != null ? ownerFromPermissionSystem : "§c✗ NULL")), false);

                boolean match = (ownerFromDataManager == null && ownerFromPermissionSystem == null) ||
                               (ownerFromDataManager != null && ownerFromDataManager.equals(ownerFromPermissionSystem));
                context.getSource().sendFeedback(() -> Text.literal("Systems Match: " + (match ? "§a✓ YES" : "§c✗ NO")), false);
            }

            // Show chunk mapping statistics
            context.getSource().sendFeedback(() -> Text.literal("§6--- Chunk Mapping Statistics ---"), false);

            // Get all towns and check their chunk data
            var townManager = com.pokecobble.town.TownManager.getInstance();
            var allTowns = townManager.getAllTowns();
            final int[] totalClaimedChunks = {0};
            final int[] townsWithChunks = {0};

            for (var town : allTowns) {
                var townChunkData = dataManager.getTownChunkData(town.getId());
                if (townChunkData != null && !townChunkData.isEmpty()) {
                    int chunkCount = townChunkData.getTotalChunks();
                    totalClaimedChunks[0] += chunkCount;
                    townsWithChunks[0]++;

                    // Check if this town owns the current chunk
                    if (townChunkData.getAllChunks().contains(chunkPos)) {
                        context.getSource().sendFeedback(() -> Text.literal("§a✓ Current chunk found in town: " + town.getName() + " (" + chunkCount + " total chunks)"), false);
                    }
                }
            }

            context.getSource().sendFeedback(() -> Text.literal("Total Towns: " + allTowns.size()), false);
            context.getSource().sendFeedback(() -> Text.literal("Towns with Chunks: " + townsWithChunks[0]), false);
            context.getSource().sendFeedback(() -> Text.literal("Total Claimed Chunks: " + totalClaimedChunks[0]), false);

            // If chunk shows as unowned but should be owned, suggest fix
            if (ownerFromDataManager == null) {
                context.getSource().sendFeedback(() -> Text.literal("§c⚠ Chunk shows as unowned - try '/permission-debug reload-chunk-data'"), false);
            }

            return 1;
        } catch (Exception e) {
            context.getSource().sendFeedback(() -> Text.literal("§c✗ Error checking chunk ownership: " + e.getMessage()), false);
            Pokecobbleclaim.LOGGER.error("Error in chunk ownership check command", e);
            return 0;
        }
    }
}
