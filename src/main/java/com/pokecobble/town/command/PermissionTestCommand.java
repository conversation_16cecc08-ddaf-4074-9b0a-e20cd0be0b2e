package com.pokecobble.town.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.town.claim.test.SimplePermissionSystemTest;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.text.Text;

/**
 * Command to test the new simple permission system.
 */
public class PermissionTestCommand {
    
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher, CommandRegistryAccess registryAccess) {
        dispatcher.register(CommandManager.literal("permission-test")
            .requires(source -> source.hasPermissionLevel(2)) // Require OP level 2
            .executes(PermissionTestCommand::runTests));
    }
    
    private static int runTests(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        
        source.sendFeedback(() -> Text.literal("§6Running Simple Permission System Tests..."), false);
        
        try {
            boolean allPassed = SimplePermissionSystemTest.runAllTests();
            
            if (allPassed) {
                source.sendFeedback(() -> Text.literal("§a✓ All permission system tests PASSED! The new simple system is working correctly."), false);
                return 1; // Success
            } else {
                source.sendFeedback(() -> Text.literal("§c✗ Some permission system tests FAILED. Check server logs for details."), false);
                return 0; // Failure
            }
            
        } catch (Exception e) {
            source.sendFeedback(() -> Text.literal("§c✗ Permission system test failed with exception: " + e.getMessage()), false);
            return 0; // Failure
        }
    }
}
