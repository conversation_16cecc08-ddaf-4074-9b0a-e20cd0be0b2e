package com.pokecobble.town.gui.bank;

import com.pokecobble.town.Town;
import com.pokecobble.town.bank.TownBank;
import com.pokecobble.town.bank.TownBankManager;
import com.pokecobble.town.network.bank.TownBankNetworkHandler;
import com.pokecobble.economy.api.EconomyAPI;
import com.pokecobble.util.MoneyAPI;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;

/**
 * Screen for depositing money into the town bank.
 */
public class TownBankDepositScreen extends Screen {
    private final Screen parent;
    private final Town town;
    private TextFieldWidget amountField;
    private TextFieldWidget descriptionField;
    private String errorMessage = "";
    private long playerBalance = 0;
    private long townBankBalance = 0;
    
    // UI dimensions - dynamic sizing like MyTownScreen
    private int panelWidth = 500;
    private int panelHeight = 320;

    // Position fields for custom rendering
    private int panelX;
    private int panelY;
    private int contentY;

    // UI colors - matching MyTownScreen glass effect style
    private static final int BACKGROUND_COLOR = 0xCC000000; // Background color for masking

    // Glass Effect Color Palette - consistent with MyTownScreen styling
    private static final int GLASS_PANEL_BG = 0xD0101010;      // Main panel background
    private static final int GLASS_HEADER_BG = 0x60404040;     // Header glass background
    private static final int GLASS_CONTENT_BG = 0x30000000;    // Content area background
    private static final int GLASS_CARD_BG = 0x40303030;       // Card backgrounds
    private static final int GLASS_CARD_HOVER = 0x60404040;    // Card hover state

    // Glass effect highlights and shadows
    private static final int GLASS_TOP_HIGHLIGHT = 0x20FFFFFF;    // Top glass highlight
    private static final int GLASS_LEFT_HIGHLIGHT = 0x20FFFFFF;   // Left glass highlight
    private static final int GLASS_BRIGHT_HIGHLIGHT = 0x40FFFFFF; // Brighter highlights
    private static final int GLASS_INNER_HIGHLIGHT = 0x30FFFFFF;  // Inner glass highlights
    private static final int GLASS_SHADOW = 0x40000000;          // Glass shadows
    private static final int GLASS_BOTTOM_SHADOW = 0x20000000;   // Bottom shadows

    // Text colors
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;     // Primary text
    private static final int TEXT_SECONDARY = 0xFFB0B0B0;   // Secondary text
    private static final int TEXT_MUTED = 0xFF808080;       // Muted text
    private static final int ERROR_COLOR = 0xFFFF5555;      // Error text
    private static final int SUCCESS_COLOR = 0xFF4CAF50;    // Success/deposit theme
    
    public TownBankDepositScreen(Screen parent, Town town) {
        super(Text.literal("Town Bank - Deposit"));
        this.parent = parent;
        this.town = town;
        
        // Get current balances
        updateBalances();
    }
    
    @Override
    protected void init() {
        // Calculate panel dimensions - more compact for better fit
        panelWidth = Math.min(width - 40, 480);
        panelHeight = Math.min(height - 40, 300); // Reduced height for compact layout

        int panelX = (width - panelWidth) / 2;
        int panelY = (height - panelHeight) / 2;

        // Header height for modern layout
        int headerHeight = 28; // Slightly reduced
        int contentY = panelY + headerHeight + 5; // Reduced gap

        // Store positions for custom button rendering
        this.panelX = panelX;
        this.panelY = panelY;
        this.contentY = contentY;

        // Amount input field - positioned within the glass panel
        amountField = new TextFieldWidget(textRenderer, panelX + 20, contentY + 50, panelWidth - 40, 20, Text.literal("Amount"));
        amountField.setPlaceholder(Text.literal("Enter amount to deposit..."));
        amountField.setMaxLength(10);
        amountField.setChangedListener(this::onAmountChanged);
        amountField.setDrawsBackground(true);
        amountField.setVisible(true);
        amountField.setEditable(true);
        addSelectableChild(amountField);

        // Description input field - positioned within the glass panel
        descriptionField = new TextFieldWidget(textRenderer, panelX + 20, contentY + 85, panelWidth - 40, 20, Text.literal("Description"));
        descriptionField.setPlaceholder(Text.literal("Optional description..."));
        descriptionField.setMaxLength(100);
        descriptionField.setDrawsBackground(true);
        descriptionField.setVisible(true);
        descriptionField.setEditable(true);
        addSelectableChild(descriptionField);

        // Note: Buttons are now custom-rendered in the render() method
        // No ButtonWidget components needed - all handled with custom glass effect buttons
        
        // Set focus to amount field
        setInitialFocus(amountField);
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw background overlay
        context.fill(0, 0, width, height, BACKGROUND_COLOR);

        int panelX = (width - panelWidth) / 2;
        int panelY = (height - panelHeight) / 2;

        // Draw glass effect panel matching MyTownScreen style
        drawGlassPanel(context, panelX, panelY, panelWidth, panelHeight);

        // Draw glass effect header
        int headerHeight = 28; // Reduced header height
        drawGlassHeader(context, panelX, panelY, panelWidth, headerHeight);

        // Content area starts after header - more compact
        int contentY = panelY + headerHeight + 5;

        // Draw town name
        String townName = "Town: " + town.getName();
        context.drawTextWithShadow(textRenderer, townName, panelX + 20, contentY, TEXT_SECONDARY);

        // Draw balance information - more compact spacing
        String playerBalanceText = "Your Balance: " + formatMoney(playerBalance) + " coins";
        context.drawTextWithShadow(textRenderer, playerBalanceText, panelX + 20, contentY + 12, SUCCESS_COLOR);

        String bankBalanceText = "Town Bank: " + formatMoney(townBankBalance) + " coins";
        context.drawTextWithShadow(textRenderer, bankBalanceText, panelX + 20, contentY + 24, 0xFFAADDFF);

        // Draw field labels - adjusted for new positions
        context.drawTextWithShadow(textRenderer, "Amount:", panelX + 20, contentY + 40, TEXT_PRIMARY);
        context.drawTextWithShadow(textRenderer, "Description:", panelX + 20, contentY + 75, TEXT_PRIMARY);
        
        // Draw error message - positioned between description and buttons
        if (!errorMessage.isEmpty()) {
            int errorY = contentY + 105;
            context.drawTextWithShadow(textRenderer, errorMessage, panelX + 20, errorY, ERROR_COLOR);
        }

        // Draw custom buttons with MyTownScreen style
        drawCustomButtons(context, mouseX, mouseY);

        // Manually render text fields (like TownSettingsScreen does)
        if (amountField != null) {
            amountField.render(context, mouseX, mouseY, delta);
        }
        if (descriptionField != null) {
            descriptionField.render(context, mouseX, mouseY, delta);
        }

        super.render(context, mouseX, mouseY, delta);
    }
    
    private void onAmountChanged(String text) {
        errorMessage = ""; // Clear error when user types
    }


    
    private boolean canDeposit() {
        String amountText = amountField.getText().trim();

        if (!amountText.isEmpty()) {
            try {
                long amount = Long.parseLong(amountText);
                return amount > 0 && amount <= playerBalance;
            } catch (NumberFormatException e) {
                // Invalid number format
            }
        }

        return false;
    }
    
    private void performDeposit() {
        String amountText = amountField.getText().trim();
        String description = descriptionField.getText().trim();
        
        if (description.isEmpty()) {
            description = "Player deposit";
        }
        
        try {
            long amount = Long.parseLong(amountText);
            
            // Validate amount
            if (amount <= 0) {
                errorMessage = "Amount must be positive";
                return;
            }
            
            if (amount > playerBalance) {
                errorMessage = "Insufficient funds";
                return;
            }
            
            // Send deposit request to server
            TownBankNetworkHandler.sendDepositRequest(town.getId(), amount, description);
            
            // Close screen
            close();
            
        } catch (NumberFormatException e) {
            errorMessage = "Invalid amount format";
        }
    }
    
    private void updateBalances() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player != null) {
            // Get player balance
            try {
                EconomyAPI economyAPI = EconomyAPI.getInstance();
                if (economyAPI.isAvailable()) {
                    playerBalance = economyAPI.getBalance(client.player.getUuid());
                } else {
                    playerBalance = MoneyAPI.getBalance(client.player);
                }
            } catch (Exception e) {
                playerBalance = 0;
            }
            
            // Get town bank balance
            TownBank bank = TownBankManager.getInstance().getTownBank(town.getId());
            townBankBalance = bank.getBalance();
        }
    }
    
    private String formatMoney(long amount) {
        if (amount >= 1000000) {
            return String.format("%.1fM", amount / 1000000.0);
        } else if (amount >= 1000) {
            return String.format("%.1fK", amount / 1000.0);
        } else {
            return String.valueOf(amount);
        }
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            // Main action buttons - match the compact positioning
            int buttonY = contentY + 115;
            int buttonHeight = 22;

            // Deposit button
            int depositButtonWidth = 90;
            if (mouseX >= panelX + 20 && mouseX <= panelX + 20 + depositButtonWidth &&
                mouseY >= buttonY && mouseY <= buttonY + buttonHeight && canDeposit()) {
                performDeposit();
                return true;
            }

            // Cancel button
            int cancelButtonWidth = 70;
            int cancelButtonX = panelX + panelWidth - 20 - cancelButtonWidth;
            if (mouseX >= cancelButtonX && mouseX <= cancelButtonX + cancelButtonWidth &&
                mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {
                close();
                return true;
            }


        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public void close() {
        client.setScreen(parent);
    }

    @Override
    public boolean shouldPause() {
        return false;
    }

    /**
     * Draws a glass effect panel matching the MyTownScreen styling
     */
    private void drawGlassPanel(DrawContext context, int x, int y, int width, int height) {
        // Main panel background - darker glass effect
        context.fill(x, y, x + width, y + height, GLASS_PANEL_BG);

        // Glass effect borders - matching MyTownScreen style
        context.fill(x, y, x + width, y + 1, GLASS_BRIGHT_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, GLASS_TOP_HIGHLIGHT); // Right highlight
        context.fill(x, y + height - 1, x + width, y + height, GLASS_SHADOW); // Bottom shadow

        // Inner glass effect for depth
        context.fill(x + 1, y + 1, x + width - 1, y + 2, GLASS_TOP_HIGHLIGHT);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x15FFFFFF);
    }

    /**
     * Draws the glass effect header section matching MyTownScreen styling
     */
    private void drawGlassHeader(DrawContext context, int x, int y, int width, int height) {
        // Header background with glass effect - matching MyTownScreen headers
        context.fill(x, y, x + width, y + height, GLASS_HEADER_BG);

        // Glass effect borders - exactly like MyTownScreen headers
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight

        // Inner glass effect for depth
        context.fill(x + 1, y + 1, x + width - 1, y + 2, GLASS_TOP_HIGHLIGHT);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x15FFFFFF);

        // Header title - adjusted for smaller header
        String title = "💰 Deposit to Town Bank";
        int titleWidth = this.textRenderer.getWidth(title);
        context.drawTextWithShadow(this.textRenderer, title,
            x + (width - titleWidth) / 2, y + (height - 8) / 2, TEXT_PRIMARY);
    }

    /**
     * Draws custom buttons with MyTownScreen glass effect style
     */
    private void drawCustomButtons(DrawContext context, int mouseX, int mouseY) {
        // Main action buttons - more compact positioning
        int buttonY = contentY + 115; // Moved up significantly
        int buttonHeight = 22; // Slightly smaller

        // Deposit button
        int depositButtonWidth = 90; // Slightly smaller
        boolean depositHovered = mouseX >= panelX + 20 && mouseX <= panelX + 20 + depositButtonWidth &&
                                mouseY >= buttonY && mouseY <= buttonY + buttonHeight;
        boolean canDeposit = canDeposit();

        drawGlassButton(context, panelX + 20, buttonY, depositButtonWidth, buttonHeight,
                       SUCCESS_COLOR, depositHovered, canDeposit, "💰", "Deposit");

        // Cancel button
        int cancelButtonWidth = 70; // Smaller
        int cancelButtonX = panelX + panelWidth - 20 - cancelButtonWidth;
        boolean cancelHovered = mouseX >= cancelButtonX && mouseX <= cancelButtonX + cancelButtonWidth &&
                               mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

        drawGlassButton(context, cancelButtonX, buttonY, cancelButtonWidth, buttonHeight,
                       0xFF666666, cancelHovered, true, "✕", "Cancel");
    }

    /**
     * Draws a glass effect button matching MyTownScreen style
     */
    private void drawGlassButton(DrawContext context, int x, int y, int width, int height,
                                int color, boolean hovered, boolean enabled, String icon, String text) {
        // Extract RGB components from the accent color
        int red = (color >> 16) & 0xFF;
        int green = (color >> 8) & 0xFF;
        int blue = color & 0xFF;

        // Adjust colors based on enabled state
        if (!enabled) {
            red = green = blue = 0x60; // Gray out disabled buttons
        }

        // Create glass effect background
        int baseGlassAlpha = hovered ? 0x40 : 0x25;
        int baseGlassColor = (baseGlassAlpha << 24) | 0x303030;

        // Accent color overlay
        int accentAlpha = hovered ? 0x30 : 0x18;
        int accentOverlay = (accentAlpha << 24) | (red << 16) | (green << 8) | blue;

        // Draw base glass background
        context.fill(x, y, x + width, y + height, baseGlassColor);
        context.fill(x, y, x + width, y + height, accentOverlay);

        // Glass effect borders
        int topBorderColor = hovered ? 0x60FFFFFF : 0x40FFFFFF;
        int sideBorderColor = hovered ? 0x40FFFFFF : 0x25FFFFFF;
        int bottomBorderColor = hovered ? 0x30000000 : 0x50000000;

        context.fill(x, y, x + width, y + 1, topBorderColor);
        context.fill(x, y, x + 1, y + height, sideBorderColor);
        context.fill(x + width - 1, y, x + width, y + height, 0x25000000);
        context.fill(x, y + height - 1, x + width, y + height, bottomBorderColor);

        // Inner highlights
        if (hovered) {
            context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x50FFFFFF);
            context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x35FFFFFF);
        } else {
            context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x30FFFFFF);
            context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x20FFFFFF);
        }

        // Draw text
        String displayText = icon.isEmpty() ? text : icon + " " + text;
        int textWidth = this.textRenderer.getWidth(displayText);
        int textX = x + (width - textWidth) / 2;
        int textY = y + (height - this.textRenderer.fontHeight) / 2;

        int textColor = enabled ? (hovered ? 0xFFFFFFFF : 0xFFE0E0E0) : 0xFF808080;
        context.drawTextWithShadow(this.textRenderer, displayText, textX, textY, textColor);
    }
}
