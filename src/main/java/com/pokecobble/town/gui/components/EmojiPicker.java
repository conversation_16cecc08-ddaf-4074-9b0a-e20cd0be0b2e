package com.pokecobble.town.gui.components;

import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;

import java.util.function.Consumer;

/**
 * Reusable emoji picker component extracted from CreateTownScreen.
 */
public class EmojiPicker {
    private final TextRenderer textRenderer;
    private boolean isOpen = false;
    private Consumer<String> emojiSelectedCallback;
    
    // Emoji picker configuration
    private static final String[] EMOJI_LIST = {
        // Basic faces - well supported
        "😀", "😃", "😄", "😁", "😆", "😅", "😂", "😊", "😇", "🙂",
        "😉", "😌", "😍", "😘", "😗", "😙", "😚", "😋", "😛", "😝",
        "😜", "😎", "😏", "😒", "😞", "😔", "😟", "😕", "😣", "😖",
        "😫", "😩", "😢", "😭", "😤", "😠", "😡", "😳", "😱", "😨",
        "😰", "😥", "😓", "😶", "😐", "😑", "😬", "😯", "😦", "😧",
        "😮", "😲", "😴", "😪", "😵", "😈", "👿", "😺", "😸", "😹",
        "😻", "😼", "😽", "😿", "😾", "🙀",

        // Hearts and symbols - simple Unicode
        "❤", "💛", "💚", "💙", "💜", "🖤", "💔", "💕", "💞", "💓",
        "💗", "💖", "💘", "💝", "💟",

        // Simple symbols and objects
        "⭐", "✨", "💫", "⚡", "🔥", "💧", "🌟", "💎", "🎉", "🎊",
        "🎈", "🎁", "🏆", "🥇", "🥈", "🥉", "⚽", "🏀", "🏈", "⚾",
        "🎾", "🏐", "🏉", "🎱", "🎮", "🎯", "🎲", "🃏", "🎴", "🎭",
        "🎨", "🎪", "🎫", "🎬", "🎤", "🎧", "🎼", "🎵", "🎶", "🎹",

        // Food and drinks - simple ones
        "🍎", "🍊", "🍋", "🍌", "🍉", "🍇", "🍓", "🍈", "🍒", "🍑",
        "🍍", "🍅", "🍆", "🌶", "🌽", "🍞", "🧀", "🍖", "🍗", "🍔",
        "🍟", "🍕", "🌭", "🌮", "🌯", "🍳", "🍲", "🍱", "🍘", "🍙",
        "🍚", "🍛", "🍜", "🍝", "🍠", "🍢", "🍣", "🍤", "🍥", "🍡",
        "🍦", "🍧", "🍨", "🍩", "🍪", "🎂", "🍰", "🧁", "🍫", "🍬",
        "🍭", "🍮", "🍯", "🍼", "☕", "🍵", "🍶", "🍾", "🍷", "🍸",
        "🍹", "🍺", "🍻", "🥂", "🥃",

        // Nature and weather
        "🌞", "🌝", "🌛", "🌜", "🌚", "🌕", "🌖", "🌗", "🌘", "🌑",
        "🌒", "🌓", "🌔", "🌙", "⭐", "🌟", "💫", "✨", "☀", "⛅",
        "☁", "🌤", "⛈", "🌦", "🌧", "⚡", "❄", "☃", "⛄", "🌈",

        // Basic symbols
        "✅", "❌", "⭕", "❗", "❓", "💯", "🔥", "💧", "⚡", "✨"
    };
    
    private static final int EMOJI_PICKER_COLS = 10;
    private static final int EMOJI_PICKER_ROWS = 8;
    private static final int EMOJI_SIZE = 16;
    private static final int EMOJI_PADDING = 2;
    
    // Colors
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;
    
    public EmojiPicker(TextRenderer textRenderer) {
        this.textRenderer = textRenderer;
    }
    
    public void setEmojiSelectedCallback(Consumer<String> callback) {
        this.emojiSelectedCallback = callback;
    }
    
    public boolean isOpen() {
        return isOpen;
    }
    
    public void setOpen(boolean open) {
        this.isOpen = open;
    }
    
    public void toggle() {
        this.isOpen = !this.isOpen;
    }
    
    public void render(DrawContext context, int screenWidth, int screenHeight, int mouseX, int mouseY) {
        if (!isOpen) return;
        
        // Push matrix to ensure highest z-index
        context.getMatrices().push();
        context.getMatrices().translate(0, 0, 10000); // Move very far forward in z-space

        // Draw semi-transparent overlay behind the picker
        context.fill(0, 0, screenWidth, screenHeight, 0x80000000);

        int pickerWidth = EMOJI_PICKER_COLS * (EMOJI_SIZE + EMOJI_PADDING) + EMOJI_PADDING * 2;
        int pickerHeight = EMOJI_PICKER_ROWS * (EMOJI_SIZE + EMOJI_PADDING) + EMOJI_PADDING * 2 + 25; // +25 for header

        // Center the picker on screen
        int pickerX = (screenWidth - pickerWidth) / 2;
        int pickerY = (screenHeight - pickerHeight) / 2;

        // Draw picker background
        context.fill(pickerX, pickerY, pickerX + pickerWidth, pickerY + pickerHeight, 0xFF2D2D30);
        context.drawBorder(pickerX, pickerY, pickerWidth, pickerHeight, 0xFF606060);

        // Draw header
        context.drawCenteredTextWithShadow(textRenderer, "Select Emoji", 
            pickerX + pickerWidth / 2, pickerY + 8, TEXT_PRIMARY);

        // Draw emojis in grid
        int emojiIndex = 0;
        for (int row = 0; row < EMOJI_PICKER_ROWS && emojiIndex < EMOJI_LIST.length; row++) {
            for (int col = 0; col < EMOJI_PICKER_COLS && emojiIndex < EMOJI_LIST.length; col++) {
                int emojiX = pickerX + EMOJI_PADDING * 2 + col * (EMOJI_SIZE + EMOJI_PADDING);
                int emojiY = pickerY + 25 + EMOJI_PADDING + row * (EMOJI_SIZE + EMOJI_PADDING); // +25 for header

                boolean emojiHovered = isMouseOver(mouseX, mouseY, emojiX, emojiY, EMOJI_SIZE, EMOJI_SIZE);

                // Draw hover background
                if (emojiHovered) {
                    context.fill(emojiX - 2, emojiY - 2, emojiX + EMOJI_SIZE + 2, emojiY + EMOJI_SIZE + 2, 0xFF505050);
                    context.drawBorder(emojiX - 2, emojiY - 2, EMOJI_SIZE + 4, EMOJI_SIZE + 4, 0xFF707070);
                }

                // Draw emoji
                String emoji = EMOJI_LIST[emojiIndex];
                context.drawCenteredTextWithShadow(textRenderer, emoji,
                    emojiX + EMOJI_SIZE / 2, emojiY + (EMOJI_SIZE - 8) / 2, TEXT_PRIMARY);

                emojiIndex++;
            }
        }

        // Pop matrix to restore original z-index
        context.getMatrices().pop();
    }
    
    public boolean mouseClicked(double mouseX, double mouseY, int screenWidth, int screenHeight) {
        if (!isOpen) return false;
        
        String clickedEmoji = getClickedEmoji(mouseX, mouseY, screenWidth, screenHeight);
        if (clickedEmoji != null) {
            // Emoji was clicked
            if (emojiSelectedCallback != null) {
                emojiSelectedCallback.accept(clickedEmoji);
            }
            setOpen(false); // Close picker after selection
            return true;
        } else {
            // Check if click is within picker bounds
            if (isClickInPickerBounds(mouseX, mouseY, screenWidth, screenHeight)) {
                return true; // Consume click to prevent other handlers
            } else {
                // Click outside picker - close it
                setOpen(false);
                return true; // Consume click to prevent other handlers
            }
        }
    }
    
    private String getClickedEmoji(double mouseX, double mouseY, int screenWidth, int screenHeight) {
        int pickerWidth = EMOJI_PICKER_COLS * (EMOJI_SIZE + EMOJI_PADDING) + EMOJI_PADDING * 2;
        int pickerHeight = EMOJI_PICKER_ROWS * (EMOJI_SIZE + EMOJI_PADDING) + EMOJI_PADDING * 2 + 25; // +25 for header

        // Calculate picker position
        int pickerX = (screenWidth - pickerWidth) / 2;
        int pickerY = (screenHeight - pickerHeight) / 2;

        // Check if click is within picker bounds (excluding header area)
        if (mouseX < pickerX || mouseX > pickerX + pickerWidth ||
            mouseY < pickerY + 25 || mouseY > pickerY + pickerHeight) { // +25 for header
            return null;
        }

        // Calculate which emoji was clicked
        int relativeX = (int)(mouseX - pickerX - EMOJI_PADDING * 2);
        int relativeY = (int)(mouseY - pickerY - 25 - EMOJI_PADDING); // -25 for header

        int col = relativeX / (EMOJI_SIZE + EMOJI_PADDING);
        int row = relativeY / (EMOJI_SIZE + EMOJI_PADDING);

        if (col >= 0 && col < EMOJI_PICKER_COLS && row >= 0 && row < EMOJI_PICKER_ROWS) {
            int emojiIndex = row * EMOJI_PICKER_COLS + col;
            if (emojiIndex < EMOJI_LIST.length) {
                return EMOJI_LIST[emojiIndex];
            }
        }

        return null;
    }
    
    private boolean isClickInPickerBounds(double mouseX, double mouseY, int screenWidth, int screenHeight) {
        int pickerWidth = EMOJI_PICKER_COLS * (EMOJI_SIZE + EMOJI_PADDING) + EMOJI_PADDING * 2;
        int pickerHeight = EMOJI_PICKER_ROWS * (EMOJI_SIZE + EMOJI_PADDING) + EMOJI_PADDING * 2 + 25; // +25 for header

        // Calculate picker position
        int pickerX = (screenWidth - pickerWidth) / 2;
        int pickerY = (screenHeight - pickerHeight) / 2;

        // Check if click is within picker bounds
        return mouseX >= pickerX && mouseX <= pickerX + pickerWidth &&
               mouseY >= pickerY && mouseY <= pickerY + pickerHeight;
    }
    
    private boolean isMouseOver(int mouseX, int mouseY, int x, int y, int width, int height) {
        return mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;
    }
}
