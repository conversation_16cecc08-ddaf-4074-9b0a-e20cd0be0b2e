package com.pokecobble.town.gui.chat;

import com.pokecobble.town.network.chat.TownChatClientManager;
import com.pokecobble.town.chat.TownChatMessage;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Scrollable chat display area that shows town chat messages.
 */
public class ChatDisplayArea {

    /**
     * Represents a group of consecutive messages from the same player.
     */
    private static class MessageGroup {
        private final TownChatClientManager.ClientChatMessage headerMessage; // First message for player info
        private final List<TownChatClientManager.ClientChatMessage> messages;

        public MessageGroup(TownChatClientManager.ClientChatMessage headerMessage) {
            this.headerMessage = headerMessage;
            this.messages = new ArrayList<>();
            this.messages.add(headerMessage);
        }

        public void addMessage(TownChatClientManager.ClientChatMessage message) {
            this.messages.add(message);
        }

        public TownChatClientManager.ClientChatMessage getHeaderMessage() {
            return headerMessage;
        }

        public List<TownChatClientManager.ClientChatMessage> getMessages() {
            return messages;
        }

        public boolean canAddMessage(TownChatClientManager.ClientChatMessage message) {
            // Can add if same player and same message type
            return headerMessage.getPlayerId().equals(message.getPlayerId()) &&
                   headerMessage.getType() == message.getType();
        }
    }
    private final TextRenderer textRenderer;
    private int x, y, width, height;
    private UUID townId;
    private int scrollOffset = 0;
    private boolean isDragging = false;
    private int lastMouseY = 0;

    // Styling constants - matching MyTownScreen glass aesthetic
    private static final int BACKGROUND_COLOR = 0x20000000; // More transparent for glass effect
    private static final int BORDER_COLOR = 0x40FFFFFF; // Glass highlight border
    private static final int MESSAGE_PADDING = 12; // Increased padding for better spacing
    private static final int MESSAGE_SPACING = 4; // Increased spacing between messages
    private static final int LINE_HEIGHT = 14; // Slightly taller lines for better readability
    private static final int SCROLLBAR_WIDTH = 8; // Wider scrollbar for better visibility
    private static final int SCROLLBAR_COLOR = 0x60404040; // Glass-style scrollbar
    private static final int SCROLLBAR_HANDLE_COLOR = 0x80FFFFFF; // Brighter handle

    // Message colors - enhanced for better contrast on glass background
    private static final int PLAYER_NAME_COLOR = 0xFF66BB6A; // Slightly brighter green
    private static final int SYSTEM_NAME_COLOR = 0xFFFFB74D; // Warmer orange
    private static final int ANNOUNCEMENT_NAME_COLOR = 0xFFEC407A; // Softer pink
    private static final int MESSAGE_TEXT_COLOR = 0xFFFFFFFF; // Pure white for contrast
    private static final int TIMESTAMP_COLOR = 0xFFB0B0B0; // Lighter gray for better visibility

    // Glass effect colors
    private static final int GLASS_MESSAGE_BG = 0x15FFFFFF; // Subtle message background
    private static final int GLASS_MESSAGE_HOVER = 0x25FFFFFF; // Hover effect for messages

    public ChatDisplayArea(TextRenderer textRenderer, int x, int y, int width, int height) {
        this.textRenderer = textRenderer;
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }

    public void render(DrawContext context, int mouseX, int mouseY, UUID townId) {
        this.townId = townId;

        // Draw glass-style background with subtle transparency
        context.fill(x, y, x + width, y + height, BACKGROUND_COLOR);

        // Draw glass effect borders for depth
        context.fill(x, y, x + width, y + 1, BORDER_COLOR); // Top highlight
        context.fill(x, y, x + 1, y + height, BORDER_COLOR); // Left highlight

        // Get chat messages
        List<TownChatClientManager.ClientChatMessage> messages =
            TownChatClientManager.getInstance().getChatHistory(townId);

        if (messages.isEmpty()) {
            // Show enhanced empty state with better styling
            String emptyText = "No messages yet. Start a conversation!";
            int textWidth = textRenderer.getWidth(emptyText);
            int textX = x + (width - textWidth) / 2;
            int textY = y + height / 2 - LINE_HEIGHT / 2;

            // Draw subtle background for empty text
            context.fill(textX - 8, textY - 4, textX + textWidth + 8, textY + LINE_HEIGHT + 4, 0x20FFFFFF);
            context.drawTextWithShadow(textRenderer, Text.literal(emptyText).formatted(Formatting.ITALIC),
                textX, textY, TIMESTAMP_COLOR);
            return;
        }

        // Calculate total content height with safety checks
        int totalContentHeight = 0;
        int maxScrollOffset = 0;

        try {
            if (height > MESSAGE_PADDING * 2) {
                totalContentHeight = calculateContentHeight(messages);
                maxScrollOffset = Math.max(0, totalContentHeight - (height - MESSAGE_PADDING * 2));
            }
        } catch (Exception e) {
            // Reset on error to prevent crashes
            totalContentHeight = 0;
            maxScrollOffset = 0;
        }

        // Clamp scroll offset
        scrollOffset = Math.max(0, Math.min(scrollOffset, maxScrollOffset));

        // Enable scissor to prevent overflow outside the chat area
        context.enableScissor(x, y, x + width, y + height);

        // Render messages
        renderMessages(context, messages);

        // Render scrollbar if needed
        if (maxScrollOffset > 0) {
            renderScrollbar(context, maxScrollOffset);
        }

        // Disable scissor after rendering
        context.disableScissor();
    }

    private void renderMessages(DrawContext context, List<TownChatClientManager.ClientChatMessage> messages) {
        int currentY = y + MESSAGE_PADDING - scrollOffset;
        int contentWidth = width - MESSAGE_PADDING * 2 - (hasScrollbar(messages) ? SCROLLBAR_WIDTH + 4 : 0);

        // Group messages by consecutive players
        List<MessageGroup> messageGroups = groupConsecutiveMessages(messages);

        for (MessageGroup group : messageGroups) {
            // Skip groups that are above the visible area
            int groupHeight = calculateGroupHeight(group, contentWidth);
            if (currentY + groupHeight < y) {
                currentY += groupHeight + MESSAGE_SPACING;
                continue;
            }

            // Stop rendering if we're below the visible area
            if (currentY > y + height) {
                break;
            }

            // Render message group
            currentY = renderMessageGroup(context, group, x + MESSAGE_PADDING, currentY, contentWidth);
            currentY += MESSAGE_SPACING;
        }
    }



    private int getNameColor(TownChatMessage.MessageType type) {
        switch (type) {
            case SYSTEM_MESSAGE:
                return SYSTEM_NAME_COLOR;
            case ANNOUNCEMENT:
                return ANNOUNCEMENT_NAME_COLOR;
            case PLAYER_MESSAGE:
            default:
                return PLAYER_NAME_COLOR;
        }
    }

    private List<String> wrapText(String text, int maxWidth) {
        List<String> lines = new ArrayList<>();
        if (text.isEmpty()) {
            lines.add("");
            return lines;
        }

        // Split by existing newlines first to preserve intentional line breaks
        String[] existingLines = text.split("\n", -1);

        for (String line : existingLines) {
            if (line.isEmpty()) {
                lines.add("");
                continue;
            }

            // Wrap each line to fit the width
            String[] words = line.split(" ");
            StringBuilder currentLine = new StringBuilder();

            for (String word : words) {
                String testLine = currentLine.length() == 0 ? word : currentLine + " " + word;

                if (textRenderer.getWidth(testLine) <= maxWidth) {
                    currentLine = new StringBuilder(testLine);
                } else {
                    // Current line is full, start a new line
                    if (currentLine.length() > 0) {
                        lines.add(currentLine.toString());
                        currentLine = new StringBuilder(word);
                    } else {
                        // Single word is too long, break it character by character
                        StringBuilder wordBuilder = new StringBuilder();
                        for (char c : word.toCharArray()) {
                            String testChar = wordBuilder + String.valueOf(c);
                            if (textRenderer.getWidth(testChar) <= maxWidth) {
                                wordBuilder.append(c);
                            } else {
                                if (wordBuilder.length() > 0) {
                                    lines.add(wordBuilder.toString());
                                    wordBuilder = new StringBuilder(String.valueOf(c));
                                } else {
                                    // Even single character doesn't fit, add it anyway
                                    lines.add(String.valueOf(c));
                                }
                            }
                        }
                        if (wordBuilder.length() > 0) {
                            currentLine = wordBuilder;
                        }
                    }
                }
            }

            // Add remaining text in current line
            if (currentLine.length() > 0) {
                lines.add(currentLine.toString());
            }
        }

        return lines.isEmpty() ? List.of("") : lines;
    }

    /**
     * Groups consecutive messages from the same player together.
     */
    private List<MessageGroup> groupConsecutiveMessages(List<TownChatClientManager.ClientChatMessage> messages) {
        List<MessageGroup> groups = new ArrayList<>();

        for (TownChatClientManager.ClientChatMessage message : messages) {
            if (groups.isEmpty()) {
                // First message, create new group
                groups.add(new MessageGroup(message));
            } else {
                MessageGroup lastGroup = groups.get(groups.size() - 1);
                if (lastGroup.canAddMessage(message)) {
                    // Same player, add to existing group
                    lastGroup.addMessage(message);
                } else {
                    // Different player, create new group
                    groups.add(new MessageGroup(message));
                }
            }
        }

        return groups;
    }

    /**
     * Calculates the height of a message group.
     */
    private int calculateGroupHeight(MessageGroup group, int messageWidth) {
        int totalHeight = 0;

        // Header height (player name without timestamp)
        totalHeight += LINE_HEIGHT + 4; // Name line + padding

        // Calculate height for all messages in the group
        for (TownChatClientManager.ClientChatMessage message : group.getMessages()) {
            List<String> wrappedLines = wrapText(message.getMessage(), messageWidth - 16); // 16px indent
            totalHeight += wrappedLines.size() * LINE_HEIGHT + 2; // Message lines + small spacing
        }

        return totalHeight + 8; // Extra padding for group background
    }

    /**
     * Renders a group of messages from the same player.
     */
    private int renderMessageGroup(DrawContext context, MessageGroup group, int messageX, int messageY, int messageWidth) {
        int currentY = messageY;

        // Get header message for player info
        TownChatClientManager.ClientChatMessage headerMessage = group.getHeaderMessage();
        int nameColor = getNameColor(headerMessage.getType());

        // Calculate total group height for background
        int groupHeight = calculateGroupHeight(group, messageWidth);

        // Draw group background with glass effect
        context.fill(messageX - 4, messageY - 2, messageX + messageWidth - 4, messageY + groupHeight - 2, GLASS_MESSAGE_BG);

        // Render player name header (no timestamp)
        String displayName = String.format("%s:", headerMessage.getDisplayName());
        context.drawTextWithShadow(textRenderer, displayName, messageX, currentY, nameColor);
        currentY += LINE_HEIGHT + 4; // Move down after name with extra spacing

        // Render all messages in the group
        for (TownChatClientManager.ClientChatMessage message : group.getMessages()) {
            List<String> wrappedLines = wrapText(message.getMessage(), messageWidth - 16); // 16px indent

            for (String line : wrappedLines) {
                context.drawTextWithShadow(textRenderer, line, messageX + 16, currentY, MESSAGE_TEXT_COLOR); // 16px indent
                currentY += LINE_HEIGHT;
            }
            currentY += 2; // Small spacing between messages in the same group
        }

        return currentY + 6; // Extra spacing after group
    }



    private int calculateContentHeight(List<TownChatClientManager.ClientChatMessage> messages) {
        int totalHeight = 0;
        // Calculate content width without scrollbar first to avoid circular dependency
        int contentWidth = width - MESSAGE_PADDING * 2;

        // Group messages and calculate height for groups
        List<MessageGroup> messageGroups = groupConsecutiveMessages(messages);
        for (MessageGroup group : messageGroups) {
            totalHeight += calculateGroupHeight(group, contentWidth) + MESSAGE_SPACING;
        }

        // If content would need scrolling, recalculate with scrollbar space
        if (totalHeight > height - MESSAGE_PADDING * 2) {
            totalHeight = 0;
            contentWidth = width - MESSAGE_PADDING * 2 - SCROLLBAR_WIDTH - 4;

            for (MessageGroup group : messageGroups) {
                totalHeight += calculateGroupHeight(group, contentWidth) + MESSAGE_SPACING;
            }
        }

        return totalHeight;
    }

    private boolean hasScrollbar(List<TownChatClientManager.ClientChatMessage> messages) {
        // Simple check without circular dependency
        int contentWidth = width - MESSAGE_PADDING * 2;
        int totalHeight = 0;

        // Group messages and calculate height for groups
        List<MessageGroup> messageGroups = groupConsecutiveMessages(messages);
        for (MessageGroup group : messageGroups) {
            totalHeight += calculateGroupHeight(group, contentWidth) + MESSAGE_SPACING;
        }

        return totalHeight > height - MESSAGE_PADDING * 2;
    }

    private void renderScrollbar(DrawContext context, int maxScrollOffset) {
        int scrollbarX = x + width - SCROLLBAR_WIDTH - 2;
        int scrollbarY = y + 2;
        int scrollbarHeight = height - 4;

        // Draw scrollbar track
        context.fill(scrollbarX, scrollbarY, scrollbarX + SCROLLBAR_WIDTH, scrollbarY + scrollbarHeight, SCROLLBAR_COLOR);

        // Calculate handle position and size
        int contentHeight = height - MESSAGE_PADDING * 2;
        int totalHeight = maxScrollOffset + contentHeight;
        int handleHeight = Math.max(10, (scrollbarHeight * contentHeight) / totalHeight);
        int handleY = scrollbarY + (scrollOffset * (scrollbarHeight - handleHeight)) / maxScrollOffset;

        // Draw scrollbar handle
        context.fill(scrollbarX, handleY, scrollbarX + SCROLLBAR_WIDTH, handleY + handleHeight, SCROLLBAR_HANDLE_COLOR);
    }

    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0 && isInBounds(mouseX, mouseY)) {
            // Check if clicking on scrollbar
            int scrollbarX = x + width - SCROLLBAR_WIDTH - 2;
            if (mouseX >= scrollbarX && mouseX <= scrollbarX + SCROLLBAR_WIDTH) {
                isDragging = true;
                lastMouseY = (int) mouseY;
                return true;
            }
        }
        return false;
    }

    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (isDragging && townId != null && height > 4) {
            try {
                List<TownChatClientManager.ClientChatMessage> messages =
                    TownChatClientManager.getInstance().getChatHistory(townId);

                if (messages != null) {
                    int totalContentHeight = calculateContentHeight(messages);
                    int maxScrollOffset = Math.max(0, totalContentHeight - (height - MESSAGE_PADDING * 2));

                    if (maxScrollOffset > 0) {
                        int deltaScroll = (int) ((deltaY * maxScrollOffset) / (height - 4));
                        scrollOffset = Math.max(0, Math.min(scrollOffset + deltaScroll, maxScrollOffset));
                    }
                }
            } catch (Exception e) {
                // Prevent crashes from scrolling issues
                scrollOffset = 0;
            }
            return true;
        }
        return false;
    }

    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (button == 0) {
            isDragging = false;
            return true;
        }
        return false;
    }

    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        if (isInBounds(mouseX, mouseY) && townId != null && height > 0) {
            try {
                List<TownChatClientManager.ClientChatMessage> messages =
                    TownChatClientManager.getInstance().getChatHistory(townId);

                if (messages != null) {
                    int totalContentHeight = calculateContentHeight(messages);
                    int maxScrollOffset = Math.max(0, totalContentHeight - (height - MESSAGE_PADDING * 2));

                    if (maxScrollOffset > 0) {
                        int scrollAmount = (int) (amount * 20); // Scroll speed
                        scrollOffset = Math.max(0, Math.min(scrollOffset - scrollAmount, maxScrollOffset));
                        return true;
                    }
                }
            } catch (Exception e) {
                // Prevent crashes from scrolling issues
                scrollOffset = 0;
            }
        }
        return false;
    }

    private boolean isInBounds(double mouseX, double mouseY) {
        return mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;
    }

    public void scrollToBottom() {
        if (townId != null && height > 0) {
            try {
                List<TownChatClientManager.ClientChatMessage> messages =
                    TownChatClientManager.getInstance().getChatHistory(townId);

                if (messages != null && !messages.isEmpty()) {
                    int totalContentHeight = calculateContentHeight(messages);
                    int maxScrollOffset = Math.max(0, totalContentHeight - (height - MESSAGE_PADDING * 2));
                    scrollOffset = Math.max(0, maxScrollOffset);
                }
            } catch (Exception e) {
                // Prevent crashes from scrolling issues
                scrollOffset = 0;
            }
        }
    }

    public void updatePosition(int newX, int newY, int newWidth, int newHeight) {
        this.x = newX;
        this.y = newY;
        this.width = newWidth;
        this.height = newHeight;
    }

    public int getX() { return x; }
    public int getY() { return y; }
    public int getWidth() { return width; }
    public int getHeight() { return height; }
}
