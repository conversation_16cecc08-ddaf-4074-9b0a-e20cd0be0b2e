package com.pokecobble.town.gui.chat;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.gui.components.EmojiPicker;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * Animated chat input box that expands when lines are added with Enter key.
 * Similar to CreateTownScreen's CustomTextBox but with line-change animation.
 */
public class AnimatedChatInputBox {
    private final TextRenderer textRenderer;
    private int x, y, width;
    private int height;
    private final String placeholder;
    private final int maxLength;
    private String text = "";
    private boolean focused = false;
    private boolean hovered = false;
    private long lastCursorBlink = 0;
    private int cursorPosition = 0;
    private Consumer<String> changedListener;
    private Consumer<String> submitListener; // Called when Enter is pressed on single line

    // Emoji picker
    private EmojiPicker emojiPicker;
    private boolean showEmojiButton = true;

    // Animation system for line changes
    private boolean isAnimating = false;
    private long animationStartTime = 0;
    private static final long ANIMATION_DURATION = 200; // 200ms animation
    private float animationProgress = 0.0f;
    private int targetHeight;
    private int startHeight;
    private int previousLineCount = 1;

    // Styling constants - matching MyTownScreen glass aesthetic
    private static final int BACKGROUND_COLOR = 0x40303030; // Glass-style background
    private static final int BORDER_COLOR = 0x60FFFFFF; // Glass highlight border
    private static final int FOCUSED_BORDER_COLOR = 0x80FFFFFF; // Brighter when focused
    private static final int TEXT_COLOR = 0xFFFFFFFF;
    private static final int PLACEHOLDER_COLOR = 0xFFB0B0B0; // Lighter placeholder for better visibility
    private static final int LINE_HEIGHT = 12; // Reduced line height for more compact appearance
    private static final int PADDING = 4; // Reduced padding for smaller vertical size
    private static final int MIN_HEIGHT = 20; // Smaller minimum height for more compact input
    private static final int MAX_LINES = 4;

    // Glass effect colors
    private static final int GLASS_INNER_HIGHLIGHT = 0x30FFFFFF;
    private static final int GLASS_SHADOW = 0x40000000;

    public AnimatedChatInputBox(TextRenderer textRenderer, int x, int y, int width, String placeholder, int maxLength) {
        this.textRenderer = textRenderer;
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = MIN_HEIGHT;
        this.placeholder = placeholder;
        this.maxLength = maxLength;
        this.targetHeight = MIN_HEIGHT;
        this.startHeight = MIN_HEIGHT;

        // Initialize emoji picker
        this.emojiPicker = new EmojiPicker(textRenderer);
        this.emojiPicker.setEmojiSelectedCallback(this::insertEmojiAtCursor);
    }

    public void render(DrawContext context, int mouseX, int mouseY) {
        // Update animation
        updateAnimation();

        // Check if mouse is hovering
        hovered = mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;

        // Draw glass-style background
        context.fill(x, y, x + width, y + height, BACKGROUND_COLOR);

        // Draw glass effect borders
        int borderColor = focused ? FOCUSED_BORDER_COLOR : BORDER_COLOR;
        context.fill(x, y, x + width, y + 1, borderColor); // Top highlight
        context.fill(x, y, x + 1, y + height, borderColor); // Left highlight

        // Add inner glass highlights for depth
        if (focused) {
            context.fill(x + 1, y + 1, x + width - 1, y + 2, GLASS_INNER_HIGHLIGHT);
            context.fill(x + 1, y + 1, x + 2, y + height - 1, GLASS_INNER_HIGHLIGHT);
        }

        // Subtle bottom shadow for depth
        context.fill(x + 1, y + height - 1, x + width, y + height, GLASS_SHADOW);

        // Enable scissor to prevent text overflow outside the input box
        context.enableScissor(x + 1, y + 1, x + width - 1, y + height - 1);

        // Draw text or placeholder
        int textX = x + PADDING;
        int textY = y + PADDING;

        if (text.isEmpty() && !focused) {
            // Draw placeholder
            context.drawTextWithShadow(textRenderer, placeholder, textX, textY, PLACEHOLDER_COLOR);
        } else {
            // Auto-wrap text to fit within the input box width
            List<String> wrappedLines = wrapTextToWidth(text, width - PADDING * 2);

            for (int i = 0; i < wrappedLines.size() && i < MAX_LINES; i++) {
                String line = wrappedLines.get(i);
                int lineY = textY + i * LINE_HEIGHT;

                // Only draw if line fits in the box
                if (lineY + LINE_HEIGHT <= y + height - PADDING) {
                    context.drawTextWithShadow(textRenderer, line, textX, lineY, TEXT_COLOR);
                }
            }

            // Draw cursor if focused
            if (focused && (System.currentTimeMillis() - lastCursorBlink) % 1000 < 500) {
                drawCursor(context, textX, textY);
            }
        }

        // Disable scissor after text rendering
        context.disableScissor();

        // Draw emoji button if enabled
        if (showEmojiButton) {
            drawEmojiButton(context);
        }
    }

    private void drawCursor(DrawContext context, int textX, int textY) {
        // Calculate cursor position based on cursorPosition in text
        String textBeforeCursor = text.substring(0, Math.min(cursorPosition, text.length()));
        String[] linesBeforeCursor = textBeforeCursor.split("\n", -1);

        int cursorLineIndex = linesBeforeCursor.length - 1;
        String currentLine = linesBeforeCursor[cursorLineIndex];

        int cursorX = textX + textRenderer.getWidth(currentLine);
        int cursorY = textY + cursorLineIndex * LINE_HEIGHT;

        // Only draw cursor if it's visible in the box
        if (cursorX < x + width - PADDING && cursorLineIndex < MAX_LINES && 
            cursorY + LINE_HEIGHT <= y + height - PADDING) {
            context.fill(cursorX, cursorY, cursorX + 1, cursorY + LINE_HEIGHT, TEXT_COLOR);
        }
    }

    private void drawEmojiButton(DrawContext context) {
        // Draw emoji button in the bottom-right corner of the input box
        int buttonSize = 16;
        int buttonX = x + width - buttonSize - 4;
        int buttonY = y + height - buttonSize - 4;

        // Draw button background
        int buttonBg = emojiPicker.isOpen() ? 0xFF505050 : 0xFF404040;
        context.fill(buttonX - 2, buttonY - 2, buttonX + buttonSize + 2, buttonY + buttonSize + 2, buttonBg);

        // Draw emoji icon
        context.drawCenteredTextWithShadow(textRenderer, "😀",
            buttonX + buttonSize / 2, buttonY + (buttonSize - 8) / 2, TEXT_COLOR);
    }

    private void updateAnimation() {
        if (!isAnimating) return;

        long currentTime = System.currentTimeMillis();
        long elapsed = currentTime - animationStartTime;

        if (elapsed >= ANIMATION_DURATION) {
            // Animation complete
            isAnimating = false;
            animationProgress = 1.0f;
            height = targetHeight;
        } else {
            // Calculate progress with easing
            float rawProgress = (float) elapsed / ANIMATION_DURATION;
            animationProgress = easeOutCubic(rawProgress);
            
            // Interpolate height
            int heightDifference = targetHeight - startHeight;
            height = startHeight + (int)(heightDifference * animationProgress);
        }
    }

    private float easeOutCubic(float t) {
        return 1 - (float) Math.pow(1 - t, 3);
    }

    private void startHeightAnimation(int newTargetHeight) {
        if (isAnimating && targetHeight == newTargetHeight) return;

        startHeight = height;
        targetHeight = newTargetHeight;
        isAnimating = true;
        animationStartTime = System.currentTimeMillis();
        animationProgress = 0.0f;
    }

    private void checkForLineCountChange() {
        // Calculate actual line count based on text wrapping
        List<String> wrappedLines = wrapTextToWidth(text, width - PADDING * 2);
        int currentLineCount = Math.max(1, wrappedLines.size());

        if (currentLineCount != previousLineCount) {
            // Line count changed, trigger animation
            int newHeight = Math.min(MIN_HEIGHT + (currentLineCount - 1) * LINE_HEIGHT + PADDING,
                                   MIN_HEIGHT + (MAX_LINES - 1) * LINE_HEIGHT + PADDING);
            startHeightAnimation(newHeight);
            previousLineCount = currentLineCount;

            Pokecobbleclaim.LOGGER.debug("Chat input line count changed to {} (wrapped), animating to height {}",
                currentLineCount, newHeight);
        }
    }

    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) {
            // Check emoji button click first
            if (showEmojiButton) {
                int buttonSize = 16;
                int buttonX = x + width - buttonSize - 4;
                int buttonY = y + height - buttonSize - 4;

                if (mouseX >= buttonX - 2 && mouseX <= buttonX + buttonSize + 2 &&
                    mouseY >= buttonY - 2 && mouseY <= buttonY + buttonSize + 2) {
                    emojiPicker.toggle();
                    return true;
                }
            }

            boolean wasInBounds = mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;
            
            if (wasInBounds) {
                setFocused(true);
                
                // Calculate cursor position from click
                int relativeX = (int)(mouseX - x - PADDING);
                int relativeY = (int)(mouseY - y - PADDING);
                
                // Find which line was clicked
                int clickedLine = Math.max(0, Math.min(relativeY / LINE_HEIGHT, MAX_LINES - 1));
                String[] lines = text.split("\n", -1);
                
                if (clickedLine < lines.length) {
                    // Find position within the line
                    String line = lines[clickedLine];
                    int positionInLine = 0;
                    
                    for (int i = 0; i <= line.length(); i++) {
                        String substring = line.substring(0, i);
                        if (textRenderer.getWidth(substring) > relativeX) {
                            positionInLine = Math.max(0, i - 1);
                            break;
                        }
                        positionInLine = i;
                    }
                    
                    // Calculate absolute cursor position
                    int absolutePosition = 0;
                    for (int i = 0; i < clickedLine; i++) {
                        absolutePosition += lines[i].length() + 1; // +1 for newline
                    }
                    absolutePosition += positionInLine;
                    
                    cursorPosition = Math.min(absolutePosition, text.length());
                }
                
                return true;
            } else {
                setFocused(false);
            }
        }
        return false;
    }

    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (!focused) return false;

        if (keyCode == 259) { // Backspace
            if (cursorPosition > 0) {
                text = text.substring(0, cursorPosition - 1) + text.substring(cursorPosition);
                cursorPosition--;
                checkForLineCountChange();
                if (changedListener != null) changedListener.accept(text);
            }
            return true;
        } else if (keyCode == 261) { // Delete key
            if (cursorPosition < text.length()) {
                text = text.substring(0, cursorPosition) + text.substring(cursorPosition + 1);
                checkForLineCountChange();
                if (changedListener != null) changedListener.accept(text);
            }
            return true;
        } else if (keyCode == 257) { // Enter key
            // Enter always sends the message if there's content
            if (submitListener != null && !text.trim().isEmpty()) {
                submitListener.accept(text.trim());
                setText(""); // Clear after submit
            }
            return true;
        } else if (keyCode == 258) { // Tab key
            // Tab adds a new line if we're not at max lines
            String newText = text.substring(0, cursorPosition) + "\n" + text.substring(cursorPosition);
            List<String> wrappedLines = wrapTextToWidth(newText, width - PADDING * 2);

            if (wrappedLines.size() <= MAX_LINES && newText.length() < maxLength) {
                text = newText;
                cursorPosition++;
                checkForLineCountChange();
                if (changedListener != null) changedListener.accept(text);
            }
            return true;
        } else if (keyCode == 262) { // Right arrow
            if (cursorPosition < text.length()) {
                cursorPosition++;
            }
            return true;
        } else if (keyCode == 263) { // Left arrow
            if (cursorPosition > 0) {
                cursorPosition--;
            }
            return true;
        }
        return false;
    }

    public boolean charTyped(char chr, int modifiers) {
        if (!focused) return false;

        if (text.length() < maxLength && chr >= 32) {
            // Insert character at cursor position
            String newText = text.substring(0, cursorPosition) + chr + text.substring(cursorPosition);

            // Check if the new text would cause automatic wrapping
            List<String> wrappedLines = wrapTextToWidth(newText, width - PADDING * 2);

            // Only allow input if it doesn't exceed max lines
            if (wrappedLines.size() <= MAX_LINES) {
                text = newText;
                cursorPosition++;
                checkForLineCountChange();
                if (changedListener != null) changedListener.accept(text);
                return true;
            }
        }
        return false;
    }

    // Getters and setters
    public String getText() { return text; }
    
    public void setText(String text) { 
        this.text = text != null ? text : "";
        this.cursorPosition = Math.min(this.cursorPosition, this.text.length());
        checkForLineCountChange();
    }
    
    public boolean isFocused() { return focused; }
    
    public void setFocused(boolean focused) { 
        this.focused = focused;
        if (focused) {
            lastCursorBlink = System.currentTimeMillis();
        }
    }
    
    public void setChangedListener(Consumer<String> listener) { this.changedListener = listener; }
    public void setSubmitListener(Consumer<String> listener) { this.submitListener = listener; }
    
    public void updatePosition(int newX, int newY, int newWidth) {
        this.x = newX;
        this.y = newY;
        this.width = newWidth;
    }
    
    public int getHeight() { return height; }
    public int getX() { return x; }
    public int getY() { return y; }
    public int getWidth() { return width; }

    /**
     * Inserts an emoji at the current cursor position.
     */
    private void insertEmojiAtCursor(String emoji) {
        if (emoji == null || emoji.isEmpty()) return;

        // Check if adding emoji would exceed length limit
        if (text.length() + emoji.length() <= maxLength) {
            // Insert emoji at cursor position
            text = text.substring(0, cursorPosition) + emoji + text.substring(cursorPosition);
            cursorPosition += emoji.length();

            // Check for line count change (in case emoji causes wrapping)
            checkForLineCountChange();

            // Notify listener
            if (changedListener != null) {
                changedListener.accept(text);
            }
        }
    }

    /**
     * Gets the emoji picker for external rendering.
     */
    public EmojiPicker getEmojiPicker() {
        return emojiPicker;
    }

    /**
     * Handles emoji picker mouse clicks. Should be called before other mouse handling.
     * Returns true if the click was handled by the emoji picker.
     */
    public boolean handleEmojiPickerClick(double mouseX, double mouseY, int screenWidth, int screenHeight) {
        if (emojiPicker.isOpen()) {
            return emojiPicker.mouseClicked(mouseX, mouseY, screenWidth, screenHeight);
        }
        return false;
    }

    /**
     * Renders the emoji picker if it's open. Should be called after all other rendering.
     */
    public void renderEmojiPicker(DrawContext context, int screenWidth, int screenHeight, int mouseX, int mouseY) {
        if (emojiPicker.isOpen()) {
            emojiPicker.render(context, screenWidth, screenHeight, mouseX, mouseY);
        }
    }

    /**
     * Wraps text to fit within the specified width, automatically breaking lines when needed.
     */
    private List<String> wrapTextToWidth(String text, int maxWidth) {
        List<String> lines = new ArrayList<>();
        if (text.isEmpty()) {
            lines.add("");
            return lines;
        }

        // Split by existing newlines first
        String[] existingLines = text.split("\n", -1);

        for (String line : existingLines) {
            if (line.isEmpty()) {
                lines.add("");
                continue;
            }

            // Wrap each line to fit the width
            String[] words = line.split(" ");
            StringBuilder currentLine = new StringBuilder();

            for (String word : words) {
                String testLine = currentLine.length() == 0 ? word : currentLine + " " + word;

                if (textRenderer.getWidth(testLine) <= maxWidth) {
                    currentLine = new StringBuilder(testLine);
                } else {
                    // Current line is full, start a new line
                    if (currentLine.length() > 0) {
                        lines.add(currentLine.toString());
                        currentLine = new StringBuilder(word);
                    } else {
                        // Single word is too long, break it character by character
                        StringBuilder wordBuilder = new StringBuilder();
                        for (char c : word.toCharArray()) {
                            String testChar = wordBuilder + String.valueOf(c);
                            if (textRenderer.getWidth(testChar) <= maxWidth) {
                                wordBuilder.append(c);
                            } else {
                                if (wordBuilder.length() > 0) {
                                    lines.add(wordBuilder.toString());
                                    wordBuilder = new StringBuilder(String.valueOf(c));
                                } else {
                                    // Even single character doesn't fit, add it anyway
                                    lines.add(String.valueOf(c));
                                }
                            }
                        }
                        if (wordBuilder.length() > 0) {
                            currentLine = wordBuilder;
                        }
                    }
                }
            }

            // Add remaining text in current line
            if (currentLine.length() > 0) {
                lines.add(currentLine.toString());
            }
        }

        return lines.isEmpty() ? List.of("") : lines;
    }
}
