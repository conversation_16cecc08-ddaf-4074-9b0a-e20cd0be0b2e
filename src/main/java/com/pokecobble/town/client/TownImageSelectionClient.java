package com.pokecobble.town.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.image.TownImageSelectionManager;
import com.pokecobble.town.network.NetworkConstants;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.minecraft.network.PacketByteBuf;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * Client-side manager for town image selection.
 * Handles caching and network communication for the new image selection system.
 */
public class TownImageSelectionClient {
    
    // Client-side cache of town image selection data
    private static final Map<UUID, TownImageSelectionManager.TownImageSlotData> clientCache = new ConcurrentHashMap<>();
    
    /**
     * Registers client-side network handlers.
     */
    public static void registerClientHandlers() {
        // Handle selection data sync from server
        ClientPlayNetworking.registerGlobalReceiver(NetworkConstants.TOWN_IMAGE_SELECTION_SYNC, 
            TownImageSelectionClient::handleSelectionSync);
    }
    
    /**
     * Handles selection data sync from server.
     */
    private static void handleSelectionSync(net.minecraft.client.MinecraftClient client,
                                          net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                          PacketByteBuf buf,
                                          net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID townId = buf.readUuid();
            int selectedSlot = buf.readInt();
            
            TownImageSelectionManager.TownImageSlotData data = new TownImageSelectionManager.TownImageSlotData();
            data.selectedSlot = selectedSlot;
            
            // Read slot data
            for (int i = 0; i < 3; i++) {
                boolean hasImage = buf.readBoolean();
                if (hasImage) {
                    String imageName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    data.setSlotImage(i, imageName);
                    
                    TownImageSelectionManager.TownImageSlotData.ImageSlotSettings settings = data.getSlotSettings(i);
                    settings.scale = buf.readFloat();
                    settings.offsetX = buf.readInt();
                    settings.offsetY = buf.readInt();
                }
            }
            
            // Update client cache
            clientCache.put(townId, data);

            // Clear TownImageUtil settings cache to force reload with new settings
            for (int i = 0; i < 3; i++) {
                String imageName = data.getSlotImage(i);
                if (imageName != null) {
                    com.pokecobble.town.util.TownImageUtil.clearSettingsCache(townId, imageName);
                }
            }

            Pokecobbleclaim.LOGGER.debug("Received image selection sync for town " + townId + ", selected slot: " + selectedSlot);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling selection sync: " + e.getMessage());
        }
    }
    
    /**
     * Gets cached selection data for a town.
     */
    public static TownImageSelectionManager.TownImageSlotData getCachedSelectionData(UUID townId) {
        return clientCache.get(townId);
    }
    
    /**
     * Requests selection data from server.
     */
    public static void requestSelectionData(UUID townId) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(townId);
            
            ClientPlayNetworking.send(NetworkConstants.TOWN_IMAGE_SELECTION_REQUEST, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting selection data: " + e.getMessage());
        }
    }
    
    /**
     * Sends slot update to server.
     */
    public static void sendSlotUpdate(UUID townId, int slot, String imageName, float scale, int offsetX, int offsetY) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(townId);
            buf.writeInt(slot);
            buf.writeString(imageName, NetworkConstants.MAX_STRING_LENGTH);
            buf.writeFloat(scale);
            buf.writeInt(offsetX);
            buf.writeInt(offsetY);
            
            ClientPlayNetworking.send(NetworkConstants.TOWN_IMAGE_SLOT_UPDATE, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending slot update: " + e.getMessage());
        }
    }
    
    /**
     * Sends slot selection to server.
     */
    public static void sendSlotSelection(UUID townId, int slot) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(townId);
            buf.writeInt(slot);
            
            ClientPlayNetworking.send(NetworkConstants.TOWN_IMAGE_SLOT_SELECT, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending slot selection: " + e.getMessage());
        }
    }
    
    /**
     * Gets the currently selected image name for a town.
     */
    public static String getSelectedImageName(UUID townId) {
        TownImageSelectionManager.TownImageSlotData data = getCachedSelectionData(townId);
        if (data != null) {
            return data.getSelectedImageName();
        }
        return null;
    }
    
    /**
     * Gets the settings for the currently selected image.
     */
    public static TownImageSelectionManager.TownImageSlotData.ImageSlotSettings getSelectedImageSettings(UUID townId) {
        TownImageSelectionManager.TownImageSlotData data = getCachedSelectionData(townId);
        if (data != null) {
            return data.getSelectedSlotSettings();
        }
        return new TownImageSelectionManager.TownImageSlotData.ImageSlotSettings();
    }
    
    /**
     * Gets image name for a specific slot.
     */
    public static String getSlotImageName(UUID townId, int slot) {
        TownImageSelectionManager.TownImageSlotData data = getCachedSelectionData(townId);
        if (data != null) {
            return data.getSlotImage(slot);
        }
        return null;
    }
    
    /**
     * Gets settings for a specific slot.
     */
    public static TownImageSelectionManager.TownImageSlotData.ImageSlotSettings getSlotSettings(UUID townId, int slot) {
        TownImageSelectionManager.TownImageSlotData data = getCachedSelectionData(townId);
        if (data != null) {
            return data.getSlotSettings(slot);
        }
        return new TownImageSelectionManager.TownImageSlotData.ImageSlotSettings();
    }
    
    /**
     * Gets the currently selected slot for a town.
     */
    public static int getSelectedSlot(UUID townId) {
        TownImageSelectionManager.TownImageSlotData data = getCachedSelectionData(townId);
        if (data != null) {
            return data.selectedSlot;
        }
        return -1;
    }
    
    /**
     * Clears cache for a specific town.
     */
    public static void clearCache(UUID townId) {
        clientCache.remove(townId);
    }
    
    /**
     * Clears all cached data.
     */
    public static void clearAllCache() {
        clientCache.clear();
    }
    
    /**
     * Checks if we have cached data for a town.
     */
    public static boolean hasCachedData(UUID townId) {
        return clientCache.containsKey(townId);
    }
}
