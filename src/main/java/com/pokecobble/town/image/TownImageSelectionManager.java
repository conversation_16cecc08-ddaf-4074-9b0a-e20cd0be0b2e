package com.pokecobble.town.image;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.loader.api.FabricLoader;

import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * Dedicated manager for town image selection data.
 * This system is completely independent from TownSettingsManager to avoid interference.
 */
public class TownImageSelectionManager {
    private static final String TOWN_IMAGES_PATH = "config/pokecobbleclaim/towns";
    private static final String SELECTION_FILE_NAME = "image_selection.json";
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    
    // Cache for loaded selection data
    private static final Map<UUID, TownImageSlotData> selectionCache = new ConcurrentHashMap<>();
    
    /**
     * Data structure representing the 3-slot image selection for a town.
     */
    public static class TownImageSlotData {
        public String slot0Image = null;  // First slot
        public String slot1Image = null;  // Second slot  
        public String slot2Image = null;  // Third slot
        public int selectedSlot = -1;     // Which slot is currently selected (-1 = none)
        
        // Image settings for each slot
        public ImageSlotSettings slot0Settings = new ImageSlotSettings();
        public ImageSlotSettings slot1Settings = new ImageSlotSettings();
        public ImageSlotSettings slot2Settings = new ImageSlotSettings();
        
        public static class ImageSlotSettings {
            public float scale = 1.0f;
            public int offsetX = 0;
            public int offsetY = 0;
        }
        
        /**
         * Gets the currently selected image name, or null if no slot is selected.
         */
        public String getSelectedImageName() {
            switch (selectedSlot) {
                case 0: return slot0Image;
                case 1: return slot1Image;
                case 2: return slot2Image;
                default: return null;
            }
        }
        
        /**
         * Gets the settings for the currently selected slot.
         */
        public ImageSlotSettings getSelectedSlotSettings() {
            switch (selectedSlot) {
                case 0: return slot0Settings;
                case 1: return slot1Settings;
                case 2: return slot2Settings;
                default: return new ImageSlotSettings();
            }
        }
        
        /**
         * Sets an image for a specific slot.
         */
        public void setSlotImage(int slot, String imageName) {
            switch (slot) {
                case 0: slot0Image = imageName; break;
                case 1: slot1Image = imageName; break;
                case 2: slot2Image = imageName; break;
            }
        }
        
        /**
         * Gets the image name for a specific slot.
         */
        public String getSlotImage(int slot) {
            switch (slot) {
                case 0: return slot0Image;
                case 1: return slot1Image;
                case 2: return slot2Image;
                default: return null;
            }
        }
        
        /**
         * Gets the settings for a specific slot.
         */
        public ImageSlotSettings getSlotSettings(int slot) {
            switch (slot) {
                case 0: return slot0Settings;
                case 1: return slot1Settings;
                case 2: return slot2Settings;
                default: return new ImageSlotSettings();
            }
        }
        
        /**
         * Removes an image from all slots that contain it.
         */
        public void removeImageFromAllSlots(String imageName) {
            if (imageName == null) return;
            
            if (imageName.equals(slot0Image)) {
                slot0Image = null;
                if (selectedSlot == 0) selectedSlot = -1;
            }
            if (imageName.equals(slot1Image)) {
                slot1Image = null;
                if (selectedSlot == 1) selectedSlot = -1;
            }
            if (imageName.equals(slot2Image)) {
                slot2Image = null;
                if (selectedSlot == 2) selectedSlot = -1;
            }
        }
    }
    
    /**
     * Ensures the town directory exists and returns the path.
     */
    private static Path ensureTownDirectory(UUID townId) {
        Path townDir = Paths.get(FabricLoader.getInstance().getGameDir().toString(), 
                                 TOWN_IMAGES_PATH, townId.toString());
        try {
            if (!Files.exists(townDir)) {
                Files.createDirectories(townDir);
                Pokecobbleclaim.LOGGER.info("Created town directory: " + townDir);
            }
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to create town directory: " + e.getMessage());
        }
        return townDir;
    }
    
    /**
     * Gets the path to the image selection file for a town.
     */
    private static Path getSelectionFilePath(UUID townId) {
        return ensureTownDirectory(townId).resolve(SELECTION_FILE_NAME);
    }
    
    /**
     * Loads image selection data for a town.
     */
    public static TownImageSlotData loadTownImageSelection(UUID townId) {
        if (townId == null) return new TownImageSlotData();
        
        // Check cache first
        TownImageSlotData cached = selectionCache.get(townId);
        if (cached != null) {
            return cached;
        }
        
        // Load from file
        Path selectionFile = getSelectionFilePath(townId);
        TownImageSlotData data = new TownImageSlotData();
        
        if (Files.exists(selectionFile)) {
            try (FileReader reader = new FileReader(selectionFile.toFile())) {
                data = GSON.fromJson(reader, TownImageSlotData.class);
                if (data == null) {
                    data = new TownImageSlotData();
                }
                // Ensure settings objects exist
                if (data.slot0Settings == null) data.slot0Settings = new TownImageSlotData.ImageSlotSettings();
                if (data.slot1Settings == null) data.slot1Settings = new TownImageSlotData.ImageSlotSettings();
                if (data.slot2Settings == null) data.slot2Settings = new TownImageSlotData.ImageSlotSettings();
                
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Failed to load image selection for town " + townId + ": " + e.getMessage());
                data = new TownImageSlotData();
            }
        }
        
        // Cache the data
        selectionCache.put(townId, data);
        return data;
    }

    /**
     * Saves image selection data for a town.
     */
    public static void saveTownImageSelection(UUID townId, TownImageSlotData data) {
        if (townId == null || data == null) return;

        Path selectionFile = getSelectionFilePath(townId);

        try (FileWriter writer = new FileWriter(selectionFile.toFile())) {
            GSON.toJson(data, writer);

            // Update cache
            selectionCache.put(townId, data);

            Pokecobbleclaim.LOGGER.debug("Saved image selection for town " + townId);
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to save image selection for town " + townId + ": " + e.getMessage());
        }
    }

    /**
     * Sets an image for a specific slot and saves immediately.
     */
    public static void setSlotImage(UUID townId, int slot, String imageName) {
        TownImageSlotData data = loadTownImageSelection(townId);
        data.setSlotImage(slot, imageName);
        saveTownImageSelection(townId, data);
    }

    /**
     * Selects a specific slot as the active town image.
     */
    public static void selectSlot(UUID townId, int slot) {
        TownImageSlotData data = loadTownImageSelection(townId);
        data.selectedSlot = slot;
        saveTownImageSelection(townId, data);
    }

    /**
     * Updates image settings for a specific slot.
     */
    public static void updateSlotSettings(UUID townId, int slot, float scale, int offsetX, int offsetY) {
        TownImageSlotData data = loadTownImageSelection(townId);
        TownImageSlotData.ImageSlotSettings settings = data.getSlotSettings(slot);
        settings.scale = scale;
        settings.offsetX = offsetX;
        settings.offsetY = offsetY;
        saveTownImageSelection(townId, data);
    }

    /**
     * Removes an image from all slots that contain it.
     */
    public static void removeImageFromAllSlots(UUID townId, String imageName) {
        TownImageSlotData data = loadTownImageSelection(townId);
        data.removeImageFromAllSlots(imageName);
        saveTownImageSelection(townId, data);
    }

    /**
     * Gets the currently selected image name for a town.
     */
    public static String getSelectedImageName(UUID townId) {
        TownImageSlotData data = loadTownImageSelection(townId);
        return data.getSelectedImageName();
    }

    /**
     * Gets the settings for the currently selected image.
     */
    public static TownImageSlotData.ImageSlotSettings getSelectedImageSettings(UUID townId) {
        TownImageSlotData data = loadTownImageSelection(townId);
        return data.getSelectedSlotSettings();
    }

    /**
     * Clears the cache for a specific town.
     */
    public static void clearCache(UUID townId) {
        selectionCache.remove(townId);
    }

    /**
     * Clears all cached data.
     */
    public static void clearAllCache() {
        selectionCache.clear();
    }

    /**
     * Creates town directory during town creation.
     * This should be called when a new town is created.
     */
    public static void initializeTownDirectory(UUID townId) {
        ensureTownDirectory(townId);

        // Create empty selection data
        TownImageSlotData data = new TownImageSlotData();
        saveTownImageSelection(townId, data);

        Pokecobbleclaim.LOGGER.info("Initialized image directory for new town: " + townId);
    }

    /**
     * Deletes all image selection data for a town.
     * This should be called when a town is deleted.
     */
    public static void deleteTownImageData(UUID townId) {
        try {
            Path townDir = Paths.get(FabricLoader.getInstance().getGameDir().toString(),
                                   TOWN_IMAGES_PATH, townId.toString());

            if (Files.exists(townDir)) {
                // Delete selection file
                Path selectionFile = townDir.resolve(SELECTION_FILE_NAME);
                if (Files.exists(selectionFile)) {
                    Files.delete(selectionFile);
                }

                // Delete entire town directory if empty
                try {
                    Files.delete(townDir);
                } catch (Exception e) {
                    // Directory not empty, that's fine
                }
            }

            // Clear from cache
            clearCache(townId);

            Pokecobbleclaim.LOGGER.info("Deleted image data for town: " + townId);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to delete image data for town " + townId + ": " + e.getMessage());
        }
    }
}
