package com.pokecobble.town.chat;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * Manages town chat messages with temporary storage that gets cleared on server restart.
 * This is intentionally not persistent - messages are lost when the server restarts.
 */
public class TownChatManager {
    private static TownChatManager instance;
    
    // Temporary storage - cleared on server restart
    private final Map<UUID, List<TownChatMessage>> townChatHistory = new ConcurrentHashMap<>();
    private final Map<UUID, Set<UUID>> activeChatViewers = new ConcurrentHashMap<>();
    
    // Configuration
    private static final int MAX_MESSAGES_PER_TOWN = 100; // Limit messages to prevent memory issues
    private static final int MAX_MESSAGE_LENGTH = 256;
    
    private TownChatManager() {
        Pokecobbleclaim.LOGGER.info("TownChatManager initialized - messages will be cleared on server restart");
    }
    
    public static TownChatManager getInstance() {
        if (instance == null) {
            instance = new TownChatManager();
        }
        return instance;
    }
    
    /**
     * Adds a message to a town's chat history.
     */
    public TownChatMessage addMessage(UUID townId, UUID playerId, String playerName, String message) {
        if (message == null || message.trim().isEmpty()) {
            return null;
        }
        
        // Trim message if too long
        String trimmedMessage = message.length() > MAX_MESSAGE_LENGTH ? 
            message.substring(0, MAX_MESSAGE_LENGTH - 3) + "..." : message;
        
        TownChatMessage chatMessage = new TownChatMessage(townId, playerId, playerName, 
            trimmedMessage, TownChatMessage.MessageType.PLAYER_MESSAGE);
        
        // Add to history
        List<TownChatMessage> history = townChatHistory.computeIfAbsent(townId, k -> new CopyOnWriteArrayList<>());
        history.add(chatMessage);
        
        // Limit history size
        if (history.size() > MAX_MESSAGES_PER_TOWN) {
            history.remove(0); // Remove oldest message
        }
        
        Pokecobbleclaim.LOGGER.debug("Added chat message to town {}: {} - {}", townId, playerName, trimmedMessage);
        return chatMessage;
    }
    
    /**
     * Adds a system message to a town's chat history.
     */
    public TownChatMessage addSystemMessage(UUID townId, String message) {
        if (message == null || message.trim().isEmpty()) {
            return null;
        }
        
        TownChatMessage chatMessage = new TownChatMessage(townId, message, TownChatMessage.MessageType.SYSTEM_MESSAGE);
        
        // Add to history
        List<TownChatMessage> history = townChatHistory.computeIfAbsent(townId, k -> new CopyOnWriteArrayList<>());
        history.add(chatMessage);
        
        // Limit history size
        if (history.size() > MAX_MESSAGES_PER_TOWN) {
            history.remove(0); // Remove oldest message
        }
        
        Pokecobbleclaim.LOGGER.debug("Added system message to town {}: {}", townId, message);
        return chatMessage;
    }
    
    /**
     * Gets the chat history for a town.
     */
    public List<TownChatMessage> getChatHistory(UUID townId) {
        List<TownChatMessage> history = townChatHistory.get(townId);
        return history != null ? new ArrayList<>(history) : new ArrayList<>();
    }
    
    /**
     * Gets recent messages for a town (last N messages).
     */
    public List<TownChatMessage> getRecentMessages(UUID townId, int count) {
        List<TownChatMessage> history = getChatHistory(townId);
        if (history.size() <= count) {
            return history;
        }
        return history.subList(history.size() - count, history.size());
    }
    
    /**
     * Registers a player as actively viewing town chat.
     */
    public void registerChatViewer(UUID townId, UUID playerId) {
        activeChatViewers.computeIfAbsent(townId, k -> ConcurrentHashMap.newKeySet()).add(playerId);
        Pokecobbleclaim.LOGGER.debug("Player {} is now viewing town {} chat", playerId, townId);
    }
    
    /**
     * Unregisters a player from viewing town chat.
     */
    public void unregisterChatViewer(UUID townId, UUID playerId) {
        Set<UUID> viewers = activeChatViewers.get(townId);
        if (viewers != null) {
            viewers.remove(playerId);
            if (viewers.isEmpty()) {
                activeChatViewers.remove(townId);
            }
        }
        Pokecobbleclaim.LOGGER.debug("Player {} stopped viewing town {} chat", playerId, townId);
    }
    
    /**
     * Gets all players currently viewing a town's chat.
     */
    public Set<UUID> getActiveChatViewers(UUID townId) {
        Set<UUID> viewers = activeChatViewers.get(townId);
        return viewers != null ? new HashSet<>(viewers) : new HashSet<>();
    }
    
    /**
     * Gets all online players in a town for message broadcasting.
     */
    public List<ServerPlayerEntity> getOnlineTownMembers(MinecraftServer server, UUID townId) {
        Town town = TownManager.getInstance().getTownById(townId);
        if (town == null) {
            return new ArrayList<>();
        }
        
        return server.getPlayerManager().getPlayerList().stream()
            .filter(player -> town.getPlayerIds().contains(player.getUuid()))
            .collect(Collectors.toList());
    }
    
    /**
     * Clears all chat data (called on server restart).
     */
    public void clearAllChatData() {
        townChatHistory.clear();
        activeChatViewers.clear();
        Pokecobbleclaim.LOGGER.info("Cleared all town chat data (server restart)");
    }
    
    /**
     * Clears chat data for a specific town (when town is deleted).
     */
    public void clearTownChatData(UUID townId) {
        townChatHistory.remove(townId);
        activeChatViewers.remove(townId);
        Pokecobbleclaim.LOGGER.debug("Cleared chat data for town {}", townId);
    }
    
    /**
     * Gets statistics about current chat usage.
     */
    public Map<String, Object> getChatStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("total_towns_with_chat", townChatHistory.size());
        stats.put("total_messages", townChatHistory.values().stream().mapToInt(List::size).sum());
        stats.put("active_viewers", activeChatViewers.values().stream().mapToInt(Set::size).sum());
        return stats;
    }
}
