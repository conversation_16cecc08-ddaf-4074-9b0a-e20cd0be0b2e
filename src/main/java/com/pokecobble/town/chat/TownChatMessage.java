package com.pokecobble.town.chat;

import java.time.Instant;
import java.util.UUID;

/**
 * Represents a chat message in a town chat.
 * This is a temporary data structure that gets cleared on server restart.
 */
public class TownChatMessage {
    private final UUID messageId;
    private final UUID townId;
    private final UUID playerId;
    private final String playerName;
    private final String message;
    private final Instant timestamp;
    private final MessageType type;

    public enum MessageType {
        PLAYER_MESSAGE,    // Regular player message
        SYSTEM_MESSAGE,    // System notifications (player joined/left, etc.)
        ANNOUNCEMENT       // Town announcements from leaders
    }

    public TownChatMessage(UUID townId, UUID playerId, String playerName, String message, MessageType type) {
        this.messageId = UUID.randomUUID();
        this.townId = townId;
        this.playerId = playerId;
        this.playerName = playerName;
        this.message = message;
        this.timestamp = Instant.now();
        this.type = type;
    }

    // Constructor for system messages (no player)
    public TownChatMessage(UUID townId, String message, MessageType type) {
        this.messageId = UUID.randomUUID();
        this.townId = townId;
        this.playerId = null;
        this.playerName = "System";
        this.message = message;
        this.timestamp = Instant.now();
        this.type = type;
    }

    // Getters
    public UUID getMessageId() {
        return messageId;
    }

    public UUID getTownId() {
        return townId;
    }

    public UUID getPlayerId() {
        return playerId;
    }

    public String getPlayerName() {
        return playerName;
    }

    public String getMessage() {
        return message;
    }

    public Instant getTimestamp() {
        return timestamp;
    }

    public MessageType getType() {
        return type;
    }

    public boolean isSystemMessage() {
        return type == MessageType.SYSTEM_MESSAGE || type == MessageType.ANNOUNCEMENT;
    }

    public boolean isPlayerMessage() {
        return type == MessageType.PLAYER_MESSAGE;
    }

    /**
     * Gets a formatted timestamp for display (e.g., "14:30")
     */
    public String getFormattedTime() {
        return String.format("%02d:%02d", 
            timestamp.atZone(java.time.ZoneId.systemDefault()).getHour(),
            timestamp.atZone(java.time.ZoneId.systemDefault()).getMinute());
    }

    /**
     * Gets the display name with appropriate formatting based on message type
     */
    public String getDisplayName() {
        switch (type) {
            case SYSTEM_MESSAGE:
                return "System";
            case ANNOUNCEMENT:
                return playerName + " (Announcement)";
            case PLAYER_MESSAGE:
            default:
                return playerName;
        }
    }

    @Override
    public String toString() {
        return String.format("[%s] %s: %s", getFormattedTime(), getDisplayName(), message);
    }
}
