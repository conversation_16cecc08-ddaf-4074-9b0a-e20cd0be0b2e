package com.pokecobble.economy.data;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.EconomyConfig;
import com.pokecobble.economy.core.PlayerEconomyData;
import com.pokecobble.economy.security.EncryptionManager;
import com.pokecobble.economy.security.IntegrityChecker;

import java.io.*;
import java.nio.file.*;
import java.security.MessageDigest;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Handles secure file-based storage for economy data.
 * Implements encryption, atomic operations, and backup mechanisms.
 */
public class EconomyDataStorage {
    
    private static final Gson GSON = new GsonBuilder()
            .setPrettyPrinting()
            .disableHtmlEscaping()
            .create();
    
    private final ExecutorService ioExecutor;
    private final ReadWriteLock storageLock;
    private final EncryptionManager encryptionManager;
    private final IntegrityChecker integrityChecker;
    private final BackupManager backupManager;
    
    private Path economyDataPath;
    private Path playerDataPath;
    private Path backupPath;
    private Path tempPath;
    
    private boolean initialized;
    
    /**
     * Creates a new economy data storage instance.
     */
    public EconomyDataStorage() {
        this.ioExecutor = Executors.newFixedThreadPool(EconomyConfig.IO_THREAD_POOL_SIZE);
        this.storageLock = new ReentrantReadWriteLock();
        this.encryptionManager = new EncryptionManager();
        this.integrityChecker = new IntegrityChecker();
        this.backupManager = new BackupManager();
        this.initialized = false;
    }
    
    /**
     * Initializes the data storage system.
     */
    public void initialize() {
        if (initialized) {
            return;
        }
        
        try {
            // Setup directory structure
            setupDirectories();
            
            // Initialize encryption
            if (EconomyConfig.ENABLE_ENCRYPTION) {
                encryptionManager.initialize();
            }
            
            // Initialize integrity checker
            integrityChecker.initialize();
            
            // Initialize backup manager
            if (EconomyConfig.AUTO_BACKUP_ENABLED) {
                backupManager.initialize(backupPath);
            }
            
            this.initialized = true;
            Pokecobbleclaim.LOGGER.info("Economy data storage initialized successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize economy data storage", e);
            throw new RuntimeException("Economy data storage initialization failed", e);
        }
    }
    
    /**
     * Shuts down the data storage system.
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        try {
            // Shutdown backup manager
            backupManager.shutdown();
            
            // Shutdown IO executor
            ioExecutor.shutdown();
            
            this.initialized = false;
            Pokecobbleclaim.LOGGER.info("Economy data storage shut down successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during economy data storage shutdown", e);
        }
    }
    
    /**
     * Saves player economy data to disk.
     * 
     * @param playerData The player data to save
     */
    public void savePlayerData(PlayerEconomyData playerData) {
        if (!initialized) {
            throw new IllegalStateException("Data storage not initialized");
        }
        
        CompletableFuture.runAsync(() -> {
            savePlayerDataSync(playerData);
        }, ioExecutor);
    }
    
    /**
     * Saves player economy data synchronously.
     * 
     * @param playerData The player data to save
     */
    public void savePlayerDataSync(PlayerEconomyData playerData) {
        if (!initialized) {
            throw new IllegalStateException("Data storage not initialized");
        }
        
        storageLock.writeLock().lock();
        try {
            UUID playerId = playerData.getPlayerId();
            String fileName = playerId.toString() + EconomyConfig.PLAYER_DATA_EXTENSION;
            Path playerFile = playerDataPath.resolve(fileName);
            Path tempFile = tempPath.resolve(fileName + ".tmp");
            
            // Calculate checksum before saving
            if (EconomyConfig.ENABLE_DATA_CHECKSUMS) {
                String checksum = integrityChecker.calculateChecksum(playerData);
                playerData.setChecksum(checksum);
            }
            
            // Serialize to JSON
            String jsonData = GSON.toJson(playerData);
            
            // Encrypt if enabled
            byte[] dataToWrite;
            if (EconomyConfig.ENABLE_ENCRYPTION) {
                dataToWrite = encryptionManager.encrypt(jsonData.getBytes("UTF-8"));
            } else {
                dataToWrite = jsonData.getBytes("UTF-8");
            }
            
            // Write to temporary file first (atomic operation)
            Files.write(tempFile, dataToWrite, StandardOpenOption.CREATE, StandardOpenOption.WRITE);
            
            // Verify the written data
            if (EconomyConfig.VERIFY_ON_SAVE) {
                PlayerEconomyData verifyData = loadPlayerDataFromFile(tempFile);
                if (verifyData == null || !verifyData.getPlayerId().equals(playerId)) {
                    throw new IOException("Data verification failed after save");
                }
            }
            
            // Atomic move to final location
            Files.move(tempFile, playerFile, StandardCopyOption.REPLACE_EXISTING, StandardCopyOption.ATOMIC_MOVE);
            
            // Create backup if enabled
            if (EconomyConfig.AUTO_BACKUP_ENABLED) {
                backupManager.createBackup(playerFile, playerId);
            }
            
            Pokecobbleclaim.LOGGER.debug("Saved economy data for player " + playerId);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to save player economy data for " + playerData.getPlayerId(), e);
            throw new RuntimeException("Failed to save player economy data", e);
        } finally {
            storageLock.writeLock().unlock();
        }
    }
    
    /**
     * Loads player economy data from disk.
     * 
     * @param playerId The player's UUID
     * @return The player's economy data, or null if not found
     */
    public PlayerEconomyData loadPlayerData(UUID playerId) {
        if (!initialized) {
            throw new IllegalStateException("Data storage not initialized");
        }
        
        storageLock.readLock().lock();
        try {
            String fileName = playerId.toString() + EconomyConfig.PLAYER_DATA_EXTENSION;
            Path playerFile = playerDataPath.resolve(fileName);
            
            if (!Files.exists(playerFile)) {
                return null;
            }
            
            PlayerEconomyData data = loadPlayerDataFromFile(playerFile);
            
            // If loading failed, try to restore from backup
            if (data == null && EconomyConfig.AUTO_BACKUP_ENABLED) {
                Pokecobbleclaim.LOGGER.warn("Failed to load player data, attempting backup restore for " + playerId);
                data = backupManager.restoreFromBackup(playerId);
            }
            
            return data;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load player economy data for " + playerId, e);
            
            // Try to restore from backup
            if (EconomyConfig.AUTO_BACKUP_ENABLED) {
                try {
                    return backupManager.restoreFromBackup(playerId);
                } catch (Exception backupError) {
                    Pokecobbleclaim.LOGGER.error("Failed to restore from backup for " + playerId, backupError);
                }
            }
            
            return null;
        } finally {
            storageLock.readLock().unlock();
        }
    }
    
    /**
     * Loads player data from a specific file.
     * 
     * @param file The file to load from
     * @return The player data, or null if loading failed
     */
    private PlayerEconomyData loadPlayerDataFromFile(Path file) {
        try {
            // Read file data
            byte[] fileData = Files.readAllBytes(file);
            
            // Decrypt if necessary
            String jsonData;
            if (EconomyConfig.ENABLE_ENCRYPTION) {
                byte[] decryptedData = encryptionManager.decrypt(fileData);
                jsonData = new String(decryptedData, "UTF-8");
            } else {
                jsonData = new String(fileData, "UTF-8");
            }
            
            // Deserialize from JSON
            PlayerEconomyData data = GSON.fromJson(jsonData, PlayerEconomyData.class);
            
            // Verify integrity if enabled
            if (EconomyConfig.VERIFY_ON_LOAD && EconomyConfig.ENABLE_DATA_CHECKSUMS) {
                if (!integrityChecker.verifyChecksum(data)) {
                    Pokecobbleclaim.LOGGER.error("Integrity check failed for player data: " + data.getPlayerId());
                    return null;
                }
            }
            
            return data;
            
        } catch (JsonSyntaxException e) {
            Pokecobbleclaim.LOGGER.error("Corrupted JSON data in file: " + file, e);
            return null;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error loading player data from file: " + file, e);
            return null;
        }
    }
    
    /**
     * Deletes player economy data.
     * 
     * @param playerId The player's UUID
     * @return true if successful, false otherwise
     */
    public boolean deletePlayerData(UUID playerId) {
        if (!initialized) {
            throw new IllegalStateException("Data storage not initialized");
        }
        
        storageLock.writeLock().lock();
        try {
            String fileName = playerId.toString() + EconomyConfig.PLAYER_DATA_EXTENSION;
            Path playerFile = playerDataPath.resolve(fileName);
            
            if (Files.exists(playerFile)) {
                // Create backup before deletion
                if (EconomyConfig.AUTO_BACKUP_ENABLED) {
                    backupManager.createBackup(playerFile, playerId);
                }
                
                Files.delete(playerFile);
                Pokecobbleclaim.LOGGER.info("Deleted economy data for player " + playerId);
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to delete player economy data for " + playerId, e);
            return false;
        } finally {
            storageLock.writeLock().unlock();
        }
    }
    
    /**
     * Checks if player data exists.
     * 
     * @param playerId The player's UUID
     * @return true if data exists, false otherwise
     */
    public boolean playerDataExists(UUID playerId) {
        if (!initialized) {
            return false;
        }
        
        String fileName = playerId.toString() + EconomyConfig.PLAYER_DATA_EXTENSION;
        Path playerFile = playerDataPath.resolve(fileName);
        return Files.exists(playerFile);
    }
    
    /**
     * Sets up the directory structure for economy data.
     */
    private void setupDirectories() throws IOException {
        // Get the server's world directory
        Path worldPath = Paths.get(".");
        
        // Create economy data directories
        economyDataPath = worldPath.resolve(EconomyConfig.ECONOMY_DATA_FOLDER);
        playerDataPath = economyDataPath.resolve(EconomyConfig.PLAYER_DATA_FOLDER);
        backupPath = economyDataPath.resolve(EconomyConfig.BACKUP_FOLDER);
        tempPath = economyDataPath.resolve(EconomyConfig.TEMP_FOLDER);
        
        // Create directories if they don't exist
        Files.createDirectories(economyDataPath);
        Files.createDirectories(playerDataPath);
        Files.createDirectories(backupPath);
        Files.createDirectories(tempPath);
        
        Pokecobbleclaim.LOGGER.info("Economy data directories created at: " + economyDataPath.toAbsolutePath());
    }
    
    /**
     * Gets the player data directory path.
     * 
     * @return The player data directory path
     */
    public Path getPlayerDataPath() {
        return playerDataPath;
    }
    
    /**
     * Gets the backup directory path.
     * 
     * @return The backup directory path
     */
    public Path getBackupPath() {
        return backupPath;
    }
    
    /**
     * Gets the temporary directory path.
     * 
     * @return The temporary directory path
     */
    public Path getTempPath() {
        return tempPath;
    }
    
    /**
     * Gets the backup manager instance.
     * 
     * @return The backup manager
     */
    public BackupManager getBackupManager() {
        return backupManager;
    }
}
