package com.pokecobble.economy.data;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.EconomyConfig;
import com.pokecobble.economy.core.PlayerEconomyData;

import java.io.IOException;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * Manages backup operations for economy data.
 * Provides automatic backups, manual backups, and data recovery functionality.
 */
public class BackupManager {
    
    private static final Gson GSON = new GsonBuilder()
            .setPrettyPrinting()
            .disableHtmlEscaping()
            .create();
    
    private static final DateTimeFormatter BACKUP_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
    
    private Path backupPath;
    private ScheduledExecutorService backupScheduler;
    private boolean initialized;
    
    /**
     * Creates a new backup manager.
     */
    public BackupManager() {
        this.initialized = false;
    }
    
    /**
     * Initializes the backup manager.
     * 
     * @param backupPath The path where backups should be stored
     */
    public void initialize(Path backupPath) {
        if (initialized) {
            return;
        }
        
        try {
            this.backupPath = backupPath;
            
            // Ensure backup directory exists
            Files.createDirectories(backupPath);
            
            // Start automatic backup scheduler if enabled
            if (EconomyConfig.AUTO_BACKUP_ENABLED) {
                startBackupScheduler();
            }
            
            // Clean up old backups
            cleanupOldBackups();
            
            this.initialized = true;
            Pokecobbleclaim.LOGGER.info("Backup manager initialized at: " + backupPath.toAbsolutePath());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize backup manager", e);
            throw new RuntimeException("Backup manager initialization failed", e);
        }
    }
    
    /**
     * Shuts down the backup manager.
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        try {
            if (backupScheduler != null && !backupScheduler.isShutdown()) {
                backupScheduler.shutdown();
                if (!backupScheduler.awaitTermination(30, TimeUnit.SECONDS)) {
                    backupScheduler.shutdownNow();
                }
            }
            
            this.initialized = false;
            Pokecobbleclaim.LOGGER.info("Backup manager shut down successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during backup manager shutdown", e);
        }
    }
    
    /**
     * Creates a backup of a player's data file.
     * 
     * @param sourceFile The source file to backup
     * @param playerId The player's UUID
     */
    public void createBackup(Path sourceFile, UUID playerId) {
        if (!initialized) {
            throw new IllegalStateException("Backup manager not initialized");
        }
        
        try {
            // Create player-specific backup directory
            Path playerBackupDir = backupPath.resolve(playerId.toString());
            Files.createDirectories(playerBackupDir);
            
            // Generate backup filename with timestamp
            String timestamp = LocalDateTime.now().format(BACKUP_DATE_FORMAT);
            String backupFileName = playerId.toString() + "_" + timestamp + EconomyConfig.BACKUP_EXTENSION;
            Path backupFile = playerBackupDir.resolve(backupFileName);
            
            // Copy the file
            Files.copy(sourceFile, backupFile, StandardCopyOption.REPLACE_EXISTING);
            
            // Clean up old backups for this player
            cleanupPlayerBackups(playerId);
            
            Pokecobbleclaim.LOGGER.debug("Created backup for player " + playerId + ": " + backupFileName);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to create backup for player " + playerId, e);
        }
    }
    
    /**
     * Restores player data from the most recent backup.
     * 
     * @param playerId The player's UUID
     * @return The restored player data, or null if no backup found
     */
    public PlayerEconomyData restoreFromBackup(UUID playerId) {
        if (!initialized) {
            throw new IllegalStateException("Backup manager not initialized");
        }
        
        try {
            Path playerBackupDir = backupPath.resolve(playerId.toString());
            if (!Files.exists(playerBackupDir)) {
                Pokecobbleclaim.LOGGER.warn("No backup directory found for player " + playerId);
                return null;
            }
            
            // Find the most recent backup file
            Path mostRecentBackup = findMostRecentBackup(playerBackupDir, playerId);
            if (mostRecentBackup == null) {
                Pokecobbleclaim.LOGGER.warn("No backup files found for player " + playerId);
                return null;
            }
            
            // Load the backup data
            String jsonData = Files.readString(mostRecentBackup);
            PlayerEconomyData restoredData = GSON.fromJson(jsonData, PlayerEconomyData.class);
            
            if (restoredData != null) {
                Pokecobbleclaim.LOGGER.info("Restored player data from backup for " + playerId + 
                                          " (backup: " + mostRecentBackup.getFileName() + ")");
            }
            
            return restoredData;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to restore from backup for player " + playerId, e);
            return null;
        }
    }
    
    /**
     * Lists all available backups for a player.
     * 
     * @param playerId The player's UUID
     * @return List of backup file paths, sorted by creation time (newest first)
     */
    public List<Path> listPlayerBackups(UUID playerId) {
        if (!initialized) {
            throw new IllegalStateException("Backup manager not initialized");
        }
        
        try {
            Path playerBackupDir = backupPath.resolve(playerId.toString());
            if (!Files.exists(playerBackupDir)) {
                return new ArrayList<>();
            }
            
            List<Path> backups = new ArrayList<>();
            try (Stream<Path> files = Files.list(playerBackupDir)) {
                files.filter(path -> path.toString().endsWith(EconomyConfig.BACKUP_EXTENSION))
                     .sorted((p1, p2) -> {
                         try {
                             return Files.getLastModifiedTime(p2).compareTo(Files.getLastModifiedTime(p1));
                         } catch (IOException e) {
                             return 0;
                         }
                     })
                     .forEach(backups::add);
            }
            
            return backups;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to list backups for player " + playerId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * Restores player data from a specific backup file.
     * 
     * @param backupFile The backup file to restore from
     * @return The restored player data, or null if restoration failed
     */
    public PlayerEconomyData restoreFromSpecificBackup(Path backupFile) {
        if (!initialized) {
            throw new IllegalStateException("Backup manager not initialized");
        }
        
        try {
            if (!Files.exists(backupFile)) {
                Pokecobbleclaim.LOGGER.warn("Backup file does not exist: " + backupFile);
                return null;
            }
            
            String jsonData = Files.readString(backupFile);
            PlayerEconomyData restoredData = GSON.fromJson(jsonData, PlayerEconomyData.class);
            
            if (restoredData != null) {
                Pokecobbleclaim.LOGGER.info("Restored player data from specific backup: " + backupFile.getFileName());
            }
            
            return restoredData;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to restore from specific backup: " + backupFile, e);
            return null;
        }
    }
    
    /**
     * Creates a full backup of all player data.
     * 
     * @param playerDataPath The path containing all player data files
     */
    public void createFullBackup(Path playerDataPath) {
        if (!initialized) {
            throw new IllegalStateException("Backup manager not initialized");
        }
        
        try {
            String timestamp = LocalDateTime.now().format(BACKUP_DATE_FORMAT);
            Path fullBackupDir = backupPath.resolve("full_backup_" + timestamp);
            Files.createDirectories(fullBackupDir);
            
            // Copy all player data files
            try (Stream<Path> files = Files.list(playerDataPath)) {
                files.filter(path -> path.toString().endsWith(EconomyConfig.PLAYER_DATA_EXTENSION))
                     .forEach(sourceFile -> {
                         try {
                             Path targetFile = fullBackupDir.resolve(sourceFile.getFileName());
                             Files.copy(sourceFile, targetFile, StandardCopyOption.REPLACE_EXISTING);
                         } catch (IOException e) {
                             Pokecobbleclaim.LOGGER.error("Failed to backup file: " + sourceFile, e);
                         }
                     });
            }
            
            Pokecobbleclaim.LOGGER.info("Created full backup at: " + fullBackupDir.getFileName());
            
            // Clean up old full backups
            cleanupOldFullBackups();
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to create full backup", e);
        }
    }
    
    /**
     * Starts the automatic backup scheduler.
     */
    private void startBackupScheduler() {
        backupScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "EconomyBackupScheduler");
            t.setDaemon(true);
            return t;
        });
        
        // Schedule periodic backups
        backupScheduler.scheduleAtFixedRate(
            this::performScheduledBackup,
            EconomyConfig.BACKUP_INTERVAL_MS,
            EconomyConfig.BACKUP_INTERVAL_MS,
            TimeUnit.MILLISECONDS
        );
        
        Pokecobbleclaim.LOGGER.info("Automatic backup scheduler started (interval: " + 
                                  EconomyConfig.BACKUP_INTERVAL_MS + "ms)");
    }
    
    /**
     * Performs a scheduled backup operation.
     */
    private void performScheduledBackup() {
        try {
            // This would typically trigger a full backup
            // For now, we'll just log that a scheduled backup would occur
            Pokecobbleclaim.LOGGER.debug("Scheduled backup check performed");
            
            // Clean up old backups during scheduled runs
            cleanupOldBackups();
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during scheduled backup", e);
        }
    }
    
    /**
     * Finds the most recent backup file for a player.
     * 
     * @param playerBackupDir The player's backup directory
     * @param playerId The player's UUID
     * @return The path to the most recent backup, or null if none found
     */
    private Path findMostRecentBackup(Path playerBackupDir, UUID playerId) throws IOException {
        try (Stream<Path> files = Files.list(playerBackupDir)) {
            return files.filter(path -> path.toString().endsWith(EconomyConfig.BACKUP_EXTENSION))
                       .filter(path -> path.getFileName().toString().startsWith(playerId.toString()))
                       .max((p1, p2) -> {
                           try {
                               return Files.getLastModifiedTime(p1).compareTo(Files.getLastModifiedTime(p2));
                           } catch (IOException e) {
                               return 0;
                           }
                       })
                       .orElse(null);
        }
    }
    
    /**
     * Cleans up old backup files for a specific player.
     * 
     * @param playerId The player's UUID
     */
    private void cleanupPlayerBackups(UUID playerId) {
        try {
            Path playerBackupDir = backupPath.resolve(playerId.toString());
            if (!Files.exists(playerBackupDir)) {
                return;
            }
            
            List<Path> backups = new ArrayList<>();
            try (Stream<Path> files = Files.list(playerBackupDir)) {
                files.filter(path -> path.toString().endsWith(EconomyConfig.BACKUP_EXTENSION))
                     .sorted((p1, p2) -> {
                         try {
                             return Files.getLastModifiedTime(p2).compareTo(Files.getLastModifiedTime(p1));
                         } catch (IOException e) {
                             return 0;
                         }
                     })
                     .forEach(backups::add);
            }
            
            // Keep only the most recent backups
            if (backups.size() > EconomyConfig.MAX_BACKUP_FILES) {
                for (int i = EconomyConfig.MAX_BACKUP_FILES; i < backups.size(); i++) {
                    try {
                        Files.delete(backups.get(i));
                        Pokecobbleclaim.LOGGER.debug("Deleted old backup: " + backups.get(i).getFileName());
                    } catch (IOException e) {
                        Pokecobbleclaim.LOGGER.warn("Failed to delete old backup: " + backups.get(i), e);
                    }
                }
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error cleaning up backups for player " + playerId, e);
        }
    }
    
    /**
     * Cleans up old backup files across all players.
     */
    private void cleanupOldBackups() {
        try {
            if (!Files.exists(backupPath)) {
                return;
            }
            
            try (Stream<Path> playerDirs = Files.list(backupPath)) {
                playerDirs.filter(Files::isDirectory)
                         .filter(dir -> !dir.getFileName().toString().startsWith("full_backup_"))
                         .forEach(playerDir -> {
                             try {
                                 String playerIdStr = playerDir.getFileName().toString();
                                 UUID playerId = UUID.fromString(playerIdStr);
                                 cleanupPlayerBackups(playerId);
                             } catch (IllegalArgumentException e) {
                                 // Not a valid UUID directory, skip
                             }
                         });
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during backup cleanup", e);
        }
    }
    
    /**
     * Cleans up old full backup directories.
     */
    private void cleanupOldFullBackups() {
        try {
            if (!Files.exists(backupPath)) {
                return;
            }
            
            List<Path> fullBackups = new ArrayList<>();
            try (Stream<Path> dirs = Files.list(backupPath)) {
                dirs.filter(Files::isDirectory)
                    .filter(dir -> dir.getFileName().toString().startsWith("full_backup_"))
                    .sorted((p1, p2) -> {
                        try {
                            return Files.getLastModifiedTime(p2).compareTo(Files.getLastModifiedTime(p1));
                        } catch (IOException e) {
                            return 0;
                        }
                    })
                    .forEach(fullBackups::add);
            }
            
            // Keep only the most recent full backups
            if (fullBackups.size() > EconomyConfig.MAX_BACKUP_FILES) {
                for (int i = EconomyConfig.MAX_BACKUP_FILES; i < fullBackups.size(); i++) {
                    try {
                        deleteDirectory(fullBackups.get(i));
                        Pokecobbleclaim.LOGGER.debug("Deleted old full backup: " + fullBackups.get(i).getFileName());
                    } catch (IOException e) {
                        Pokecobbleclaim.LOGGER.warn("Failed to delete old full backup: " + fullBackups.get(i), e);
                    }
                }
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error cleaning up full backups", e);
        }
    }
    
    /**
     * Recursively deletes a directory and all its contents.
     * 
     * @param directory The directory to delete
     */
    private void deleteDirectory(Path directory) throws IOException {
        try (Stream<Path> files = Files.walk(directory)) {
            files.sorted(Comparator.reverseOrder())
                 .forEach(path -> {
                     try {
                         Files.delete(path);
                     } catch (IOException e) {
                         Pokecobbleclaim.LOGGER.warn("Failed to delete: " + path, e);
                     }
                 });
        }
    }
    
    /**
     * Checks if the backup manager is initialized.
     * 
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}
