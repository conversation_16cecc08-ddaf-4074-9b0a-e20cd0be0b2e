package com.pokecobble.economy.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.PlayerEconomyData;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.MinecraftClient;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Client-side economy data manager.
 * Manages cached economy data received from the server.
 */
@Environment(EnvType.CLIENT)
public class ClientEconomyManager {
    private static ClientEconomyManager instance;
    
    // Cached economy data
    private final Map<UUID, Long> playerBalances = new ConcurrentHashMap<>();
    private final Map<UUID, PlayerEconomyData> playerData = new ConcurrentHashMap<>();
    private final Map<UUID, Integer> dataVersions = new ConcurrentHashMap<>();
    private final Map<UUID, Long> lastUpdateTimes = new ConcurrentHashMap<>();
    
    // Cache timeout (5 minutes)
    private static final long CACHE_TIMEOUT_MS = 5 * 60 * 1000;
    
    private ClientEconomyManager() {
        // Private constructor for singleton
    }
    
    /**
     * Gets the singleton instance.
     */
    public static ClientEconomyManager getInstance() {
        if (instance == null) {
            instance = new ClientEconomyManager();
        }
        return instance;
    }
    
    /**
     * Updates a player's balance from server sync.
     */
    public void updateBalance(UUID playerId, long balance, int version) {
        if (playerId == null) {
            return;
        }
        
        int currentVersion = dataVersions.getOrDefault(playerId, -1);
        
        // Only update if version is newer or equal (to handle initial sync)
        if (version >= currentVersion) {
            playerBalances.put(playerId, balance);
            dataVersions.put(playerId, version);
            lastUpdateTimes.put(playerId, System.currentTimeMillis());
            
            Pokecobbleclaim.LOGGER.debug("Updated client balance cache for player " + playerId + 
                                       " (version " + version + ", balance: " + balance + ")");
        }
    }
    
    /**
     * Updates a player's full economy data from server sync.
     */
    public void updatePlayerData(UUID playerId, long balance, long totalEarned, long totalSpent,
                               int transactionCount, int version, long lastUpdated) {
        if (playerId == null) {
            return;
        }

        int currentVersion = dataVersions.getOrDefault(playerId, -1);

        // Only update if version is newer or equal (to handle initial sync)
        if (version >= currentVersion) {
            // Update balance cache
            playerBalances.put(playerId, balance);
            dataVersions.put(playerId, version);
            lastUpdateTimes.put(playerId, System.currentTimeMillis());

            // Create simplified player data for client-side use
            // Note: We can't set all fields as they don't have public setters
            PlayerEconomyData data = new PlayerEconomyData(playerId, "");
            data.setBalance(balance);
            data.setDataVersion(version);

            // Store additional data in a simple structure for display purposes
            // The balance is the most important for client-side display
            playerData.put(playerId, data);

            Pokecobbleclaim.LOGGER.debug("Updated client economy data cache for player " + playerId +
                                       " (version " + version + ", balance: " + balance + ")");
        }
    }
    
    /**
     * Gets a player's balance from the cache.
     */
    public long getBalance(UUID playerId) {
        if (playerId == null) {
            Pokecobbleclaim.LOGGER.debug("ClientEconomyManager: playerId is null, returning 0");
            return 0L;
        }

        // Check if cache is expired
        if (isCacheExpired(playerId)) {
            Pokecobbleclaim.LOGGER.debug("ClientEconomyManager: Cache expired for player " + playerId + ", requesting fresh data");

            // Request fresh data from server
            requestBalanceFromServer(playerId);

            // Return cached data if available, otherwise 0
            long balance = playerBalances.getOrDefault(playerId, 0L);
            Pokecobbleclaim.LOGGER.debug("ClientEconomyManager: Cache expired, returning cached balance " + balance + " for player " + playerId);
            return balance;
        }

        long balance = playerBalances.getOrDefault(playerId, 0L);
        Pokecobbleclaim.LOGGER.debug("ClientEconomyManager: Returning balance " + balance + " for player " + playerId);
        return balance;
    }
    
    /**
     * Gets a player's full economy data from the cache.
     */
    public PlayerEconomyData getPlayerData(UUID playerId) {
        if (playerId == null) {
            return null;
        }
        
        // Check if cache is expired
        if (isCacheExpired(playerId)) {
            return null;
        }
        
        return playerData.get(playerId);
    }
    
    /**
     * Gets the current player's balance.
     */
    public long getCurrentPlayerBalance() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null) {
            Pokecobbleclaim.LOGGER.debug("ClientEconomyManager: No current player, returning 0");
            return 0L;
        }

        UUID playerId = client.player.getUuid();
        Pokecobbleclaim.LOGGER.debug("ClientEconomyManager: Getting current player balance for " + playerId);
        return getBalance(playerId);
    }
    
    /**
     * Checks if the cache for a player is expired.
     */
    private boolean isCacheExpired(UUID playerId) {
        Long lastUpdate = lastUpdateTimes.get(playerId);
        if (lastUpdate == null) {
            return true; // No data cached
        }
        
        return System.currentTimeMillis() - lastUpdate > CACHE_TIMEOUT_MS;
    }
    
    /**
     * Invalidates a player's cached data.
     */
    public void invalidatePlayer(UUID playerId) {
        playerBalances.remove(playerId);
        playerData.remove(playerId);
        dataVersions.remove(playerId);
        lastUpdateTimes.remove(playerId);
        
        Pokecobbleclaim.LOGGER.debug("Invalidated cache for player " + playerId);
    }
    
    /**
     * Clears all cached data.
     */
    public void clearCache() {
        playerBalances.clear();
        playerData.clear();
        dataVersions.clear();
        lastUpdateTimes.clear();
        
        Pokecobbleclaim.LOGGER.debug("Cleared client economy cache");
    }
    
    /**
     * Gets cache statistics for debugging.
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("cachedBalances", playerBalances.size());
        stats.put("cachedPlayerData", playerData.size());
        stats.put("currentPlayerBalance", getCurrentPlayerBalance());
        return stats;
    }
    
    /**
     * Checks if a player has cached data.
     */
    public boolean hasPlayerData(UUID playerId) {
        return playerBalances.containsKey(playerId) && !isCacheExpired(playerId);
    }

    /**
     * Requests fresh balance data from the server for a specific player.
     */
    private void requestBalanceFromServer(UUID playerId) {
        try {
            // Create balance request packet
            net.minecraft.network.PacketByteBuf buf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();
            buf.writeUuid(playerId);

            // Send balance request to server
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                com.pokecobble.economy.network.EconomyNetworkManager.ECONOMY_BALANCE_REQUEST, buf
            );

            Pokecobbleclaim.LOGGER.debug("ClientEconomyManager: Sent balance request for player " + playerId);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("ClientEconomyManager: Failed to request balance from server for player " + playerId, e);
        }
    }
}
