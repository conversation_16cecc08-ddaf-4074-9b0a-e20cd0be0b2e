package com.pokecobble.economy.core;

import com.google.gson.annotations.SerializedName;

/**
 * Security flags for player economy data.
 * Used to track security-related information and potential issues.
 */
public class SecurityFlags {
    
    @SerializedName("is_frozen")
    private boolean isFrozen;
    
    @SerializedName("is_flagged")
    private boolean isFlagged;
    
    @SerializedName("suspicious_activity_count")
    private int suspiciousActivityCount;
    
    @SerializedName("last_suspicious_activity")
    private long lastSuspiciousActivity;
    
    @SerializedName("failed_transaction_attempts")
    private int failedTransactionAttempts;
    
    @SerializedName("last_failed_attempt")
    private long lastFailedAttempt;
    
    @SerializedName("rate_limit_violations")
    private int rateLimitViolations;
    
    @SerializedName("last_rate_limit_violation")
    private long lastRateLimitViolation;
    
    @SerializedName("duplicate_transaction_attempts")
    private int duplicateTransactionAttempts;
    
    @SerializedName("last_duplicate_attempt")
    private long lastDuplicateAttempt;
    
    @SerializedName("admin_notes")
    private String adminNotes;
    
    @SerializedName("auto_freeze_reason")
    private String autoFreezeReason;
    
    @SerializedName("freeze_timestamp")
    private long freezeTimestamp;
    
    @SerializedName("freeze_duration")
    private long freezeDuration;
    
    /**
     * Default constructor.
     */
    public SecurityFlags() {
        this.isFrozen = false;
        this.isFlagged = false;
        this.suspiciousActivityCount = 0;
        this.failedTransactionAttempts = 0;
        this.rateLimitViolations = 0;
        this.duplicateTransactionAttempts = 0;
    }
    
    /**
     * Checks if the account is frozen.
     * 
     * @return true if frozen, false otherwise
     */
    public boolean isFrozen() {
        // Check if temporary freeze has expired
        if (isFrozen && freezeDuration > 0) {
            long currentTime = System.currentTimeMillis();
            if (currentTime - freezeTimestamp > freezeDuration) {
                unfreeze();
                return false;
            }
        }
        return isFrozen;
    }
    
    /**
     * Freezes the account.
     * 
     * @param reason The reason for freezing
     * @param duration The freeze duration in milliseconds (0 for permanent)
     */
    public void freeze(String reason, long duration) {
        this.isFrozen = true;
        this.autoFreezeReason = reason;
        this.freezeTimestamp = System.currentTimeMillis();
        this.freezeDuration = duration;
    }
    
    /**
     * Unfreezes the account.
     */
    public void unfreeze() {
        this.isFrozen = false;
        this.autoFreezeReason = null;
        this.freezeTimestamp = 0;
        this.freezeDuration = 0;
    }
    
    /**
     * Checks if the account is flagged for review.
     * 
     * @return true if flagged, false otherwise
     */
    public boolean isFlagged() {
        return isFlagged;
    }
    
    /**
     * Sets the flagged status.
     * 
     * @param flagged true to flag, false to unflag
     */
    public void setFlagged(boolean flagged) {
        this.isFlagged = flagged;
    }
    
    /**
     * Gets the suspicious activity count.
     * 
     * @return The count of suspicious activities
     */
    public int getSuspiciousActivityCount() {
        return suspiciousActivityCount;
    }
    
    /**
     * Records a suspicious activity.
     */
    public void recordSuspiciousActivity() {
        this.suspiciousActivityCount++;
        this.lastSuspiciousActivity = System.currentTimeMillis();
        
        // Auto-flag if too many suspicious activities
        if (suspiciousActivityCount >= 5) {
            setFlagged(true);
        }
        
        // Auto-freeze if extremely suspicious
        if (suspiciousActivityCount >= 10) {
            freeze("Automatic freeze due to excessive suspicious activity", 3600000L); // 1 hour
        }
    }
    
    /**
     * Gets the timestamp of the last suspicious activity.
     * 
     * @return The timestamp
     */
    public long getLastSuspiciousActivity() {
        return lastSuspiciousActivity;
    }
    
    /**
     * Gets the failed transaction attempts count.
     * 
     * @return The count of failed attempts
     */
    public int getFailedTransactionAttempts() {
        return failedTransactionAttempts;
    }
    
    /**
     * Records a failed transaction attempt.
     */
    public void recordFailedTransactionAttempt() {
        this.failedTransactionAttempts++;
        this.lastFailedAttempt = System.currentTimeMillis();
        
        // Auto-freeze if too many failed attempts
        if (failedTransactionAttempts >= 20) {
            freeze("Automatic freeze due to excessive failed transaction attempts", 1800000L); // 30 minutes
        }
    }
    
    /**
     * Gets the timestamp of the last failed attempt.
     * 
     * @return The timestamp
     */
    public long getLastFailedAttempt() {
        return lastFailedAttempt;
    }
    
    /**
     * Gets the rate limit violations count.
     * 
     * @return The count of rate limit violations
     */
    public int getRateLimitViolations() {
        return rateLimitViolations;
    }
    
    /**
     * Records a rate limit violation.
     */
    public void recordRateLimitViolation() {
        this.rateLimitViolations++;
        this.lastRateLimitViolation = System.currentTimeMillis();
        
        // Auto-freeze if too many rate limit violations
        if (rateLimitViolations >= 10) {
            freeze("Automatic freeze due to excessive rate limit violations", 900000L); // 15 minutes
        }
    }
    
    /**
     * Gets the timestamp of the last rate limit violation.
     * 
     * @return The timestamp
     */
    public long getLastRateLimitViolation() {
        return lastRateLimitViolation;
    }
    
    /**
     * Gets the duplicate transaction attempts count.
     * 
     * @return The count of duplicate attempts
     */
    public int getDuplicateTransactionAttempts() {
        return duplicateTransactionAttempts;
    }
    
    /**
     * Records a duplicate transaction attempt.
     */
    public void recordDuplicateTransactionAttempt() {
        this.duplicateTransactionAttempts++;
        this.lastDuplicateAttempt = System.currentTimeMillis();
        recordSuspiciousActivity(); // Duplicate attempts are suspicious
    }
    
    /**
     * Gets the timestamp of the last duplicate attempt.
     * 
     * @return The timestamp
     */
    public long getLastDuplicateAttempt() {
        return lastDuplicateAttempt;
    }
    
    /**
     * Gets the admin notes.
     * 
     * @return The admin notes
     */
    public String getAdminNotes() {
        return adminNotes;
    }
    
    /**
     * Sets the admin notes.
     * 
     * @param adminNotes The admin notes
     */
    public void setAdminNotes(String adminNotes) {
        this.adminNotes = adminNotes;
    }
    
    /**
     * Gets the auto-freeze reason.
     * 
     * @return The freeze reason
     */
    public String getAutoFreezeReason() {
        return autoFreezeReason;
    }
    
    /**
     * Gets the freeze timestamp.
     * 
     * @return The freeze timestamp
     */
    public long getFreezeTimestamp() {
        return freezeTimestamp;
    }
    
    /**
     * Gets the freeze duration.
     * 
     * @return The freeze duration in milliseconds
     */
    public long getFreezeDuration() {
        return freezeDuration;
    }
    
    /**
     * Resets all security counters (admin action).
     */
    public void resetCounters() {
        this.suspiciousActivityCount = 0;
        this.failedTransactionAttempts = 0;
        this.rateLimitViolations = 0;
        this.duplicateTransactionAttempts = 0;
        this.lastSuspiciousActivity = 0;
        this.lastFailedAttempt = 0;
        this.lastRateLimitViolation = 0;
        this.lastDuplicateAttempt = 0;
    }
    
    /**
     * Checks if the account has any security issues.
     * 
     * @return true if there are security issues, false otherwise
     */
    public boolean hasSecurityIssues() {
        return isFrozen() || isFlagged() || suspiciousActivityCount > 0 || 
               failedTransactionAttempts > 5 || rateLimitViolations > 3;
    }
    
    @Override
    public String toString() {
        return "SecurityFlags{" +
                "isFrozen=" + isFrozen +
                ", isFlagged=" + isFlagged +
                ", suspiciousActivityCount=" + suspiciousActivityCount +
                ", failedTransactionAttempts=" + failedTransactionAttempts +
                ", rateLimitViolations=" + rateLimitViolations +
                '}';
    }
}
