package com.pokecobble.economy.core;

/**
 * Configuration constants for the economy system.
 * Contains all configurable values for security, performance, and functionality.
 */
public class EconomyConfig {
    
    // File System Configuration
    public static final String ECONOMY_DATA_FOLDER = "economy";
    public static final String PLAYER_DATA_FOLDER = "players";
    public static final String TRANSACTION_LOG_FOLDER = "transactions";
    public static final String BACKUP_FOLDER = "backups";
    public static final String TEMP_FOLDER = "temp";
    
    // File Extensions
    public static final String PLAYER_DATA_EXTENSION = ".econ";
    public static final String TRANSACTION_LOG_EXTENSION = ".log";
    public static final String BACKUP_EXTENSION = ".backup";
    public static final String ENCRYPTED_EXTENSION = ".enc";
    
    // Security Configuration
    public static final boolean ENABLE_ENCRYPTION = true;
    public static final String ENCRYPTION_ALGORITHM = "AES/GCM/NoPadding";
    public static final int ENCRYPTION_KEY_LENGTH = 256;
    public static final int GCM_IV_LENGTH = 12;
    public static final int GCM_TAG_LENGTH = 16;
    
    // Transaction Security
    public static final long MAX_TRANSACTION_AMOUNT = 1_000_000_000L; // 1 billion max
    public static final long MIN_TRANSACTION_AMOUNT = 1L;
    public static final int MAX_TRANSACTIONS_PER_MINUTE = 60;
    public static final int MAX_TRANSACTIONS_PER_HOUR = 1000;
    
    // Balance Limits
    public static final long MAX_PLAYER_BALANCE = 10_000_000_000L; // 10 billion max
    public static final long MIN_PLAYER_BALANCE = 0L;
    public static final long DEFAULT_STARTING_BALANCE = 1000L;
    
    // Caching Configuration
    public static final int CACHE_EXPIRY_MINUTES = 5;
    public static final int MAX_CACHE_SIZE = 1000;
    public static final boolean ENABLE_CACHE_COMPRESSION = true;
    
    // Backup Configuration
    public static final int MAX_BACKUP_FILES = 10;
    public static final long BACKUP_INTERVAL_MS = 300_000L; // 5 minutes
    public static final boolean AUTO_BACKUP_ENABLED = true;
    
    // Network Configuration
    public static final int PACKET_TIMEOUT_MS = 5000;
    public static final int MAX_PACKET_SIZE = 1024 * 64; // 64KB
    public static final int SYNC_RETRY_ATTEMPTS = 3;
    public static final long SYNC_RETRY_DELAY_MS = 1000L;
    
    // Transaction Logging
    public static final boolean ENABLE_TRANSACTION_LOGGING = true;
    public static final int MAX_LOG_FILE_SIZE_MB = 10;
    public static final int MAX_LOG_FILES = 50;
    public static final boolean LOG_ALL_OPERATIONS = true;
    
    // Performance Configuration
    public static final int IO_THREAD_POOL_SIZE = 4;
    public static final int NETWORK_THREAD_POOL_SIZE = 2;
    public static final long FILE_LOCK_TIMEOUT_MS = 10_000L;
    public static final int BATCH_OPERATION_SIZE = 100;
    
    // Validation Configuration
    public static final boolean STRICT_VALIDATION = true;
    public static final boolean VALIDATE_CHECKSUMS = true;
    public static final boolean ENABLE_INTEGRITY_CHECKS = true;
    
    // Rate Limiting
    public static final int RATE_LIMIT_WINDOW_SECONDS = 60;
    public static final int MAX_REQUESTS_PER_WINDOW = 100;
    public static final boolean ENABLE_RATE_LIMITING = true;
    
    // Anti-Duplication
    public static final boolean ENABLE_DUPLICATE_DETECTION = true;
    public static final long DUPLICATE_DETECTION_WINDOW_MS = 30_000L; // 30 seconds
    public static final int MAX_DUPLICATE_TRANSACTIONS = 1;
    
    // Error Handling
    public static final int MAX_RETRY_ATTEMPTS = 3;
    public static final long RETRY_DELAY_MS = 1000L;
    public static final boolean FAIL_FAST_ON_CORRUPTION = true;
    
    // Monitoring
    public static final boolean ENABLE_PERFORMANCE_MONITORING = true;
    public static final boolean ENABLE_SECURITY_MONITORING = true;
    public static final long MONITORING_INTERVAL_MS = 60_000L; // 1 minute
    
    // Data Integrity
    public static final boolean ENABLE_DATA_CHECKSUMS = true;
    public static final String CHECKSUM_ALGORITHM = "SHA-256";
    public static final boolean VERIFY_ON_LOAD = true;
    public static final boolean VERIFY_ON_SAVE = true;
    
    // Synchronization
    public static final boolean ENABLE_REAL_TIME_SYNC = true;
    public static final long SYNC_INTERVAL_MS = 1000L; // 1 second
    public static final boolean SYNC_ON_JOIN = true;
    public static final boolean SYNC_ON_LEAVE = true;
    
    // Development/Debug
    public static final boolean DEBUG_MODE = false;
    public static final boolean VERBOSE_LOGGING = false;
    public static final boolean ENABLE_PROFILING = false;
    
    /**
     * Private constructor to prevent instantiation.
     */
    private EconomyConfig() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
    
    /**
     * Validates the configuration values.
     * 
     * @throws IllegalStateException if any configuration value is invalid
     */
    public static void validateConfig() {
        if (MAX_PLAYER_BALANCE <= MIN_PLAYER_BALANCE) {
            throw new IllegalStateException("MAX_PLAYER_BALANCE must be greater than MIN_PLAYER_BALANCE");
        }
        
        if (MAX_TRANSACTION_AMOUNT <= MIN_TRANSACTION_AMOUNT) {
            throw new IllegalStateException("MAX_TRANSACTION_AMOUNT must be greater than MIN_TRANSACTION_AMOUNT");
        }
        
        if (DEFAULT_STARTING_BALANCE < MIN_PLAYER_BALANCE || DEFAULT_STARTING_BALANCE > MAX_PLAYER_BALANCE) {
            throw new IllegalStateException("DEFAULT_STARTING_BALANCE must be within balance limits");
        }
        
        if (CACHE_EXPIRY_MINUTES <= 0) {
            throw new IllegalStateException("CACHE_EXPIRY_MINUTES must be positive");
        }
        
        if (MAX_CACHE_SIZE <= 0) {
            throw new IllegalStateException("MAX_CACHE_SIZE must be positive");
        }
        
        if (IO_THREAD_POOL_SIZE <= 0) {
            throw new IllegalStateException("IO_THREAD_POOL_SIZE must be positive");
        }
        
        if (NETWORK_THREAD_POOL_SIZE <= 0) {
            throw new IllegalStateException("NETWORK_THREAD_POOL_SIZE must be positive");
        }
    }
}
