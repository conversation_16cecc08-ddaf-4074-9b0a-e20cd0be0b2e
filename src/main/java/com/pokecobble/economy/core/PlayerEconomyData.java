package com.pokecobble.economy.core;

import com.google.gson.annotations.SerializedName;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * Represents a player's economy data including balance, transaction history, and metadata.
 * This class is serializable and includes integrity checks.
 */
public class PlayerEconomyData {
    
    @SerializedName("player_id")
    private String playerId;
    
    @SerializedName("player_name")
    private String playerName;
    
    @SerializedName("balance")
    private long balance;
    
    @SerializedName("last_updated")
    private long lastUpdated;
    
    @SerializedName("created_at")
    private long createdAt;
    
    @SerializedName("last_login")
    private long lastLogin;
    
    @SerializedName("total_earned")
    private long totalEarned;
    
    @SerializedName("total_spent")
    private long totalSpent;
    
    @SerializedName("transaction_count")
    private int transactionCount;
    
    @SerializedName("data_version")
    private int dataVersion;
    
    @SerializedName("checksum")
    private String checksum;
    
    @SerializedName("recent_transactions")
    private List<TransactionRecord> recentTransactions;
    
    @SerializedName("security_flags")
    private SecurityFlags securityFlags;
    
    /**
     * Default constructor for GSON deserialization.
     */
    public PlayerEconomyData() {
        this.recentTransactions = new ArrayList<>();
        this.securityFlags = new SecurityFlags();
        this.dataVersion = 1;
        this.createdAt = System.currentTimeMillis();
        this.lastUpdated = System.currentTimeMillis();
    }
    
    /**
     * Creates new player economy data with default values.
     * 
     * @param playerId The player's UUID
     * @param playerName The player's name
     */
    public PlayerEconomyData(UUID playerId, String playerName) {
        this();
        this.playerId = playerId.toString();
        this.playerName = playerName;
        this.balance = EconomyConfig.DEFAULT_STARTING_BALANCE;
        this.totalEarned = EconomyConfig.DEFAULT_STARTING_BALANCE;
        this.totalSpent = 0L;
        this.transactionCount = 0;
    }
    
    /**
     * Gets the player's UUID.
     * 
     * @return The player's UUID
     */
    public UUID getPlayerId() {
        return UUID.fromString(playerId);
    }
    
    /**
     * Sets the player's UUID.
     * 
     * @param playerId The player's UUID
     */
    public void setPlayerId(UUID playerId) {
        this.playerId = playerId.toString();
        updateTimestamp();
    }
    
    /**
     * Gets the player's name.
     * 
     * @return The player's name
     */
    public String getPlayerName() {
        return playerName;
    }
    
    /**
     * Sets the player's name.
     * 
     * @param playerName The player's name
     */
    public void setPlayerName(String playerName) {
        this.playerName = playerName;
        updateTimestamp();
    }
    
    /**
     * Gets the player's current balance.
     * 
     * @return The current balance
     */
    public long getBalance() {
        return balance;
    }
    
    /**
     * Sets the player's balance with validation.
     * 
     * @param balance The new balance
     * @throws IllegalArgumentException if balance is invalid
     */
    public void setBalance(long balance) {
        validateBalance(balance);
        this.balance = balance;
        updateTimestamp();
    }
    
    /**
     * Adds to the player's balance with validation.
     * 
     * @param amount The amount to add
     * @throws IllegalArgumentException if the operation would result in invalid balance
     */
    public void addBalance(long amount) {
        long newBalance = this.balance + amount;
        validateBalance(newBalance);
        this.balance = newBalance;
        
        if (amount > 0) {
            this.totalEarned += amount;
        } else {
            this.totalSpent += Math.abs(amount);
        }
        
        updateTimestamp();
    }
    
    /**
     * Subtracts from the player's balance with validation.
     * 
     * @param amount The amount to subtract
     * @throws IllegalArgumentException if the operation would result in invalid balance
     */
    public void subtractBalance(long amount) {
        long newBalance = this.balance - amount;
        validateBalance(newBalance);
        this.balance = newBalance;
        this.totalSpent += amount;
        updateTimestamp();
    }
    
    /**
     * Gets the timestamp of the last update.
     * 
     * @return The last update timestamp
     */
    public long getLastUpdated() {
        return lastUpdated;
    }
    
    /**
     * Gets the creation timestamp.
     * 
     * @return The creation timestamp
     */
    public long getCreatedAt() {
        return createdAt;
    }
    
    /**
     * Gets the last login timestamp.
     * 
     * @return The last login timestamp
     */
    public long getLastLogin() {
        return lastLogin;
    }
    
    /**
     * Sets the last login timestamp.
     * 
     * @param lastLogin The last login timestamp
     */
    public void setLastLogin(long lastLogin) {
        this.lastLogin = lastLogin;
        updateTimestamp();
    }
    
    /**
     * Gets the total amount earned by the player.
     * 
     * @return The total earned amount
     */
    public long getTotalEarned() {
        return totalEarned;
    }
    
    /**
     * Gets the total amount spent by the player.
     * 
     * @return The total spent amount
     */
    public long getTotalSpent() {
        return totalSpent;
    }
    
    /**
     * Gets the total number of transactions.
     * 
     * @return The transaction count
     */
    public int getTransactionCount() {
        return transactionCount;
    }
    
    /**
     * Increments the transaction count.
     */
    public void incrementTransactionCount() {
        this.transactionCount++;
        updateTimestamp();
    }
    
    /**
     * Gets the data version.
     * 
     * @return The data version
     */
    public int getDataVersion() {
        return dataVersion;
    }
    
    /**
     * Sets the data version.
     * 
     * @param dataVersion The data version
     */
    public void setDataVersion(int dataVersion) {
        this.dataVersion = dataVersion;
        updateTimestamp();
    }
    
    /**
     * Gets the data checksum.
     * 
     * @return The checksum
     */
    public String getChecksum() {
        return checksum;
    }
    
    /**
     * Sets the data checksum.
     * 
     * @param checksum The checksum
     */
    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }
    
    /**
     * Gets the recent transactions list.
     * 
     * @return The recent transactions
     */
    public List<TransactionRecord> getRecentTransactions() {
        return new ArrayList<>(recentTransactions);
    }
    
    /**
     * Adds a transaction record.
     * 
     * @param transaction The transaction to add
     */
    public void addTransaction(TransactionRecord transaction) {
        recentTransactions.add(0, transaction); // Add to beginning
        
        // Keep only the most recent transactions
        if (recentTransactions.size() > 50) {
            recentTransactions = recentTransactions.subList(0, 50);
        }
        
        incrementTransactionCount();
    }
    
    /**
     * Gets the security flags.
     * 
     * @return The security flags
     */
    public SecurityFlags getSecurityFlags() {
        return securityFlags;
    }
    
    /**
     * Updates the last updated timestamp.
     */
    private void updateTimestamp() {
        this.lastUpdated = System.currentTimeMillis();
    }
    
    /**
     * Validates a balance value.
     * 
     * @param balance The balance to validate
     * @throws IllegalArgumentException if the balance is invalid
     */
    private void validateBalance(long balance) {
        if (balance < EconomyConfig.MIN_PLAYER_BALANCE) {
            throw new IllegalArgumentException("Balance cannot be less than " + EconomyConfig.MIN_PLAYER_BALANCE);
        }
        if (balance > EconomyConfig.MAX_PLAYER_BALANCE) {
            throw new IllegalArgumentException("Balance cannot exceed " + EconomyConfig.MAX_PLAYER_BALANCE);
        }
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PlayerEconomyData that = (PlayerEconomyData) o;
        return Objects.equals(playerId, that.playerId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(playerId);
    }
    
    @Override
    public String toString() {
        return "PlayerEconomyData{" +
                "playerId='" + playerId + '\'' +
                ", playerName='" + playerName + '\'' +
                ", balance=" + balance +
                ", lastUpdated=" + lastUpdated +
                '}';
    }
}
