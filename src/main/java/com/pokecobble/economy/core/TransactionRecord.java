package com.pokecobble.economy.core;

import com.google.gson.annotations.SerializedName;
import java.util.Objects;
import java.util.UUID;

/**
 * Represents a single transaction record in the economy system.
 * Contains all necessary information for transaction tracking and auditing.
 */
public class TransactionRecord {
    
    @SerializedName("transaction_id")
    private String transactionId;
    
    @SerializedName("timestamp")
    private long timestamp;
    
    @SerializedName("type")
    private TransactionType type;
    
    @SerializedName("amount")
    private long amount;
    
    @SerializedName("from_player")
    private String fromPlayer;
    
    @SerializedName("to_player")
    private String toPlayer;
    
    @SerializedName("description")
    private String description;
    
    @SerializedName("balance_before")
    private long balanceBefore;
    
    @SerializedName("balance_after")
    private long balanceAfter;
    
    @SerializedName("server_timestamp")
    private long serverTimestamp;
    
    @SerializedName("source")
    private String source;
    
    @SerializedName("metadata")
    private String metadata;
    
    /**
     * Transaction types supported by the economy system.
     */
    public enum TransactionType {
        DEPOSIT,        // Adding money to account
        WITHDRAWAL,     // Removing money from account
        TRANSFER_SEND,  // Sending money to another player
        TRANSFER_RECEIVE, // Receiving money from another player
        PURCHASE,       // Buying something
        SALE,          // Selling something
        REWARD,        // Receiving a reward
        PENALTY,       // Paying a penalty/fine
        ADMIN_ADD,     // Admin adding money
        ADMIN_REMOVE,  // Admin removing money
        SYSTEM         // System-generated transaction
    }
    
    /**
     * Default constructor for GSON deserialization.
     */
    public TransactionRecord() {
        this.transactionId = UUID.randomUUID().toString();
        this.timestamp = System.currentTimeMillis();
        this.serverTimestamp = System.currentTimeMillis();
    }
    
    /**
     * Creates a new transaction record.
     * 
     * @param type The transaction type
     * @param amount The transaction amount
     * @param fromPlayer The source player (can be null for system transactions)
     * @param toPlayer The target player (can be null for system transactions)
     * @param description A description of the transaction
     * @param balanceBefore The balance before the transaction
     * @param balanceAfter The balance after the transaction
     */
    public TransactionRecord(TransactionType type, long amount, UUID fromPlayer, UUID toPlayer, 
                           String description, long balanceBefore, long balanceAfter) {
        this();
        this.type = type;
        this.amount = amount;
        this.fromPlayer = fromPlayer != null ? fromPlayer.toString() : null;
        this.toPlayer = toPlayer != null ? toPlayer.toString() : null;
        this.description = description;
        this.balanceBefore = balanceBefore;
        this.balanceAfter = balanceAfter;
        this.source = "GAME";
    }
    
    /**
     * Gets the transaction ID.
     * 
     * @return The transaction ID
     */
    public UUID getTransactionId() {
        return UUID.fromString(transactionId);
    }
    
    /**
     * Gets the transaction timestamp.
     * 
     * @return The timestamp
     */
    public long getTimestamp() {
        return timestamp;
    }
    
    /**
     * Sets the transaction timestamp.
     * 
     * @param timestamp The timestamp
     */
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    /**
     * Gets the transaction type.
     * 
     * @return The transaction type
     */
    public TransactionType getType() {
        return type;
    }
    
    /**
     * Sets the transaction type.
     * 
     * @param type The transaction type
     */
    public void setType(TransactionType type) {
        this.type = type;
    }
    
    /**
     * Gets the transaction amount.
     * 
     * @return The amount
     */
    public long getAmount() {
        return amount;
    }
    
    /**
     * Sets the transaction amount.
     * 
     * @param amount The amount
     */
    public void setAmount(long amount) {
        this.amount = amount;
    }
    
    /**
     * Gets the source player UUID.
     * 
     * @return The source player UUID, or null if not applicable
     */
    public UUID getFromPlayer() {
        return fromPlayer != null ? UUID.fromString(fromPlayer) : null;
    }
    
    /**
     * Sets the source player.
     * 
     * @param fromPlayer The source player UUID
     */
    public void setFromPlayer(UUID fromPlayer) {
        this.fromPlayer = fromPlayer != null ? fromPlayer.toString() : null;
    }
    
    /**
     * Gets the target player UUID.
     * 
     * @return The target player UUID, or null if not applicable
     */
    public UUID getToPlayer() {
        return toPlayer != null ? UUID.fromString(toPlayer) : null;
    }
    
    /**
     * Sets the target player.
     * 
     * @param toPlayer The target player UUID
     */
    public void setToPlayer(UUID toPlayer) {
        this.toPlayer = toPlayer != null ? toPlayer.toString() : null;
    }
    
    /**
     * Gets the transaction description.
     * 
     * @return The description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Sets the transaction description.
     * 
     * @param description The description
     */
    public void setDescription(String description) {
        this.description = description;
    }
    
    /**
     * Gets the balance before the transaction.
     * 
     * @return The balance before
     */
    public long getBalanceBefore() {
        return balanceBefore;
    }
    
    /**
     * Sets the balance before the transaction.
     * 
     * @param balanceBefore The balance before
     */
    public void setBalanceBefore(long balanceBefore) {
        this.balanceBefore = balanceBefore;
    }
    
    /**
     * Gets the balance after the transaction.
     * 
     * @return The balance after
     */
    public long getBalanceAfter() {
        return balanceAfter;
    }
    
    /**
     * Sets the balance after the transaction.
     * 
     * @param balanceAfter The balance after
     */
    public void setBalanceAfter(long balanceAfter) {
        this.balanceAfter = balanceAfter;
    }
    
    /**
     * Gets the server timestamp.
     * 
     * @return The server timestamp
     */
    public long getServerTimestamp() {
        return serverTimestamp;
    }
    
    /**
     * Sets the server timestamp.
     * 
     * @param serverTimestamp The server timestamp
     */
    public void setServerTimestamp(long serverTimestamp) {
        this.serverTimestamp = serverTimestamp;
    }
    
    /**
     * Gets the transaction source.
     * 
     * @return The source
     */
    public String getSource() {
        return source;
    }
    
    /**
     * Sets the transaction source.
     * 
     * @param source The source
     */
    public void setSource(String source) {
        this.source = source;
    }
    
    /**
     * Gets the transaction metadata.
     * 
     * @return The metadata
     */
    public String getMetadata() {
        return metadata;
    }
    
    /**
     * Sets the transaction metadata.
     * 
     * @param metadata The metadata
     */
    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }
    
    /**
     * Validates the transaction record.
     * 
     * @throws IllegalStateException if the transaction is invalid
     */
    public void validate() {
        if (type == null) {
            throw new IllegalStateException("Transaction type cannot be null");
        }
        
        if (amount < 0) {
            throw new IllegalStateException("Transaction amount cannot be negative");
        }
        
        if (timestamp <= 0) {
            throw new IllegalStateException("Transaction timestamp must be positive");
        }
        
        if (balanceAfter < EconomyConfig.MIN_PLAYER_BALANCE) {
            throw new IllegalStateException("Balance after transaction is below minimum");
        }
        
        if (balanceAfter > EconomyConfig.MAX_PLAYER_BALANCE) {
            throw new IllegalStateException("Balance after transaction exceeds maximum");
        }
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TransactionRecord that = (TransactionRecord) o;
        return Objects.equals(transactionId, that.transactionId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(transactionId);
    }
    
    @Override
    public String toString() {
        return "TransactionRecord{" +
                "transactionId='" + transactionId + '\'' +
                ", type=" + type +
                ", amount=" + amount +
                ", timestamp=" + timestamp +
                '}';
    }
}
