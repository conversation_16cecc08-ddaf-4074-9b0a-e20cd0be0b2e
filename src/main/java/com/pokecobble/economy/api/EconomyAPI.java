package com.pokecobble.economy.api;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.PlayerEconomyData;
import com.pokecobble.economy.core.TransactionRecord;
import com.pokecobble.economy.manager.EconomyManager;
import com.pokecobble.economy.transaction.TransactionRequest;
import com.pokecobble.economy.transaction.TransactionResult;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Public API for the economy system.
 * Provides a clean interface for other mods and plugins to interact with the economy.
 */
public class EconomyAPI {
    
    private static EconomyAPI instance;
    private final EconomyManager economyManager;
    
    /**
     * Private constructor for singleton pattern.
     */
    private EconomyAPI() {
        this.economyManager = EconomyManager.getInstance();
    }
    
    /**
     * Gets the singleton instance of the economy API.
     * 
     * @return The economy API instance
     */
    public static EconomyAPI getInstance() {
        if (instance == null) {
            instance = new EconomyAPI();
        }
        return instance;
    }
    
    /**
     * Checks if the economy system is available and initialized.
     *
     * @return true if available, false otherwise
     */
    public boolean isAvailable() {
        // Check if we're on the client side
        if (net.fabricmc.api.EnvType.CLIENT.equals(net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType())) {
            // On client side, economy is available if we can communicate with server
            // The ClientEconomyManager handles the actual data caching
            boolean available = true; // Client-side is always "available" for API calls
            Pokecobbleclaim.LOGGER.debug("EconomyAPI: isAvailable() = " + available + " (client-side)");
            return available;
        } else {
            // On server side, check if the manager is initialized
            boolean available = economyManager != null && economyManager.isInitialized();
            Pokecobbleclaim.LOGGER.debug("EconomyAPI: isAvailable() = " + available +
                                       " (server-side, manager: " + (economyManager != null) +
                                       ", initialized: " + (economyManager != null ? economyManager.isInitialized() : "N/A") + ")");
            return available;
        }
    }
    
    /**
     * Gets a player's current balance.
     *
     * @param playerId The player's UUID
     * @return The player's balance, or 0 if player not found
     */
    public long getBalance(UUID playerId) {
        if (!isAvailable()) {
            return 0L;
        }

        try {
            // Check if we're on the client side
            if (net.fabricmc.api.EnvType.CLIENT.equals(net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType())) {
                // Use client-side cache
                return com.pokecobble.economy.client.ClientEconomyManager.getInstance().getBalance(playerId);
            } else {
                // Use server-side manager
                return economyManager.getBalance(playerId);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error getting balance for player " + playerId, e);
            return 0L;
        }
    }
    
    /**
     * Gets a player's current balance.
     * 
     * @param player The server player entity
     * @return The player's balance, or 0 if player not found
     */
    public long getBalance(ServerPlayerEntity player) {
        if (player == null) {
            return 0L;
        }
        return getBalance(player.getUuid());
    }
    
    /**
     * Sets a player's balance.
     * 
     * @param playerId The player's UUID
     * @param amount The new balance amount
     * @return true if successful, false otherwise
     */
    public boolean setBalance(UUID playerId, long amount) {
        return setBalance(playerId, amount, "Balance set via API");
    }
    
    /**
     * Sets a player's balance with a description.
     * 
     * @param playerId The player's UUID
     * @param amount The new balance amount
     * @param description The transaction description
     * @return true if successful, false otherwise
     */
    public boolean setBalance(UUID playerId, long amount, String description) {
        if (!isAvailable()) {
            return false;
        }
        
        try {
            return economyManager.setBalance(playerId, amount, description);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error setting balance for player " + playerId, e);
            return false;
        }
    }
    
    /**
     * Sets a player's balance.
     * 
     * @param player The server player entity
     * @param amount The new balance amount
     * @return true if successful, false otherwise
     */
    public boolean setBalance(ServerPlayerEntity player, long amount) {
        if (player == null) {
            return false;
        }
        return setBalance(player.getUuid(), amount);
    }
    
    /**
     * Adds money to a player's balance.
     * 
     * @param playerId The player's UUID
     * @param amount The amount to add
     * @return true if successful, false otherwise
     */
    public boolean addMoney(UUID playerId, long amount) {
        return addMoney(playerId, amount, "Money added via API");
    }
    
    /**
     * Adds money to a player's balance with a description.
     * 
     * @param playerId The player's UUID
     * @param amount The amount to add
     * @param description The transaction description
     * @return true if successful, false otherwise
     */
    public boolean addMoney(UUID playerId, long amount, String description) {
        if (!isAvailable() || amount <= 0) {
            return false;
        }
        
        try {
            return economyManager.addBalance(playerId, amount, description);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error adding money for player " + playerId, e);
            return false;
        }
    }
    
    /**
     * Adds money to a player's balance.
     * 
     * @param player The server player entity
     * @param amount The amount to add
     * @return true if successful, false otherwise
     */
    public boolean addMoney(ServerPlayerEntity player, long amount) {
        if (player == null) {
            return false;
        }
        return addMoney(player.getUuid(), amount);
    }
    
    /**
     * Subtracts money from a player's balance.
     * 
     * @param playerId The player's UUID
     * @param amount The amount to subtract
     * @return true if successful, false otherwise
     */
    public boolean subtractMoney(UUID playerId, long amount) {
        return subtractMoney(playerId, amount, "Money subtracted via API");
    }
    
    /**
     * Subtracts money from a player's balance with a description.
     * 
     * @param playerId The player's UUID
     * @param amount The amount to subtract
     * @param description The transaction description
     * @return true if successful, false otherwise
     */
    public boolean subtractMoney(UUID playerId, long amount, String description) {
        if (!isAvailable() || amount <= 0) {
            return false;
        }
        
        try {
            return economyManager.subtractBalance(playerId, amount, description);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error subtracting money for player " + playerId, e);
            return false;
        }
    }
    
    /**
     * Subtracts money from a player's balance.
     * 
     * @param player The server player entity
     * @param amount The amount to subtract
     * @return true if successful, false otherwise
     */
    public boolean subtractMoney(ServerPlayerEntity player, long amount) {
        if (player == null) {
            return false;
        }
        return subtractMoney(player.getUuid(), amount);
    }
    
    /**
     * Transfers money between two players.
     * 
     * @param fromPlayerId The source player's UUID
     * @param toPlayerId The target player's UUID
     * @param amount The amount to transfer
     * @return true if successful, false otherwise
     */
    public boolean transferMoney(UUID fromPlayerId, UUID toPlayerId, long amount) {
        return transferMoney(fromPlayerId, toPlayerId, amount, "Money transfer via API");
    }
    
    /**
     * Transfers money between two players with a description.
     * 
     * @param fromPlayerId The source player's UUID
     * @param toPlayerId The target player's UUID
     * @param amount The amount to transfer
     * @param description The transaction description
     * @return true if successful, false otherwise
     */
    public boolean transferMoney(UUID fromPlayerId, UUID toPlayerId, long amount, String description) {
        if (!isAvailable() || amount <= 0 || fromPlayerId.equals(toPlayerId)) {
            return false;
        }
        
        try {
            TransactionRequest request = TransactionRequest.createTransfer(fromPlayerId, toPlayerId, amount, description);
            TransactionResult result = economyManager.getTransactionManager().executeTransaction(request);
            return result.isSuccess();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error transferring money from " + fromPlayerId + " to " + toPlayerId, e);
            return false;
        }
    }
    
    /**
     * Transfers money between two players.
     * 
     * @param fromPlayer The source player
     * @param toPlayer The target player
     * @param amount The amount to transfer
     * @return true if successful, false otherwise
     */
    public boolean transferMoney(ServerPlayerEntity fromPlayer, ServerPlayerEntity toPlayer, long amount) {
        if (fromPlayer == null || toPlayer == null) {
            return false;
        }
        return transferMoney(fromPlayer.getUuid(), toPlayer.getUuid(), amount);
    }
    
    /**
     * Checks if a player has at least the specified amount of money.
     * 
     * @param playerId The player's UUID
     * @param amount The amount to check
     * @return true if player has enough money, false otherwise
     */
    public boolean hasEnoughMoney(UUID playerId, long amount) {
        return getBalance(playerId) >= amount;
    }
    
    /**
     * Checks if a player has at least the specified amount of money.
     * 
     * @param player The server player entity
     * @param amount The amount to check
     * @return true if player has enough money, false otherwise
     */
    public boolean hasEnoughMoney(ServerPlayerEntity player, long amount) {
        if (player == null) {
            return false;
        }
        return hasEnoughMoney(player.getUuid(), amount);
    }
    
    /**
     * Gets a player's economy data.
     *
     * @param playerId The player's UUID
     * @return The player's economy data, or null if not found
     */
    public PlayerEconomyData getPlayerData(UUID playerId) {
        if (!isAvailable()) {
            return null;
        }

        try {
            // Check if we're on the client side
            if (net.fabricmc.api.EnvType.CLIENT.equals(net.fabricmc.loader.api.FabricLoader.getInstance().getEnvironmentType())) {
                // Use client-side cache
                return com.pokecobble.economy.client.ClientEconomyManager.getInstance().getPlayerData(playerId);
            } else {
                // Use server-side manager
                return economyManager.getPlayerData(playerId);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error getting player data for " + playerId, e);
            return null;
        }
    }
    
    /**
     * Gets a player's transaction history.
     * 
     * @param playerId The player's UUID
     * @return The player's recent transactions, or empty list if not found
     */
    public List<TransactionRecord> getTransactionHistory(UUID playerId) {
        PlayerEconomyData playerData = getPlayerData(playerId);
        if (playerData != null) {
            return playerData.getRecentTransactions();
        }
        return List.of();
    }
    
    /**
     * Checks if a player exists in the economy system.
     * 
     * @param playerId The player's UUID
     * @return true if player exists, false otherwise
     */
    public boolean playerExists(UUID playerId) {
        if (!isAvailable()) {
            return false;
        }
        
        return economyManager.getDataStorage().playerDataExists(playerId);
    }
    
    /**
     * Creates economy data for a new player.
     * 
     * @param playerId The player's UUID
     * @param playerName The player's name
     * @return true if successful, false otherwise
     */
    public boolean createPlayer(UUID playerId, String playerName) {
        if (!isAvailable()) {
            return false;
        }
        
        try {
            PlayerEconomyData data = economyManager.createPlayerData(playerId, playerName);
            return data != null;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error creating player data for " + playerId, e);
            return false;
        }
    }
    
    /**
     * Executes a transaction asynchronously.
     * 
     * @param request The transaction request
     * @return A CompletableFuture containing the transaction result
     */
    public CompletableFuture<TransactionResult> executeTransactionAsync(TransactionRequest request) {
        if (!isAvailable()) {
            return CompletableFuture.completedFuture(TransactionResult.failure("Economy system not available"));
        }
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return economyManager.getTransactionManager().executeTransaction(request);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error executing async transaction", e);
                return TransactionResult.failure("Transaction execution error: " + e.getMessage());
            }
        });
    }
    
    /**
     * Gets the total amount of money in the economy.
     * 
     * @return The total money amount
     */
    public long getTotalMoney() {
        // This would require iterating through all player data
        // For now, return -1 to indicate not implemented
        return -1L;
    }
    
    /**
     * Gets economy system statistics.
     * 
     * @return A map of statistics
     */
    public java.util.Map<String, Object> getStatistics() {
        if (!isAvailable()) {
            return java.util.Map.of("available", false);
        }
        
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("available", true);
        stats.put("initialized", economyManager.isInitialized());
        
        // Add component statistics
        if (economyManager.getSecurityManager() != null) {
            stats.put("security", economyManager.getSecurityManager().getSecurityStatistics());
        }
        
        if (economyManager.getNetworkManager() != null) {
            stats.put("network", economyManager.getNetworkManager().getStatistics());
        }
        
        return stats;
    }
    
    /**
     * Formats a money amount for display.
     * 
     * @param amount The amount to format
     * @return The formatted amount string
     */
    public String formatMoney(long amount) {
        if (amount >= 1_000_000_000L) {
            return String.format("%.1fB", amount / 1_000_000_000.0);
        } else if (amount >= 1_000_000L) {
            return String.format("%.1fM", amount / 1_000_000.0);
        } else if (amount >= 1_000L) {
            return String.format("%.1fK", amount / 1_000.0);
        } else {
            return String.valueOf(amount);
        }
    }
    
    /**
     * Gets the currency symbol or name.
     * 
     * @return The currency symbol
     */
    public String getCurrencySymbol() {
        return "$"; // This could be configurable
    }
    
    /**
     * Gets the currency name.
     * 
     * @return The currency name
     */
    public String getCurrencyName() {
        return "Coins"; // This could be configurable
    }
}
