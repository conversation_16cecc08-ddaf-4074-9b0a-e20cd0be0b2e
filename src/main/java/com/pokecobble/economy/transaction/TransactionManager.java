package com.pokecobble.economy.transaction;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.EconomyConfig;
import com.pokecobble.economy.core.PlayerEconomyData;
import com.pokecobble.economy.core.TransactionRecord;
import com.pokecobble.economy.manager.EconomyManager;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Manages all transaction operations including logging, validation, and rollback mechanisms.
 * Ensures transaction integrity and prevents money duplication exploits.
 */
public class TransactionManager {
    
    private static final DateTimeFormatter LOG_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final EconomyManager economyManager;
    private final ExecutorService transactionExecutor;
    private final ReadWriteLock transactionLock;
    private final TransactionValidator validator;
    private final DuplicateDetector duplicateDetector;
    private final RateLimiter rateLimiter;
    
    // Transaction logging
    private Path transactionLogPath;
    private final Map<String, TransactionLogWriter> logWriters;
    
    // Pending transactions for rollback capability
    private final Map<UUID, PendingTransaction> pendingTransactions;
    
    // Transaction history for duplicate detection
    private final Map<UUID, List<TransactionRecord>> recentTransactions;
    
    private boolean initialized;
    
    /**
     * Creates a new transaction manager.
     * 
     * @param economyManager The economy manager instance
     */
    public TransactionManager(EconomyManager economyManager) {
        this.economyManager = economyManager;
        this.transactionExecutor = Executors.newFixedThreadPool(EconomyConfig.NETWORK_THREAD_POOL_SIZE);
        this.transactionLock = new ReentrantReadWriteLock();
        this.validator = new TransactionValidator();
        this.duplicateDetector = new DuplicateDetector();
        this.rateLimiter = new RateLimiter();
        this.logWriters = new ConcurrentHashMap<>();
        this.pendingTransactions = new ConcurrentHashMap<>();
        this.recentTransactions = new ConcurrentHashMap<>();
        this.initialized = false;
    }
    
    /**
     * Initializes the transaction manager.
     */
    public void initialize() {
        if (initialized) {
            return;
        }
        
        try {
            // Setup transaction log directory
            setupTransactionLogging();
            
            // Initialize components
            validator.initialize();
            duplicateDetector.initialize();
            rateLimiter.initialize();
            
            // Start cleanup scheduler
            startCleanupScheduler();
            
            this.initialized = true;
            Pokecobbleclaim.LOGGER.info("Transaction manager initialized successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize transaction manager", e);
            throw new RuntimeException("Transaction manager initialization failed", e);
        }
    }
    
    /**
     * Shuts down the transaction manager.
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        try {
            // Shutdown executor
            transactionExecutor.shutdown();
            if (!transactionExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                transactionExecutor.shutdownNow();
            }
            
            // Close all log writers
            for (TransactionLogWriter writer : logWriters.values()) {
                writer.close();
            }
            logWriters.clear();
            
            // Clear pending transactions
            pendingTransactions.clear();
            recentTransactions.clear();
            
            this.initialized = false;
            Pokecobbleclaim.LOGGER.info("Transaction manager shut down successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during transaction manager shutdown", e);
        }
    }
    
    /**
     * Executes a transaction with full validation and logging.
     * 
     * @param transaction The transaction to execute
     * @return The result of the transaction
     */
    public TransactionResult executeTransaction(TransactionRequest request) {
        if (!initialized) {
            return TransactionResult.failure("Transaction manager not initialized");
        }
        
        transactionLock.writeLock().lock();
        try {
            // 1. Validate the transaction request
            ValidationResult validation = validator.validateTransaction(request);
            if (!validation.isValid()) {
                return TransactionResult.failure("Validation failed: " + validation.getErrorMessage());
            }
            
            // 2. Check rate limits
            if (!rateLimiter.checkRateLimit(request.getFromPlayer())) {
                return TransactionResult.failure("Rate limit exceeded");
            }
            
            // 3. Check for duplicates
            if (duplicateDetector.isDuplicate(request)) {
                return TransactionResult.failure("Duplicate transaction detected");
            }
            
            // 4. Create pending transaction for rollback capability
            PendingTransaction pending = new PendingTransaction(request);
            pendingTransactions.put(pending.getTransactionId(), pending);
            
            try {
                // 5. Execute the actual transaction
                TransactionResult result = performTransaction(request, pending);
                
                if (result.isSuccess()) {
                    // 6. Log successful transaction
                    logTransaction(pending.getTransactionRecord());
                    
                    // 7. Update recent transactions for duplicate detection
                    updateRecentTransactions(pending.getTransactionRecord());
                    
                    // 8. Remove from pending
                    pendingTransactions.remove(pending.getTransactionId());
                    
                    Pokecobbleclaim.LOGGER.debug("Transaction executed successfully: " + pending.getTransactionId());
                } else {
                    // Rollback if failed
                    rollbackTransaction(pending);
                }
                
                return result;
                
            } catch (Exception e) {
                // Rollback on exception
                rollbackTransaction(pending);
                Pokecobbleclaim.LOGGER.error("Transaction execution failed", e);
                return TransactionResult.failure("Transaction execution error: " + e.getMessage());
            }
            
        } finally {
            transactionLock.writeLock().unlock();
        }
    }
    
    /**
     * Performs the actual transaction operation.
     * 
     * @param request The transaction request
     * @param pending The pending transaction
     * @return The transaction result
     */
    private TransactionResult performTransaction(TransactionRequest request, PendingTransaction pending) {
        try {
            UUID fromPlayer = request.getFromPlayer();
            UUID toPlayer = request.getToPlayer();
            long amount = request.getAmount();
            String description = request.getDescription();
            
            // Handle different transaction types
            switch (request.getType()) {
                case TRANSFER_SEND:
                    return performTransfer(fromPlayer, toPlayer, amount, description, pending);
                    
                case DEPOSIT:
                    return performDeposit(toPlayer, amount, description, pending);
                    
                case WITHDRAWAL:
                    return performWithdrawal(fromPlayer, amount, description, pending);
                    
                case ADMIN_ADD:
                    return performAdminAdd(toPlayer, amount, description, pending);
                    
                case ADMIN_REMOVE:
                    return performAdminRemove(fromPlayer, amount, description, pending);
                    
                default:
                    return TransactionResult.failure("Unsupported transaction type: " + request.getType());
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error performing transaction", e);
            return TransactionResult.failure("Transaction error: " + e.getMessage());
        }
    }
    
    /**
     * Performs a transfer between two players.
     */
    private TransactionResult performTransfer(UUID fromPlayer, UUID toPlayer, long amount, 
                                            String description, PendingTransaction pending) {
        // Get player data
        PlayerEconomyData fromData = economyManager.getPlayerData(fromPlayer);
        PlayerEconomyData toData = economyManager.getPlayerData(toPlayer);
        
        if (fromData == null) {
            return TransactionResult.failure("Source player not found");
        }
        if (toData == null) {
            return TransactionResult.failure("Target player not found");
        }
        
        // Check if source has sufficient balance
        if (fromData.getBalance() < amount) {
            return TransactionResult.failure("Insufficient balance");
        }
        
        // Check security flags
        if (fromData.getSecurityFlags().isFrozen() || toData.getSecurityFlags().isFrozen()) {
            return TransactionResult.failure("Account frozen");
        }
        
        // Store original balances for rollback
        pending.setOriginalFromBalance(fromData.getBalance());
        pending.setOriginalToBalance(toData.getBalance());
        
        try {
            // Perform the transfer
            fromData.subtractBalance(amount);
            toData.addBalance(amount);
            
            // Create transaction records
            TransactionRecord sendRecord = new TransactionRecord(
                TransactionRecord.TransactionType.TRANSFER_SEND, amount, fromPlayer, toPlayer,
                description, pending.getOriginalFromBalance(), fromData.getBalance()
            );
            
            TransactionRecord receiveRecord = new TransactionRecord(
                TransactionRecord.TransactionType.TRANSFER_RECEIVE, amount, fromPlayer, toPlayer,
                description, pending.getOriginalToBalance(), toData.getBalance()
            );
            
            // Add transactions to player data
            fromData.addTransaction(sendRecord);
            toData.addTransaction(receiveRecord);
            
            // Save data
            economyManager.getDataStorage().savePlayerDataSync(fromData);
            economyManager.getDataStorage().savePlayerDataSync(toData);
            
            // Store transaction record in pending
            pending.setTransactionRecord(sendRecord);
            pending.setSecondaryTransactionRecord(receiveRecord);
            
            return TransactionResult.success("Transfer completed successfully", sendRecord);
            
        } catch (Exception e) {
            return TransactionResult.failure("Transfer execution failed: " + e.getMessage());
        }
    }
    
    /**
     * Performs a deposit operation.
     */
    private TransactionResult performDeposit(UUID playerId, long amount, String description, 
                                           PendingTransaction pending) {
        PlayerEconomyData playerData = economyManager.getPlayerData(playerId);
        if (playerData == null) {
            return TransactionResult.failure("Player not found");
        }
        
        if (playerData.getSecurityFlags().isFrozen()) {
            return TransactionResult.failure("Account frozen");
        }
        
        pending.setOriginalToBalance(playerData.getBalance());
        
        try {
            playerData.addBalance(amount);
            
            TransactionRecord record = new TransactionRecord(
                TransactionRecord.TransactionType.DEPOSIT, amount, null, playerId,
                description, pending.getOriginalToBalance(), playerData.getBalance()
            );
            
            playerData.addTransaction(record);
            economyManager.getDataStorage().savePlayerDataSync(playerData);
            
            pending.setTransactionRecord(record);
            
            return TransactionResult.success("Deposit completed successfully", record);
            
        } catch (Exception e) {
            return TransactionResult.failure("Deposit execution failed: " + e.getMessage());
        }
    }
    
    /**
     * Performs a withdrawal operation.
     */
    private TransactionResult performWithdrawal(UUID playerId, long amount, String description, 
                                              PendingTransaction pending) {
        PlayerEconomyData playerData = economyManager.getPlayerData(playerId);
        if (playerData == null) {
            return TransactionResult.failure("Player not found");
        }
        
        if (playerData.getBalance() < amount) {
            return TransactionResult.failure("Insufficient balance");
        }
        
        if (playerData.getSecurityFlags().isFrozen()) {
            return TransactionResult.failure("Account frozen");
        }
        
        pending.setOriginalFromBalance(playerData.getBalance());
        
        try {
            playerData.subtractBalance(amount);
            
            TransactionRecord record = new TransactionRecord(
                TransactionRecord.TransactionType.WITHDRAWAL, amount, playerId, null,
                description, pending.getOriginalFromBalance(), playerData.getBalance()
            );
            
            playerData.addTransaction(record);
            economyManager.getDataStorage().savePlayerDataSync(playerData);
            
            pending.setTransactionRecord(record);
            
            return TransactionResult.success("Withdrawal completed successfully", record);
            
        } catch (Exception e) {
            return TransactionResult.failure("Withdrawal execution failed: " + e.getMessage());
        }
    }
    
    /**
     * Performs an admin add operation.
     */
    private TransactionResult performAdminAdd(UUID playerId, long amount, String description, 
                                            PendingTransaction pending) {
        PlayerEconomyData playerData = economyManager.getPlayerData(playerId);
        if (playerData == null) {
            return TransactionResult.failure("Player not found");
        }
        
        pending.setOriginalToBalance(playerData.getBalance());
        
        try {
            playerData.addBalance(amount);
            
            TransactionRecord record = new TransactionRecord(
                TransactionRecord.TransactionType.ADMIN_ADD, amount, null, playerId,
                description, pending.getOriginalToBalance(), playerData.getBalance()
            );
            
            playerData.addTransaction(record);
            economyManager.getDataStorage().savePlayerDataSync(playerData);
            
            pending.setTransactionRecord(record);
            
            return TransactionResult.success("Admin add completed successfully", record);
            
        } catch (Exception e) {
            return TransactionResult.failure("Admin add execution failed: " + e.getMessage());
        }
    }
    
    /**
     * Performs an admin remove operation.
     */
    private TransactionResult performAdminRemove(UUID playerId, long amount, String description, 
                                               PendingTransaction pending) {
        PlayerEconomyData playerData = economyManager.getPlayerData(playerId);
        if (playerData == null) {
            return TransactionResult.failure("Player not found");
        }
        
        if (playerData.getBalance() < amount) {
            return TransactionResult.failure("Insufficient balance");
        }
        
        pending.setOriginalFromBalance(playerData.getBalance());
        
        try {
            playerData.subtractBalance(amount);
            
            TransactionRecord record = new TransactionRecord(
                TransactionRecord.TransactionType.ADMIN_REMOVE, amount, playerId, null,
                description, pending.getOriginalFromBalance(), playerData.getBalance()
            );
            
            playerData.addTransaction(record);
            economyManager.getDataStorage().savePlayerDataSync(playerData);
            
            pending.setTransactionRecord(record);
            
            return TransactionResult.success("Admin remove completed successfully", record);
            
        } catch (Exception e) {
            return TransactionResult.failure("Admin remove execution failed: " + e.getMessage());
        }
    }
    
    /**
     * Rolls back a failed transaction.
     * 
     * @param pending The pending transaction to rollback
     */
    private void rollbackTransaction(PendingTransaction pending) {
        try {
            TransactionRequest request = pending.getRequest();
            
            // Restore original balances based on transaction type
            switch (request.getType()) {
                case TRANSFER_SEND:
                    rollbackTransfer(pending);
                    break;
                    
                case DEPOSIT:
                case ADMIN_ADD:
                    rollbackDeposit(pending);
                    break;
                    
                case WITHDRAWAL:
                case ADMIN_REMOVE:
                    rollbackWithdrawal(pending);
                    break;
            }
            
            // Remove from pending transactions
            pendingTransactions.remove(pending.getTransactionId());
            
            Pokecobbleclaim.LOGGER.info("Transaction rolled back: " + pending.getTransactionId());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during transaction rollback", e);
        }
    }
    
    /**
     * Rolls back a transfer transaction.
     */
    private void rollbackTransfer(PendingTransaction pending) {
        TransactionRequest request = pending.getRequest();
        
        PlayerEconomyData fromData = economyManager.getPlayerData(request.getFromPlayer());
        PlayerEconomyData toData = economyManager.getPlayerData(request.getToPlayer());
        
        if (fromData != null && pending.getOriginalFromBalance() >= 0) {
            fromData.setBalance(pending.getOriginalFromBalance());
            economyManager.getDataStorage().savePlayerDataSync(fromData);
        }
        
        if (toData != null && pending.getOriginalToBalance() >= 0) {
            toData.setBalance(pending.getOriginalToBalance());
            economyManager.getDataStorage().savePlayerDataSync(toData);
        }
    }
    
    /**
     * Rolls back a deposit transaction.
     */
    private void rollbackDeposit(PendingTransaction pending) {
        TransactionRequest request = pending.getRequest();
        
        PlayerEconomyData playerData = economyManager.getPlayerData(request.getToPlayer());
        if (playerData != null && pending.getOriginalToBalance() >= 0) {
            playerData.setBalance(pending.getOriginalToBalance());
            economyManager.getDataStorage().savePlayerDataSync(playerData);
        }
    }
    
    /**
     * Rolls back a withdrawal transaction.
     */
    private void rollbackWithdrawal(PendingTransaction pending) {
        TransactionRequest request = pending.getRequest();
        
        PlayerEconomyData playerData = economyManager.getPlayerData(request.getFromPlayer());
        if (playerData != null && pending.getOriginalFromBalance() >= 0) {
            playerData.setBalance(pending.getOriginalFromBalance());
            economyManager.getDataStorage().savePlayerDataSync(playerData);
        }
    }
    
    /**
     * Logs a transaction to the transaction log.
     * 
     * @param transaction The transaction to log
     */
    public void logTransaction(TransactionRecord transaction) {
        if (!EconomyConfig.ENABLE_TRANSACTION_LOGGING) {
            return;
        }
        
        CompletableFuture.runAsync(() -> {
            try {
                String logDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                TransactionLogWriter writer = getOrCreateLogWriter(logDate);
                writer.writeTransaction(transaction);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to log transaction", e);
            }
        }, transactionExecutor);
    }
    
    /**
     * Updates recent transactions for duplicate detection.
     */
    private void updateRecentTransactions(TransactionRecord transaction) {
        UUID playerId = transaction.getFromPlayer();
        if (playerId == null) {
            playerId = transaction.getToPlayer();
        }
        
        if (playerId != null) {
            recentTransactions.computeIfAbsent(playerId, k -> new ArrayList<>()).add(transaction);
            
            // Keep only recent transactions
            List<TransactionRecord> playerTransactions = recentTransactions.get(playerId);
            long cutoffTime = System.currentTimeMillis() - EconomyConfig.DUPLICATE_DETECTION_WINDOW_MS;
            playerTransactions.removeIf(t -> t.getTimestamp() < cutoffTime);
        }
    }
    
    /**
     * Gets or creates a transaction log writer for a specific date.
     */
    private TransactionLogWriter getOrCreateLogWriter(String logDate) throws IOException {
        return logWriters.computeIfAbsent(logDate, date -> {
            try {
                String fileName = "transactions_" + date + EconomyConfig.TRANSACTION_LOG_EXTENSION;
                Path logFile = transactionLogPath.resolve(fileName);
                return new TransactionLogWriter(logFile);
            } catch (IOException e) {
                throw new RuntimeException("Failed to create log writer", e);
            }
        });
    }
    
    /**
     * Sets up transaction logging directory and files.
     */
    private void setupTransactionLogging() throws IOException {
        if (!EconomyConfig.ENABLE_TRANSACTION_LOGGING) {
            return;
        }
        
        Path economyDataPath = Paths.get(".").resolve(EconomyConfig.ECONOMY_DATA_FOLDER);
        transactionLogPath = economyDataPath.resolve(EconomyConfig.TRANSACTION_LOG_FOLDER);
        Files.createDirectories(transactionLogPath);
        
        Pokecobbleclaim.LOGGER.info("Transaction logging initialized at: " + transactionLogPath.toAbsolutePath());
    }
    
    /**
     * Starts the cleanup scheduler for removing old data.
     */
    private void startCleanupScheduler() {
        ScheduledExecutorService cleanupScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "TransactionCleanupScheduler");
            t.setDaemon(true);
            return t;
        });
        
        cleanupScheduler.scheduleAtFixedRate(this::performCleanup, 1, 1, TimeUnit.HOURS);
    }
    
    /**
     * Performs cleanup of old transaction data.
     */
    private void performCleanup() {
        try {
            // Clean up old pending transactions (should not happen in normal operation)
            long cutoffTime = System.currentTimeMillis() - 300000; // 5 minutes
            pendingTransactions.entrySet().removeIf(entry -> 
                entry.getValue().getCreationTime() < cutoffTime);
            
            // Clean up old recent transactions
            long recentCutoffTime = System.currentTimeMillis() - EconomyConfig.DUPLICATE_DETECTION_WINDOW_MS;
            recentTransactions.values().forEach(list -> 
                list.removeIf(t -> t.getTimestamp() < recentCutoffTime));
            
            // Remove empty lists
            recentTransactions.entrySet().removeIf(entry -> entry.getValue().isEmpty());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during transaction cleanup", e);
        }
    }
    
    /**
     * Gets the transaction validator.
     * 
     * @return The validator
     */
    public TransactionValidator getValidator() {
        return validator;
    }
    
    /**
     * Gets the duplicate detector.
     * 
     * @return The duplicate detector
     */
    public DuplicateDetector getDuplicateDetector() {
        return duplicateDetector;
    }
    
    /**
     * Gets the rate limiter.
     * 
     * @return The rate limiter
     */
    public RateLimiter getRateLimiter() {
        return rateLimiter;
    }
    
    /**
     * Checks if the transaction manager is initialized.
     * 
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}
