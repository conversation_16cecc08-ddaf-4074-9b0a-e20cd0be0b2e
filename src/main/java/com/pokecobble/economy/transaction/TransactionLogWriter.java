package com.pokecobble.economy.transaction;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.EconomyConfig;
import com.pokecobble.economy.core.TransactionRecord;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Writes transaction records to log files for auditing and debugging.
 */
public class TransactionLogWriter implements AutoCloseable {
    
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    private final Path logFile;
    private final ReentrantLock writeLock;
    private BufferedWriter writer;
    private long currentFileSize;
    private boolean closed;
    
    /**
     * Creates a new transaction log writer.
     * 
     * @param logFile The log file path
     * @throws IOException if the log file cannot be created
     */
    public TransactionLogWriter(Path logFile) throws IOException {
        this.logFile = logFile;
        this.writeLock = new ReentrantLock();
        this.closed = false;
        
        // Create parent directories if they don't exist
        Files.createDirectories(logFile.getParent());
        
        // Initialize the writer
        initializeWriter();
        
        Pokecobbleclaim.LOGGER.debug("Transaction log writer created for: " + logFile);
    }
    
    /**
     * Initializes the buffered writer.
     * 
     * @throws IOException if the writer cannot be initialized
     */
    private void initializeWriter() throws IOException {
        // Check current file size
        if (Files.exists(logFile)) {
            currentFileSize = Files.size(logFile);
        } else {
            currentFileSize = 0;
        }
        
        // Create writer with append mode
        writer = Files.newBufferedWriter(logFile, 
                StandardOpenOption.CREATE, 
                StandardOpenOption.APPEND);
        
        // Write header if this is a new file
        if (currentFileSize == 0) {
            writeHeader();
        }
    }
    
    /**
     * Writes a header to the log file.
     * 
     * @throws IOException if writing fails
     */
    private void writeHeader() throws IOException {
        writer.write("# Transaction Log - Created: " + LocalDateTime.now().format(TIMESTAMP_FORMAT));
        writer.newLine();
        writer.write("# Format: TIMESTAMP | TYPE | AMOUNT | FROM_PLAYER | TO_PLAYER | DESCRIPTION | BALANCE_BEFORE | BALANCE_AFTER | TRANSACTION_ID");
        writer.newLine();
        writer.write("# ================================================================================");
        writer.newLine();
        writer.flush();
        
        currentFileSize += 200; // Approximate header size
    }
    
    /**
     * Writes a transaction record to the log.
     * 
     * @param transaction The transaction to log
     * @throws IOException if writing fails
     */
    public void writeTransaction(TransactionRecord transaction) throws IOException {
        if (closed) {
            throw new IOException("Log writer is closed");
        }
        
        writeLock.lock();
        try {
            // Check if we need to rotate the log file
            if (shouldRotateLog()) {
                rotateLog();
            }
            
            // Format the transaction record
            String logEntry = formatTransaction(transaction);
            
            // Write to file
            writer.write(logEntry);
            writer.newLine();
            writer.flush();
            
            // Update file size estimate
            currentFileSize += logEntry.length() + System.lineSeparator().length();
            
        } finally {
            writeLock.unlock();
        }
    }
    
    /**
     * Formats a transaction record for logging.
     * 
     * @param transaction The transaction to format
     * @return The formatted log entry
     */
    private String formatTransaction(TransactionRecord transaction) {
        StringBuilder entry = new StringBuilder();
        
        // Timestamp
        LocalDateTime timestamp = LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(transaction.getTimestamp()),
                java.time.ZoneId.systemDefault()
        );
        entry.append(timestamp.format(TIMESTAMP_FORMAT));
        entry.append(" | ");
        
        // Transaction type
        entry.append(transaction.getType().name());
        entry.append(" | ");
        
        // Amount
        entry.append(transaction.getAmount());
        entry.append(" | ");
        
        // From player
        entry.append(transaction.getFromPlayer() != null ? transaction.getFromPlayer().toString() : "NULL");
        entry.append(" | ");
        
        // To player
        entry.append(transaction.getToPlayer() != null ? transaction.getToPlayer().toString() : "NULL");
        entry.append(" | ");
        
        // Description (escape pipe characters)
        String description = transaction.getDescription();
        if (description != null) {
            description = description.replace("|", "\\|").replace("\n", "\\n").replace("\r", "\\r");
        } else {
            description = "NULL";
        }
        entry.append(description);
        entry.append(" | ");
        
        // Balance before
        entry.append(transaction.getBalanceBefore());
        entry.append(" | ");
        
        // Balance after
        entry.append(transaction.getBalanceAfter());
        entry.append(" | ");
        
        // Transaction ID
        entry.append(transaction.getTransactionId().toString());
        
        return entry.toString();
    }
    
    /**
     * Checks if the log file should be rotated.
     * 
     * @return true if rotation is needed, false otherwise
     */
    private boolean shouldRotateLog() {
        long maxSizeBytes = EconomyConfig.MAX_LOG_FILE_SIZE_MB * 1024L * 1024L;
        return currentFileSize >= maxSizeBytes;
    }
    
    /**
     * Rotates the current log file.
     * 
     * @throws IOException if rotation fails
     */
    private void rotateLog() throws IOException {
        // Close current writer
        if (writer != null) {
            writer.close();
        }
        
        // Find next available rotation number
        int rotationNumber = 1;
        Path rotatedFile;
        do {
            String rotatedFileName = logFile.getFileName().toString() + "." + rotationNumber;
            rotatedFile = logFile.getParent().resolve(rotatedFileName);
            rotationNumber++;
        } while (Files.exists(rotatedFile) && rotationNumber <= EconomyConfig.MAX_LOG_FILES);
        
        // If we've reached the maximum number of log files, delete the oldest
        if (rotationNumber > EconomyConfig.MAX_LOG_FILES) {
            deleteOldestLogFile();
            rotationNumber = EconomyConfig.MAX_LOG_FILES;
            String rotatedFileName = logFile.getFileName().toString() + "." + rotationNumber;
            rotatedFile = logFile.getParent().resolve(rotatedFileName);
        }
        
        // Move current log file to rotated name
        try {
            Files.move(logFile, rotatedFile);
            Pokecobbleclaim.LOGGER.info("Rotated transaction log: " + rotatedFile.getFileName());
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to rotate log file", e);
            // Continue with new file anyway
        }
        
        // Reset file size and reinitialize writer
        currentFileSize = 0;
        initializeWriter();
    }
    
    /**
     * Deletes the oldest log file to make room for rotation.
     */
    private void deleteOldestLogFile() {
        try {
            // Find and delete the highest numbered log file
            for (int i = EconomyConfig.MAX_LOG_FILES; i >= 1; i--) {
                String fileName = logFile.getFileName().toString() + "." + i;
                Path oldFile = logFile.getParent().resolve(fileName);
                if (Files.exists(oldFile)) {
                    Files.delete(oldFile);
                    Pokecobbleclaim.LOGGER.debug("Deleted old log file: " + fileName);
                    break;
                }
            }
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to delete old log file", e);
        }
    }
    
    /**
     * Forces a flush of any buffered data.
     * 
     * @throws IOException if flushing fails
     */
    public void flush() throws IOException {
        if (closed) {
            return;
        }
        
        writeLock.lock();
        try {
            if (writer != null) {
                writer.flush();
            }
        } finally {
            writeLock.unlock();
        }
    }
    
    /**
     * Gets the current log file path.
     * 
     * @return The log file path
     */
    public Path getLogFile() {
        return logFile;
    }
    
    /**
     * Gets the current estimated file size.
     * 
     * @return The file size in bytes
     */
    public long getCurrentFileSize() {
        return currentFileSize;
    }
    
    /**
     * Checks if the writer is closed.
     * 
     * @return true if closed, false otherwise
     */
    public boolean isClosed() {
        return closed;
    }
    
    @Override
    public void close() throws IOException {
        if (closed) {
            return;
        }
        
        writeLock.lock();
        try {
            if (writer != null) {
                writer.flush();
                writer.close();
                writer = null;
            }
            closed = true;
            Pokecobbleclaim.LOGGER.debug("Transaction log writer closed: " + logFile);
        } finally {
            writeLock.unlock();
        }
    }
}
