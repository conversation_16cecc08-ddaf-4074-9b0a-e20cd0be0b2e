package com.pokecobble.economy.transaction;

import com.pokecobble.economy.core.TransactionRecord;
import java.util.UUID;

/**
 * Represents a transaction that is currently being processed.
 * Used for rollback capabilities and transaction tracking.
 */
public class PendingTransaction {
    
    private final UUID transactionId;
    private final TransactionRequest request;
    private final long creationTime;
    
    private TransactionRecord transactionRecord;
    private TransactionRecord secondaryTransactionRecord; // For transfers
    
    private long originalFromBalance = -1;
    private long originalToBalance = -1;
    
    /**
     * Creates a new pending transaction.
     * 
     * @param request The transaction request
     */
    public PendingTransaction(TransactionRequest request) {
        this.transactionId = UUID.randomUUID();
        this.request = request;
        this.creationTime = System.currentTimeMillis();
    }
    
    /**
     * Gets the transaction ID.
     * 
     * @return The transaction ID
     */
    public UUID getTransactionId() {
        return transactionId;
    }
    
    /**
     * Gets the transaction request.
     * 
     * @return The request
     */
    public TransactionRequest getRequest() {
        return request;
    }
    
    /**
     * Gets the creation time.
     * 
     * @return The creation time
     */
    public long getCreationTime() {
        return creationTime;
    }
    
    /**
     * Gets the transaction record.
     * 
     * @return The transaction record
     */
    public TransactionRecord getTransactionRecord() {
        return transactionRecord;
    }
    
    /**
     * Sets the transaction record.
     * 
     * @param transactionRecord The transaction record
     */
    public void setTransactionRecord(TransactionRecord transactionRecord) {
        this.transactionRecord = transactionRecord;
    }
    
    /**
     * Gets the secondary transaction record (for transfers).
     * 
     * @return The secondary transaction record
     */
    public TransactionRecord getSecondaryTransactionRecord() {
        return secondaryTransactionRecord;
    }
    
    /**
     * Sets the secondary transaction record.
     * 
     * @param secondaryTransactionRecord The secondary transaction record
     */
    public void setSecondaryTransactionRecord(TransactionRecord secondaryTransactionRecord) {
        this.secondaryTransactionRecord = secondaryTransactionRecord;
    }
    
    /**
     * Gets the original balance of the source player.
     * 
     * @return The original balance, or -1 if not set
     */
    public long getOriginalFromBalance() {
        return originalFromBalance;
    }
    
    /**
     * Sets the original balance of the source player.
     * 
     * @param originalFromBalance The original balance
     */
    public void setOriginalFromBalance(long originalFromBalance) {
        this.originalFromBalance = originalFromBalance;
    }
    
    /**
     * Gets the original balance of the target player.
     * 
     * @return The original balance, or -1 if not set
     */
    public long getOriginalToBalance() {
        return originalToBalance;
    }
    
    /**
     * Sets the original balance of the target player.
     * 
     * @param originalToBalance The original balance
     */
    public void setOriginalToBalance(long originalToBalance) {
        this.originalToBalance = originalToBalance;
    }
    
    @Override
    public String toString() {
        return "PendingTransaction{" +
                "transactionId=" + transactionId +
                ", request=" + request +
                ", creationTime=" + creationTime +
                '}';
    }
}
