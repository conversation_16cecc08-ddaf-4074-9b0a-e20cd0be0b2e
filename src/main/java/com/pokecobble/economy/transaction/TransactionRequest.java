package com.pokecobble.economy.transaction;

import com.pokecobble.economy.core.TransactionRecord;
import java.util.Objects;
import java.util.UUID;

/**
 * Represents a transaction request with all necessary information.
 */
public class TransactionRequest {
    
    private final UUID requestId;
    private final TransactionRecord.TransactionType type;
    private final long amount;
    private final UUID fromPlayer;
    private final UUID toPlayer;
    private final String description;
    private final long timestamp;
    private final String source;
    private final String metadata;
    
    /**
     * Creates a new transaction request.
     * 
     * @param type The transaction type
     * @param amount The transaction amount
     * @param fromPlayer The source player (can be null)
     * @param toPlayer The target player (can be null)
     * @param description The transaction description
     */
    public TransactionRequest(TransactionRecord.TransactionType type, long amount, 
                            UUID fromPlayer, UUID toPlayer, String description) {
        this(type, amount, fromPlayer, toPlayer, description, "GAME", null);
    }
    
    /**
     * Creates a new transaction request with source and metadata.
     * 
     * @param type The transaction type
     * @param amount The transaction amount
     * @param fromPlayer The source player (can be null)
     * @param toPlayer The target player (can be null)
     * @param description The transaction description
     * @param source The transaction source
     * @param metadata Additional metadata
     */
    public TransactionRequest(TransactionRecord.TransactionType type, long amount, 
                            UUID fromPlayer, UUID toPlayer, String description, 
                            String source, String metadata) {
        this.requestId = UUID.randomUUID();
        this.type = type;
        this.amount = amount;
        this.fromPlayer = fromPlayer;
        this.toPlayer = toPlayer;
        this.description = description;
        this.timestamp = System.currentTimeMillis();
        this.source = source != null ? source : "GAME";
        this.metadata = metadata;
    }
    
    /**
     * Gets the request ID.
     * 
     * @return The request ID
     */
    public UUID getRequestId() {
        return requestId;
    }
    
    /**
     * Gets the transaction type.
     * 
     * @return The transaction type
     */
    public TransactionRecord.TransactionType getType() {
        return type;
    }
    
    /**
     * Gets the transaction amount.
     * 
     * @return The amount
     */
    public long getAmount() {
        return amount;
    }
    
    /**
     * Gets the source player.
     * 
     * @return The source player UUID, or null if not applicable
     */
    public UUID getFromPlayer() {
        return fromPlayer;
    }
    
    /**
     * Gets the target player.
     * 
     * @return The target player UUID, or null if not applicable
     */
    public UUID getToPlayer() {
        return toPlayer;
    }
    
    /**
     * Gets the transaction description.
     * 
     * @return The description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Gets the request timestamp.
     * 
     * @return The timestamp
     */
    public long getTimestamp() {
        return timestamp;
    }
    
    /**
     * Gets the transaction source.
     * 
     * @return The source
     */
    public String getSource() {
        return source;
    }
    
    /**
     * Gets the transaction metadata.
     * 
     * @return The metadata
     */
    public String getMetadata() {
        return metadata;
    }
    
    /**
     * Creates a transfer request.
     * 
     * @param fromPlayer The source player
     * @param toPlayer The target player
     * @param amount The amount to transfer
     * @param description The description
     * @return The transaction request
     */
    public static TransactionRequest createTransfer(UUID fromPlayer, UUID toPlayer, 
                                                  long amount, String description) {
        return new TransactionRequest(TransactionRecord.TransactionType.TRANSFER_SEND, 
                                    amount, fromPlayer, toPlayer, description);
    }
    
    /**
     * Creates a deposit request.
     * 
     * @param toPlayer The target player
     * @param amount The amount to deposit
     * @param description The description
     * @return The transaction request
     */
    public static TransactionRequest createDeposit(UUID toPlayer, long amount, String description) {
        return new TransactionRequest(TransactionRecord.TransactionType.DEPOSIT, 
                                    amount, null, toPlayer, description);
    }
    
    /**
     * Creates a withdrawal request.
     * 
     * @param fromPlayer The source player
     * @param amount The amount to withdraw
     * @param description The description
     * @return The transaction request
     */
    public static TransactionRequest createWithdrawal(UUID fromPlayer, long amount, String description) {
        return new TransactionRequest(TransactionRecord.TransactionType.WITHDRAWAL, 
                                    amount, fromPlayer, null, description);
    }
    
    /**
     * Creates an admin add request.
     * 
     * @param toPlayer The target player
     * @param amount The amount to add
     * @param description The description
     * @return The transaction request
     */
    public static TransactionRequest createAdminAdd(UUID toPlayer, long amount, String description) {
        return new TransactionRequest(TransactionRecord.TransactionType.ADMIN_ADD, 
                                    amount, null, toPlayer, description);
    }
    
    /**
     * Creates an admin remove request.
     * 
     * @param fromPlayer The source player
     * @param amount The amount to remove
     * @param description The description
     * @return The transaction request
     */
    public static TransactionRequest createAdminRemove(UUID fromPlayer, long amount, String description) {
        return new TransactionRequest(TransactionRecord.TransactionType.ADMIN_REMOVE, 
                                    amount, fromPlayer, null, description);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TransactionRequest that = (TransactionRequest) o;
        return Objects.equals(requestId, that.requestId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(requestId);
    }
    
    @Override
    public String toString() {
        return "TransactionRequest{" +
                "requestId=" + requestId +
                ", type=" + type +
                ", amount=" + amount +
                ", fromPlayer=" + fromPlayer +
                ", toPlayer=" + toPlayer +
                ", description='" + description + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
