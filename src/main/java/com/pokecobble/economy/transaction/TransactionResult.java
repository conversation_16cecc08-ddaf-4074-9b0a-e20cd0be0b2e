package com.pokecobble.economy.transaction;

import com.pokecobble.economy.core.TransactionRecord;

/**
 * Represents the result of a transaction operation.
 */
public class TransactionResult {
    
    private final boolean success;
    private final String message;
    private final TransactionRecord transactionRecord;
    private final String errorCode;
    private final long timestamp;
    
    /**
     * Creates a new transaction result.
     * 
     * @param success Whether the transaction was successful
     * @param message The result message
     * @param transactionRecord The transaction record (if successful)
     * @param errorCode The error code (if failed)
     */
    private TransactionResult(boolean success, String message, 
                            TransactionRecord transactionRecord, String errorCode) {
        this.success = success;
        this.message = message;
        this.transactionRecord = transactionRecord;
        this.errorCode = errorCode;
        this.timestamp = System.currentTimeMillis();
    }
    
    /**
     * Creates a successful transaction result.
     * 
     * @param message The success message
     * @param transactionRecord The transaction record
     * @return The transaction result
     */
    public static TransactionResult success(String message, TransactionRecord transactionRecord) {
        return new TransactionResult(true, message, transactionRecord, null);
    }
    
    /**
     * Creates a successful transaction result without a transaction record.
     * 
     * @param message The success message
     * @return The transaction result
     */
    public static TransactionResult success(String message) {
        return new TransactionResult(true, message, null, null);
    }
    
    /**
     * Creates a failed transaction result.
     * 
     * @param message The error message
     * @return The transaction result
     */
    public static TransactionResult failure(String message) {
        return new TransactionResult(false, message, null, null);
    }
    
    /**
     * Creates a failed transaction result with an error code.
     * 
     * @param message The error message
     * @param errorCode The error code
     * @return The transaction result
     */
    public static TransactionResult failure(String message, String errorCode) {
        return new TransactionResult(false, message, null, errorCode);
    }
    
    /**
     * Checks if the transaction was successful.
     * 
     * @return true if successful, false otherwise
     */
    public boolean isSuccess() {
        return success;
    }
    
    /**
     * Checks if the transaction failed.
     * 
     * @return true if failed, false otherwise
     */
    public boolean isFailure() {
        return !success;
    }
    
    /**
     * Gets the result message.
     * 
     * @return The message
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * Gets the transaction record.
     * 
     * @return The transaction record, or null if not available
     */
    public TransactionRecord getTransactionRecord() {
        return transactionRecord;
    }
    
    /**
     * Gets the error code.
     * 
     * @return The error code, or null if not available
     */
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * Gets the result timestamp.
     * 
     * @return The timestamp
     */
    public long getTimestamp() {
        return timestamp;
    }
    
    @Override
    public String toString() {
        return "TransactionResult{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
