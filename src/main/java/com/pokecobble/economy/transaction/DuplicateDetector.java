package com.pokecobble.economy.transaction;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.EconomyConfig;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Detects duplicate transactions to prevent money duplication exploits.
 */
public class DuplicateDetector {
    
    // Store recent transaction signatures for duplicate detection
    private final Map<UUID, Set<String>> playerTransactionSignatures;
    private final Map<String, Long> signatureTimestamps;
    
    private boolean initialized;
    
    /**
     * Creates a new duplicate detector.
     */
    public DuplicateDetector() {
        this.playerTransactionSignatures = new ConcurrentHashMap<>();
        this.signatureTimestamps = new ConcurrentHashMap<>();
        this.initialized = false;
    }
    
    /**
     * Initializes the duplicate detector.
     */
    public void initialize() {
        this.initialized = true;
        Pokecobbleclaim.LOGGER.info("Duplicate detector initialized");
    }
    
    /**
     * Checks if a transaction request is a duplicate.
     * 
     * @param request The transaction request to check
     * @return true if duplicate, false otherwise
     */
    public boolean isDuplicate(TransactionRequest request) {
        if (!initialized || !EconomyConfig.ENABLE_DUPLICATE_DETECTION) {
            return false;
        }
        
        try {
            String signature = generateTransactionSignature(request);
            UUID playerId = getRelevantPlayerId(request);
            
            if (playerId == null) {
                return false; // Cannot detect duplicates without player ID
            }
            
            // Clean up old signatures first
            cleanupOldSignatures();
            
            // Check if this signature already exists for this player
            Set<String> playerSignatures = playerTransactionSignatures.computeIfAbsent(playerId, k -> ConcurrentHashMap.newKeySet());
            
            if (playerSignatures.contains(signature)) {
                // Check if it's within the duplicate detection window
                Long signatureTime = signatureTimestamps.get(signature);
                if (signatureTime != null) {
                    long timeDiff = System.currentTimeMillis() - signatureTime;
                    if (timeDiff <= EconomyConfig.DUPLICATE_DETECTION_WINDOW_MS) {
                        Pokecobbleclaim.LOGGER.warn("Duplicate transaction detected for player " + playerId + 
                                                  " (signature: " + signature + ", time diff: " + timeDiff + "ms)");
                        return true;
                    }
                }
            }
            
            // Add signature to tracking
            playerSignatures.add(signature);
            signatureTimestamps.put(signature, System.currentTimeMillis());
            
            return false;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error checking for duplicate transaction", e);
            return false; // Fail open to avoid blocking legitimate transactions
        }
    }
    
    /**
     * Generates a unique signature for a transaction request.
     * 
     * @param request The transaction request
     * @return The transaction signature
     */
    private String generateTransactionSignature(TransactionRequest request) {
        StringBuilder signature = new StringBuilder();
        
        signature.append(request.getType().name());
        signature.append("|");
        signature.append(request.getAmount());
        signature.append("|");
        signature.append(request.getFromPlayer() != null ? request.getFromPlayer().toString() : "null");
        signature.append("|");
        signature.append(request.getToPlayer() != null ? request.getToPlayer().toString() : "null");
        signature.append("|");
        signature.append(request.getDescription());
        
        // Use a hash to make the signature more manageable
        return Integer.toString(signature.toString().hashCode());
    }
    
    /**
     * Gets the relevant player ID for duplicate detection.
     * 
     * @param request The transaction request
     * @return The player ID to use for tracking
     */
    private UUID getRelevantPlayerId(TransactionRequest request) {
        // For most transactions, use the source player
        if (request.getFromPlayer() != null) {
            return request.getFromPlayer();
        }
        
        // For deposits and admin adds, use the target player
        if (request.getToPlayer() != null) {
            return request.getToPlayer();
        }
        
        return null;
    }
    
    /**
     * Cleans up old transaction signatures that are outside the detection window.
     */
    private void cleanupOldSignatures() {
        long cutoffTime = System.currentTimeMillis() - EconomyConfig.DUPLICATE_DETECTION_WINDOW_MS;
        
        // Remove old signatures from timestamp map
        Iterator<Map.Entry<String, Long>> timestampIterator = signatureTimestamps.entrySet().iterator();
        Set<String> expiredSignatures = new HashSet<>();
        
        while (timestampIterator.hasNext()) {
            Map.Entry<String, Long> entry = timestampIterator.next();
            if (entry.getValue() < cutoffTime) {
                expiredSignatures.add(entry.getKey());
                timestampIterator.remove();
            }
        }
        
        // Remove expired signatures from player signature sets
        for (Set<String> playerSignatures : playerTransactionSignatures.values()) {
            playerSignatures.removeAll(expiredSignatures);
        }
        
        // Remove empty player signature sets
        playerTransactionSignatures.entrySet().removeIf(entry -> entry.getValue().isEmpty());
    }
    
    /**
     * Manually removes a transaction signature (for testing or admin purposes).
     * 
     * @param playerId The player ID
     * @param signature The signature to remove
     */
    public void removeSignature(UUID playerId, String signature) {
        if (!initialized) {
            return;
        }
        
        Set<String> playerSignatures = playerTransactionSignatures.get(playerId);
        if (playerSignatures != null) {
            playerSignatures.remove(signature);
            signatureTimestamps.remove(signature);
        }
    }
    
    /**
     * Clears all signatures for a player (for admin purposes).
     * 
     * @param playerId The player ID
     */
    public void clearPlayerSignatures(UUID playerId) {
        if (!initialized) {
            return;
        }
        
        Set<String> playerSignatures = playerTransactionSignatures.remove(playerId);
        if (playerSignatures != null) {
            for (String signature : playerSignatures) {
                signatureTimestamps.remove(signature);
            }
        }
    }
    
    /**
     * Gets the number of tracked signatures for a player.
     * 
     * @param playerId The player ID
     * @return The number of signatures
     */
    public int getSignatureCount(UUID playerId) {
        if (!initialized) {
            return 0;
        }
        
        Set<String> playerSignatures = playerTransactionSignatures.get(playerId);
        return playerSignatures != null ? playerSignatures.size() : 0;
    }
    
    /**
     * Gets the total number of tracked signatures across all players.
     * 
     * @return The total signature count
     */
    public int getTotalSignatureCount() {
        if (!initialized) {
            return 0;
        }
        
        return signatureTimestamps.size();
    }
    
    /**
     * Performs a full cleanup of all expired signatures.
     */
    public void performFullCleanup() {
        if (!initialized) {
            return;
        }
        
        cleanupOldSignatures();
        Pokecobbleclaim.LOGGER.debug("Performed full duplicate detection cleanup. " +
                                   "Total signatures: " + getTotalSignatureCount());
    }
    
    /**
     * Checks if the duplicate detector is initialized.
     * 
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}
