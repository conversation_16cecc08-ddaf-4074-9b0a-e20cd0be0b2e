package com.pokecobble.economy.transaction;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.EconomyConfig;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Implements rate limiting for transactions to prevent spam and abuse.
 */
public class RateLimiter {
    
    // Track transaction counts per player per time window
    private final Map<UUID, List<Long>> playerTransactionTimes;
    
    // Track failed attempts for additional security
    private final Map<UUID, Integer> playerFailedAttempts;
    private final Map<UUID, Long> playerLastFailedAttempt;
    
    private boolean initialized;
    
    /**
     * Creates a new rate limiter.
     */
    public RateLimiter() {
        this.playerTransactionTimes = new ConcurrentHashMap<>();
        this.playerFailedAttempts = new ConcurrentHashMap<>();
        this.playerLastFailedAttempt = new ConcurrentHashMap<>();
        this.initialized = false;
    }
    
    /**
     * Initializes the rate limiter.
     */
    public void initialize() {
        this.initialized = true;
        Pokecobbleclaim.LOGGER.info("Rate limiter initialized");
    }
    
    /**
     * Checks if a player is within rate limits for transactions.
     * 
     * @param playerId The player ID to check
     * @return true if within limits, false if rate limited
     */
    public boolean checkRateLimit(UUID playerId) {
        if (!initialized || !EconomyConfig.ENABLE_RATE_LIMITING) {
            return true;
        }
        
        if (playerId == null) {
            return true; // Allow system transactions
        }
        
        try {
            long currentTime = System.currentTimeMillis();
            
            // Clean up old transaction times first
            cleanupOldTransactions(playerId, currentTime);
            
            // Check if player has too many failed attempts recently
            if (hasExcessiveFailedAttempts(playerId, currentTime)) {
                Pokecobbleclaim.LOGGER.warn("Player " + playerId + " blocked due to excessive failed attempts");
                return false;
            }
            
            // Get player's transaction times
            List<Long> transactionTimes = playerTransactionTimes.computeIfAbsent(playerId, k -> new ArrayList<>());
            
            // Check per-minute limit
            long oneMinuteAgo = currentTime - 60000; // 1 minute
            long recentTransactions = transactionTimes.stream()
                    .filter(time -> time > oneMinuteAgo)
                    .count();
            
            if (recentTransactions >= EconomyConfig.MAX_TRANSACTIONS_PER_MINUTE) {
                recordFailedAttempt(playerId, currentTime);
                Pokecobbleclaim.LOGGER.warn("Player " + playerId + " exceeded per-minute transaction limit: " + 
                                          recentTransactions + "/" + EconomyConfig.MAX_TRANSACTIONS_PER_MINUTE);
                return false;
            }
            
            // Check per-hour limit
            long oneHourAgo = currentTime - 3600000; // 1 hour
            long hourlyTransactions = transactionTimes.stream()
                    .filter(time -> time > oneHourAgo)
                    .count();
            
            if (hourlyTransactions >= EconomyConfig.MAX_TRANSACTIONS_PER_HOUR) {
                recordFailedAttempt(playerId, currentTime);
                Pokecobbleclaim.LOGGER.warn("Player " + playerId + " exceeded per-hour transaction limit: " + 
                                          hourlyTransactions + "/" + EconomyConfig.MAX_TRANSACTIONS_PER_HOUR);
                return false;
            }
            
            // Record this transaction attempt
            transactionTimes.add(currentTime);
            
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error checking rate limit for player " + playerId, e);
            return true; // Fail open to avoid blocking legitimate transactions
        }
    }
    
    /**
     * Records a successful transaction for rate limiting purposes.
     * 
     * @param playerId The player ID
     */
    public void recordTransaction(UUID playerId) {
        if (!initialized || playerId == null) {
            return;
        }
        
        // Reset failed attempts on successful transaction
        playerFailedAttempts.remove(playerId);
        playerLastFailedAttempt.remove(playerId);
    }
    
    /**
     * Records a failed transaction attempt.
     * 
     * @param playerId The player ID
     * @param currentTime The current timestamp
     */
    private void recordFailedAttempt(UUID playerId, long currentTime) {
        playerFailedAttempts.merge(playerId, 1, Integer::sum);
        playerLastFailedAttempt.put(playerId, currentTime);
    }
    
    /**
     * Checks if a player has excessive failed attempts recently.
     * 
     * @param playerId The player ID
     * @param currentTime The current timestamp
     * @return true if excessive failed attempts, false otherwise
     */
    private boolean hasExcessiveFailedAttempts(UUID playerId, long currentTime) {
        Integer failedAttempts = playerFailedAttempts.get(playerId);
        Long lastFailedAttempt = playerLastFailedAttempt.get(playerId);
        
        if (failedAttempts == null || lastFailedAttempt == null) {
            return false;
        }
        
        // Reset failed attempts if enough time has passed
        long timeSinceLastFailed = currentTime - lastFailedAttempt;
        if (timeSinceLastFailed > 300000) { // 5 minutes
            playerFailedAttempts.remove(playerId);
            playerLastFailedAttempt.remove(playerId);
            return false;
        }
        
        // Check if failed attempts exceed threshold
        return failedAttempts >= 10; // 10 failed attempts in 5 minutes
    }
    
    /**
     * Cleans up old transaction times for a player.
     * 
     * @param playerId The player ID
     * @param currentTime The current timestamp
     */
    private void cleanupOldTransactions(UUID playerId, long currentTime) {
        List<Long> transactionTimes = playerTransactionTimes.get(playerId);
        if (transactionTimes == null) {
            return;
        }
        
        // Remove transactions older than 1 hour
        long oneHourAgo = currentTime - 3600000;
        transactionTimes.removeIf(time -> time < oneHourAgo);
        
        // Remove empty lists
        if (transactionTimes.isEmpty()) {
            playerTransactionTimes.remove(playerId);
        }
    }
    
    /**
     * Gets the number of transactions a player has made in the last minute.
     * 
     * @param playerId The player ID
     * @return The transaction count
     */
    public int getRecentTransactionCount(UUID playerId) {
        if (!initialized || playerId == null) {
            return 0;
        }
        
        List<Long> transactionTimes = playerTransactionTimes.get(playerId);
        if (transactionTimes == null) {
            return 0;
        }
        
        long oneMinuteAgo = System.currentTimeMillis() - 60000;
        return (int) transactionTimes.stream()
                .filter(time -> time > oneMinuteAgo)
                .count();
    }
    
    /**
     * Gets the number of transactions a player has made in the last hour.
     * 
     * @param playerId The player ID
     * @return The transaction count
     */
    public int getHourlyTransactionCount(UUID playerId) {
        if (!initialized || playerId == null) {
            return 0;
        }
        
        List<Long> transactionTimes = playerTransactionTimes.get(playerId);
        if (transactionTimes == null) {
            return 0;
        }
        
        long oneHourAgo = System.currentTimeMillis() - 3600000;
        return (int) transactionTimes.stream()
                .filter(time -> time > oneHourAgo)
                .count();
    }
    
    /**
     * Gets the number of failed attempts for a player.
     * 
     * @param playerId The player ID
     * @return The failed attempt count
     */
    public int getFailedAttemptCount(UUID playerId) {
        if (!initialized || playerId == null) {
            return 0;
        }
        
        return playerFailedAttempts.getOrDefault(playerId, 0);
    }
    
    /**
     * Manually resets rate limits for a player (admin function).
     * 
     * @param playerId The player ID
     */
    public void resetPlayerLimits(UUID playerId) {
        if (!initialized || playerId == null) {
            return;
        }
        
        playerTransactionTimes.remove(playerId);
        playerFailedAttempts.remove(playerId);
        playerLastFailedAttempt.remove(playerId);
        
        Pokecobbleclaim.LOGGER.info("Reset rate limits for player " + playerId);
    }
    
    /**
     * Performs cleanup of old data across all players.
     */
    public void performCleanup() {
        if (!initialized) {
            return;
        }
        
        long currentTime = System.currentTimeMillis();
        
        // Clean up transaction times
        for (UUID playerId : new HashSet<>(playerTransactionTimes.keySet())) {
            cleanupOldTransactions(playerId, currentTime);
        }
        
        // Clean up old failed attempts
        long fiveMinutesAgo = currentTime - 300000;
        playerLastFailedAttempt.entrySet().removeIf(entry -> entry.getValue() < fiveMinutesAgo);
        
        // Remove failed attempt counts for players with no recent failed attempts
        Set<UUID> playersWithOldFailedAttempts = new HashSet<>(playerFailedAttempts.keySet());
        playersWithOldFailedAttempts.removeAll(playerLastFailedAttempt.keySet());
        playersWithOldFailedAttempts.forEach(playerFailedAttempts::remove);
        
        Pokecobbleclaim.LOGGER.debug("Rate limiter cleanup completed. Tracking " + 
                                   playerTransactionTimes.size() + " players");
    }
    
    /**
     * Gets statistics about the rate limiter.
     * 
     * @return A map of statistics
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("initialized", initialized);
        stats.put("tracked_players", playerTransactionTimes.size());
        stats.put("players_with_failed_attempts", playerFailedAttempts.size());
        
        int totalTransactions = playerTransactionTimes.values().stream()
                .mapToInt(List::size)
                .sum();
        stats.put("total_tracked_transactions", totalTransactions);
        
        return stats;
    }
    
    /**
     * Checks if the rate limiter is initialized.
     * 
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}
