package com.pokecobble.economy.transaction;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.EconomyConfig;
import com.pokecobble.economy.core.TransactionRecord;

/**
 * Validates transaction requests to ensure they meet all requirements.
 */
public class TransactionValidator {
    
    private boolean initialized;
    
    /**
     * Creates a new transaction validator.
     */
    public TransactionValidator() {
        this.initialized = false;
    }
    
    /**
     * Initializes the validator.
     */
    public void initialize() {
        this.initialized = true;
        Pokecobbleclaim.LOGGER.info("Transaction validator initialized");
    }
    
    /**
     * Validates a transaction request.
     * 
     * @param request The request to validate
     * @return The validation result
     */
    public ValidationResult validateTransaction(TransactionRequest request) {
        if (!initialized) {
            return ValidationResult.invalid("Validator not initialized");
        }
        
        if (request == null) {
            return ValidationResult.invalid("Transaction request is null");
        }
        
        // Validate transaction type
        if (request.getType() == null) {
            return ValidationResult.invalid("Transaction type is null");
        }
        
        // Validate amount
        ValidationResult amountValidation = validateAmount(request.getAmount());
        if (!amountValidation.isValid()) {
            return amountValidation;
        }
        
        // Validate players based on transaction type
        ValidationResult playerValidation = validatePlayers(request);
        if (!playerValidation.isValid()) {
            return playerValidation;
        }
        
        // Validate description
        if (request.getDescription() == null || request.getDescription().trim().isEmpty()) {
            return ValidationResult.invalid("Transaction description is required");
        }
        
        if (request.getDescription().length() > 255) {
            return ValidationResult.invalid("Transaction description too long (max 255 characters)");
        }
        
        // Validate timestamp
        long currentTime = System.currentTimeMillis();
        if (request.getTimestamp() > currentTime + 60000) { // Allow 1 minute future tolerance
            return ValidationResult.invalid("Transaction timestamp is too far in the future");
        }
        
        if (request.getTimestamp() < currentTime - 300000) { // Allow 5 minutes past tolerance
            return ValidationResult.invalid("Transaction timestamp is too old");
        }
        
        return ValidationResult.valid();
    }
    
    /**
     * Validates the transaction amount.
     * 
     * @param amount The amount to validate
     * @return The validation result
     */
    private ValidationResult validateAmount(long amount) {
        if (amount <= 0) {
            return ValidationResult.invalid("Transaction amount must be positive");
        }
        
        if (amount < EconomyConfig.MIN_TRANSACTION_AMOUNT) {
            return ValidationResult.invalid("Transaction amount below minimum: " + EconomyConfig.MIN_TRANSACTION_AMOUNT);
        }
        
        if (amount > EconomyConfig.MAX_TRANSACTION_AMOUNT) {
            return ValidationResult.invalid("Transaction amount exceeds maximum: " + EconomyConfig.MAX_TRANSACTION_AMOUNT);
        }
        
        return ValidationResult.valid();
    }
    
    /**
     * Validates the players involved in the transaction.
     * 
     * @param request The transaction request
     * @return The validation result
     */
    private ValidationResult validatePlayers(TransactionRequest request) {
        TransactionRecord.TransactionType type = request.getType();
        
        switch (type) {
            case TRANSFER_SEND:
                if (request.getFromPlayer() == null) {
                    return ValidationResult.invalid("Transfer requires source player");
                }
                if (request.getToPlayer() == null) {
                    return ValidationResult.invalid("Transfer requires target player");
                }
                if (request.getFromPlayer().equals(request.getToPlayer())) {
                    return ValidationResult.invalid("Cannot transfer to self");
                }
                break;
                
            case DEPOSIT:
            case ADMIN_ADD:
                if (request.getToPlayer() == null) {
                    return ValidationResult.invalid("Deposit/Add requires target player");
                }
                break;
                
            case WITHDRAWAL:
            case ADMIN_REMOVE:
                if (request.getFromPlayer() == null) {
                    return ValidationResult.invalid("Withdrawal/Remove requires source player");
                }
                break;
                
            default:
                return ValidationResult.invalid("Unsupported transaction type: " + type);
        }
        
        return ValidationResult.valid();
    }
    
    /**
     * Checks if the validator is initialized.
     * 
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}

/**
 * Represents the result of a validation operation.
 */
class ValidationResult {
    
    private final boolean valid;
    private final String errorMessage;
    
    /**
     * Creates a new validation result.
     * 
     * @param valid Whether the validation passed
     * @param errorMessage The error message (if validation failed)
     */
    private ValidationResult(boolean valid, String errorMessage) {
        this.valid = valid;
        this.errorMessage = errorMessage;
    }
    
    /**
     * Creates a valid result.
     * 
     * @return The validation result
     */
    public static ValidationResult valid() {
        return new ValidationResult(true, null);
    }
    
    /**
     * Creates an invalid result.
     * 
     * @param errorMessage The error message
     * @return The validation result
     */
    public static ValidationResult invalid(String errorMessage) {
        return new ValidationResult(false, errorMessage);
    }
    
    /**
     * Checks if the validation passed.
     * 
     * @return true if valid, false otherwise
     */
    public boolean isValid() {
        return valid;
    }
    
    /**
     * Gets the error message.
     * 
     * @return The error message, or null if valid
     */
    public String getErrorMessage() {
        return errorMessage;
    }
    
    @Override
    public String toString() {
        return "ValidationResult{" +
                "valid=" + valid +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}
