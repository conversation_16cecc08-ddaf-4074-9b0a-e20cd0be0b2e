package com.pokecobble.economy.manager;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.EconomyConfig;
import com.pokecobble.economy.core.PlayerEconomyData;
import com.pokecobble.economy.core.TransactionRecord;
import com.pokecobble.economy.data.EconomyDataStorage;
import com.pokecobble.economy.network.EconomyNetworkManager;
import com.pokecobble.economy.security.EconomySecurityManager;
import com.pokecobble.economy.transaction.TransactionManager;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Main economy manager that coordinates all economy operations.
 * Provides a unified interface for all economy-related functionality.
 */
public class EconomyManager {
    
    private static EconomyManager instance;
    private static final ReadWriteLock instanceLock = new ReentrantReadWriteLock();
    
    private final EconomyDataStorage dataStorage;
    private final TransactionManager transactionManager;
    private final EconomySecurityManager securityManager;
    private final EconomyNetworkManager networkManager;
    
    // Cache for frequently accessed player data
    private final Map<UUID, PlayerEconomyData> playerDataCache;
    private final ReadWriteLock cacheLock;
    
    private MinecraftServer server;
    private boolean initialized;
    
    /**
     * Private constructor for singleton pattern.
     */
    private EconomyManager() {
        this.dataStorage = new EconomyDataStorage();
        this.transactionManager = new TransactionManager(this);
        this.securityManager = new EconomySecurityManager();
        this.networkManager = new EconomyNetworkManager(this);
        this.playerDataCache = new ConcurrentHashMap<>();
        this.cacheLock = new ReentrantReadWriteLock();
        this.initialized = false;
    }
    
    /**
     * Gets the singleton instance of the economy manager.
     * 
     * @return The economy manager instance
     */
    public static EconomyManager getInstance() {
        instanceLock.readLock().lock();
        try {
            if (instance != null) {
                return instance;
            }
        } finally {
            instanceLock.readLock().unlock();
        }
        
        instanceLock.writeLock().lock();
        try {
            if (instance == null) {
                instance = new EconomyManager();
            }
            return instance;
        } finally {
            instanceLock.writeLock().unlock();
        }
    }
    
    /**
     * Initializes the economy system.
     * 
     * @param server The Minecraft server instance
     */
    public void initialize(MinecraftServer server) {
        if (initialized) {
            Pokecobbleclaim.LOGGER.warn("Economy system already initialized");
            return;
        }
        
        try {
            this.server = server;
            
            // Validate configuration
            EconomyConfig.validateConfig();
            
            // Initialize components
            dataStorage.initialize();
            transactionManager.initialize();
            securityManager.initialize();
            networkManager.initialize();
            
            // Register shutdown hook
            Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown, "EconomyShutdownHook"));
            
            this.initialized = true;
            Pokecobbleclaim.LOGGER.info("Economy system initialized successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize economy system", e);
            throw new RuntimeException("Economy system initialization failed", e);
        }
    }
    
    /**
     * Shuts down the economy system gracefully.
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        try {
            Pokecobbleclaim.LOGGER.info("Shutting down economy system...");
            
            // Save all cached data
            saveAllCachedData();
            
            // Shutdown components
            networkManager.shutdown();
            transactionManager.shutdown();
            dataStorage.shutdown();
            securityManager.shutdown();
            
            // Clear cache
            playerDataCache.clear();
            
            this.initialized = false;
            Pokecobbleclaim.LOGGER.info("Economy system shut down successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during economy system shutdown", e);
        }
    }
    
    /**
     * Gets a player's economy data.
     * 
     * @param playerId The player's UUID
     * @return The player's economy data, or null if not found
     */
    public PlayerEconomyData getPlayerData(UUID playerId) {
        if (!initialized) {
            throw new IllegalStateException("Economy system not initialized");
        }
        
        // Check cache first
        cacheLock.readLock().lock();
        try {
            PlayerEconomyData cached = playerDataCache.get(playerId);
            if (cached != null) {
                return cached;
            }
        } finally {
            cacheLock.readLock().unlock();
        }
        
        // Load from storage
        PlayerEconomyData data = dataStorage.loadPlayerData(playerId);
        if (data != null) {
            // Cache the data
            cacheLock.writeLock().lock();
            try {
                playerDataCache.put(playerId, data);
            } finally {
                cacheLock.writeLock().unlock();
            }
        }
        
        return data;
    }
    
    /**
     * Creates new player economy data.
     * 
     * @param playerId The player's UUID
     * @param playerName The player's name
     * @return The created player data
     */
    public PlayerEconomyData createPlayerData(UUID playerId, String playerName) {
        if (!initialized) {
            throw new IllegalStateException("Economy system not initialized");
        }
        
        PlayerEconomyData data = new PlayerEconomyData(playerId, playerName);
        
        // Save to storage
        dataStorage.savePlayerData(data);
        
        // Cache the data
        cacheLock.writeLock().lock();
        try {
            playerDataCache.put(playerId, data);
        } finally {
            cacheLock.writeLock().unlock();
        }
        
        Pokecobbleclaim.LOGGER.info("Created economy data for player " + playerName + " (" + playerId + ")");
        return data;
    }
    
    /**
     * Gets a player's balance.
     * 
     * @param playerId The player's UUID
     * @return The player's balance, or 0 if player not found
     */
    public long getBalance(UUID playerId) {
        PlayerEconomyData data = getPlayerData(playerId);
        return data != null ? data.getBalance() : 0L;
    }
    
    /**
     * Sets a player's balance.
     * 
     * @param playerId The player's UUID
     * @param amount The new balance amount
     * @return true if successful, false otherwise
     */
    public boolean setBalance(UUID playerId, long amount) {
        return setBalance(playerId, amount, "Balance set by system");
    }
    
    /**
     * Sets a player's balance with a description.
     * 
     * @param playerId The player's UUID
     * @param amount The new balance amount
     * @param description The transaction description
     * @return true if successful, false otherwise
     */
    public boolean setBalance(UUID playerId, long amount, String description) {
        if (!initialized) {
            return false;
        }
        
        try {
            PlayerEconomyData data = getPlayerData(playerId);
            if (data == null) {
                return false;
            }
            
            // Check security
            if (data.getSecurityFlags().isFrozen()) {
                Pokecobbleclaim.LOGGER.warn("Attempted to modify balance for frozen account: " + playerId);
                return false;
            }
            
            long oldBalance = data.getBalance();
            data.setBalance(amount);
            
            // Create transaction record
            TransactionRecord.TransactionType type = amount > oldBalance ? 
                TransactionRecord.TransactionType.ADMIN_ADD : 
                TransactionRecord.TransactionType.ADMIN_REMOVE;
            
            TransactionRecord transaction = new TransactionRecord(
                type, Math.abs(amount - oldBalance), null, playerId, 
                description, oldBalance, amount
            );
            
            data.addTransaction(transaction);
            
            // Save to storage
            dataStorage.savePlayerData(data);
            
            // Log transaction
            transactionManager.logTransaction(transaction);
            
            // Sync to client if player is online
            syncPlayerData(playerId);
            
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error setting balance for player " + playerId, e);
            return false;
        }
    }
    
    /**
     * Adds money to a player's balance.
     * 
     * @param playerId The player's UUID
     * @param amount The amount to add
     * @param description The transaction description
     * @return true if successful, false otherwise
     */
    public boolean addBalance(UUID playerId, long amount, String description) {
        if (!initialized || amount <= 0) {
            return false;
        }
        
        try {
            PlayerEconomyData data = getPlayerData(playerId);
            if (data == null) {
                return false;
            }
            
            // Check security
            if (data.getSecurityFlags().isFrozen()) {
                Pokecobbleclaim.LOGGER.warn("Attempted to modify balance for frozen account: " + playerId);
                return false;
            }
            
            long oldBalance = data.getBalance();
            data.addBalance(amount);
            
            // Create transaction record
            TransactionRecord transaction = new TransactionRecord(
                TransactionRecord.TransactionType.DEPOSIT, amount, null, playerId, 
                description, oldBalance, data.getBalance()
            );
            
            data.addTransaction(transaction);
            
            // Save to storage
            dataStorage.savePlayerData(data);
            
            // Log transaction
            transactionManager.logTransaction(transaction);
            
            // Sync to client if player is online
            syncPlayerData(playerId);
            
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error adding balance for player " + playerId, e);
            return false;
        }
    }
    
    /**
     * Subtracts money from a player's balance.
     * 
     * @param playerId The player's UUID
     * @param amount The amount to subtract
     * @param description The transaction description
     * @return true if successful, false otherwise
     */
    public boolean subtractBalance(UUID playerId, long amount, String description) {
        if (!initialized || amount <= 0) {
            return false;
        }
        
        try {
            PlayerEconomyData data = getPlayerData(playerId);
            if (data == null || data.getBalance() < amount) {
                return false;
            }
            
            // Check security
            if (data.getSecurityFlags().isFrozen()) {
                Pokecobbleclaim.LOGGER.warn("Attempted to modify balance for frozen account: " + playerId);
                return false;
            }
            
            long oldBalance = data.getBalance();
            data.subtractBalance(amount);
            
            // Create transaction record
            TransactionRecord transaction = new TransactionRecord(
                TransactionRecord.TransactionType.WITHDRAWAL, amount, playerId, null, 
                description, oldBalance, data.getBalance()
            );
            
            data.addTransaction(transaction);
            
            // Save to storage
            dataStorage.savePlayerData(data);
            
            // Log transaction
            transactionManager.logTransaction(transaction);
            
            // Sync to client if player is online
            syncPlayerData(playerId);
            
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error subtracting balance for player " + playerId, e);
            return false;
        }
    }
    
    /**
     * Handles player joining the server.
     * 
     * @param player The player who joined
     */
    public void onPlayerJoin(ServerPlayerEntity player) {
        if (!initialized) {
            return;
        }
        
        CompletableFuture.runAsync(() -> {
            try {
                UUID playerId = player.getUuid();
                String playerName = player.getName().getString();
                
                // Get or create player data
                PlayerEconomyData data = getPlayerData(playerId);
                if (data == null) {
                    data = createPlayerData(playerId, playerName);
                } else {
                    // Update player name if changed
                    if (!playerName.equals(data.getPlayerName())) {
                        data.setPlayerName(playerName);
                        dataStorage.savePlayerData(data);
                    }
                    
                    // Update last login
                    data.setLastLogin(System.currentTimeMillis());
                    dataStorage.savePlayerData(data);
                }
                
                // Sync data to client
                syncPlayerData(playerId);
                
                Pokecobbleclaim.LOGGER.debug("Processed economy data for joining player: " + playerName);
                
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling player join for economy system", e);
            }
        });
    }
    
    /**
     * Handles player leaving the server.
     * 
     * @param player The player who left
     */
    public void onPlayerLeave(ServerPlayerEntity player) {
        if (!initialized) {
            return;
        }
        
        UUID playerId = player.getUuid();
        
        // Save any cached data
        cacheLock.readLock().lock();
        try {
            PlayerEconomyData data = playerDataCache.get(playerId);
            if (data != null) {
                dataStorage.savePlayerData(data);
            }
        } finally {
            cacheLock.readLock().unlock();
        }
        
        // Remove from cache after a delay to allow for quick reconnects
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(30000); // 30 seconds
                cacheLock.writeLock().lock();
                try {
                    playerDataCache.remove(playerId);
                } finally {
                    cacheLock.writeLock().unlock();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }
    
    /**
     * Synchronizes player data to the client.
     * 
     * @param playerId The player's UUID
     */
    private void syncPlayerData(UUID playerId) {
        if (server != null) {
            ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
            if (player != null) {
                networkManager.syncPlayerData(player);
            }
        }
    }
    
    /**
     * Saves all cached player data.
     */
    private void saveAllCachedData() {
        cacheLock.readLock().lock();
        try {
            for (PlayerEconomyData data : playerDataCache.values()) {
                dataStorage.savePlayerData(data);
            }
        } finally {
            cacheLock.readLock().unlock();
        }
    }
    
    /**
     * Gets the data storage instance.
     * 
     * @return The data storage
     */
    public EconomyDataStorage getDataStorage() {
        return dataStorage;
    }
    
    /**
     * Gets the transaction manager instance.
     * 
     * @return The transaction manager
     */
    public TransactionManager getTransactionManager() {
        return transactionManager;
    }
    
    /**
     * Gets the security manager instance.
     * 
     * @return The security manager
     */
    public EconomySecurityManager getSecurityManager() {
        return securityManager;
    }
    
    /**
     * Gets the network manager instance.
     * 
     * @return The network manager
     */
    public EconomyNetworkManager getNetworkManager() {
        return networkManager;
    }
    
    /**
     * Checks if the economy system is initialized.
     * 
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}
