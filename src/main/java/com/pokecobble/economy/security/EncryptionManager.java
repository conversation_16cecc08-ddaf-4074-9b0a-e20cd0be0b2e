package com.pokecobble.economy.security;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.EconomyConfig;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Arrays;

/**
 * Manages encryption and decryption of economy data.
 * Uses AES-GCM for authenticated encryption to prevent tampering.
 */
public class EncryptionManager {
    
    private static final String KEY_FILE_NAME = "economy.key";
    private static final String ALGORITHM = "AES";
    
    private SecretKey encryptionKey;
    private final SecureRandom secureRandom;
    private boolean initialized;
    
    /**
     * Creates a new encryption manager.
     */
    public EncryptionManager() {
        this.secureRandom = new SecureRandom();
        this.initialized = false;
    }
    
    /**
     * Initializes the encryption manager.
     */
    public void initialize() {
        if (initialized) {
            return;
        }
        
        try {
            // Load or generate encryption key
            loadOrGenerateKey();
            
            this.initialized = true;
            Pokecobbleclaim.LOGGER.info("Encryption manager initialized successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize encryption manager", e);
            throw new RuntimeException("Encryption manager initialization failed", e);
        }
    }
    
    /**
     * Encrypts data using AES-GCM.
     * 
     * @param plaintext The data to encrypt
     * @return The encrypted data with IV prepended
     * @throws Exception if encryption fails
     */
    public byte[] encrypt(byte[] plaintext) throws Exception {
        if (!initialized) {
            throw new IllegalStateException("Encryption manager not initialized");
        }
        
        // Generate random IV
        byte[] iv = new byte[EconomyConfig.GCM_IV_LENGTH];
        secureRandom.nextBytes(iv);
        
        // Initialize cipher
        Cipher cipher = Cipher.getInstance(EconomyConfig.ENCRYPTION_ALGORITHM);
        GCMParameterSpec gcmSpec = new GCMParameterSpec(EconomyConfig.GCM_TAG_LENGTH * 8, iv);
        cipher.init(Cipher.ENCRYPT_MODE, encryptionKey, gcmSpec);
        
        // Encrypt the data
        byte[] ciphertext = cipher.doFinal(plaintext);
        
        // Combine IV and ciphertext
        byte[] encryptedData = new byte[iv.length + ciphertext.length];
        System.arraycopy(iv, 0, encryptedData, 0, iv.length);
        System.arraycopy(ciphertext, 0, encryptedData, iv.length, ciphertext.length);
        
        return encryptedData;
    }
    
    /**
     * Decrypts data using AES-GCM.
     * 
     * @param encryptedData The encrypted data with IV prepended
     * @return The decrypted plaintext
     * @throws Exception if decryption fails
     */
    public byte[] decrypt(byte[] encryptedData) throws Exception {
        if (!initialized) {
            throw new IllegalStateException("Encryption manager not initialized");
        }
        
        if (encryptedData.length < EconomyConfig.GCM_IV_LENGTH) {
            throw new IllegalArgumentException("Encrypted data too short");
        }
        
        // Extract IV and ciphertext
        byte[] iv = new byte[EconomyConfig.GCM_IV_LENGTH];
        byte[] ciphertext = new byte[encryptedData.length - EconomyConfig.GCM_IV_LENGTH];
        
        System.arraycopy(encryptedData, 0, iv, 0, EconomyConfig.GCM_IV_LENGTH);
        System.arraycopy(encryptedData, EconomyConfig.GCM_IV_LENGTH, ciphertext, 0, ciphertext.length);
        
        // Initialize cipher
        Cipher cipher = Cipher.getInstance(EconomyConfig.ENCRYPTION_ALGORITHM);
        GCMParameterSpec gcmSpec = new GCMParameterSpec(EconomyConfig.GCM_TAG_LENGTH * 8, iv);
        cipher.init(Cipher.DECRYPT_MODE, encryptionKey, gcmSpec);
        
        // Decrypt the data
        return cipher.doFinal(ciphertext);
    }
    
    /**
     * Loads an existing encryption key or generates a new one.
     */
    private void loadOrGenerateKey() throws Exception {
        Path keyPath = getKeyFilePath();
        
        if (Files.exists(keyPath)) {
            // Load existing key
            loadKey(keyPath);
            Pokecobbleclaim.LOGGER.info("Loaded existing encryption key");
        } else {
            // Generate new key
            generateKey();
            saveKey(keyPath);
            Pokecobbleclaim.LOGGER.info("Generated new encryption key");
        }
    }
    
    /**
     * Generates a new encryption key.
     */
    private void generateKey() throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
        keyGenerator.init(EconomyConfig.ENCRYPTION_KEY_LENGTH);
        this.encryptionKey = keyGenerator.generateKey();
    }
    
    /**
     * Saves the encryption key to disk.
     * 
     * @param keyPath The path to save the key to
     */
    private void saveKey(Path keyPath) throws IOException {
        byte[] keyBytes = encryptionKey.getEncoded();
        
        // Create a simple obfuscation to prevent casual viewing
        byte[] obfuscatedKey = obfuscateKey(keyBytes);
        
        Files.write(keyPath, obfuscatedKey);
        
        // Set restrictive permissions (Unix-like systems only)
        try {
            keyPath.toFile().setReadable(false, false);
            keyPath.toFile().setWritable(false, false);
            keyPath.toFile().setExecutable(false, false);
            keyPath.toFile().setReadable(true, true);
            keyPath.toFile().setWritable(true, true);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Could not set restrictive permissions on key file: " + e.getMessage());
        }
    }
    
    /**
     * Loads the encryption key from disk.
     * 
     * @param keyPath The path to load the key from
     */
    private void loadKey(Path keyPath) throws IOException {
        byte[] obfuscatedKey = Files.readAllBytes(keyPath);
        byte[] keyBytes = deobfuscateKey(obfuscatedKey);
        this.encryptionKey = new SecretKeySpec(keyBytes, ALGORITHM);
    }
    
    /**
     * Simple obfuscation of the key bytes to prevent casual viewing.
     * Note: This is not cryptographically secure, just prevents casual inspection.
     * 
     * @param keyBytes The key bytes to obfuscate
     * @return The obfuscated key bytes
     */
    private byte[] obfuscateKey(byte[] keyBytes) {
        byte[] obfuscated = new byte[keyBytes.length];
        byte xorKey = (byte) 0xAB; // Simple XOR key
        
        for (int i = 0; i < keyBytes.length; i++) {
            obfuscated[i] = (byte) (keyBytes[i] ^ xorKey ^ (i & 0xFF));
        }
        
        return obfuscated;
    }
    
    /**
     * Deobfuscates the key bytes.
     * 
     * @param obfuscatedKey The obfuscated key bytes
     * @return The original key bytes
     */
    private byte[] deobfuscateKey(byte[] obfuscatedKey) {
        byte[] keyBytes = new byte[obfuscatedKey.length];
        byte xorKey = (byte) 0xAB; // Same XOR key used for obfuscation
        
        for (int i = 0; i < obfuscatedKey.length; i++) {
            keyBytes[i] = (byte) (obfuscatedKey[i] ^ xorKey ^ (i & 0xFF));
        }
        
        return keyBytes;
    }
    
    /**
     * Gets the path to the encryption key file.
     * 
     * @return The key file path
     */
    private Path getKeyFilePath() {
        return Paths.get(".").resolve(EconomyConfig.ECONOMY_DATA_FOLDER).resolve(KEY_FILE_NAME);
    }
    
    /**
     * Rotates the encryption key (generates a new one).
     * This should be done periodically for security.
     * 
     * @throws Exception if key rotation fails
     */
    public void rotateKey() throws Exception {
        if (!initialized) {
            throw new IllegalStateException("Encryption manager not initialized");
        }
        
        Pokecobbleclaim.LOGGER.info("Starting encryption key rotation...");
        
        // Generate new key
        SecretKey oldKey = this.encryptionKey;
        generateKey();
        
        // Save new key
        Path keyPath = getKeyFilePath();
        saveKey(keyPath);
        
        // Clear old key from memory
        if (oldKey != null) {
            Arrays.fill(oldKey.getEncoded(), (byte) 0);
        }
        
        Pokecobbleclaim.LOGGER.info("Encryption key rotation completed");
    }
    
    /**
     * Validates that the encryption system is working correctly.
     * 
     * @return true if validation passes, false otherwise
     */
    public boolean validateEncryption() {
        if (!initialized) {
            return false;
        }
        
        try {
            // Test encryption/decryption with sample data
            String testData = "Economy encryption test data";
            byte[] plaintext = testData.getBytes("UTF-8");
            
            // Encrypt
            byte[] encrypted = encrypt(plaintext);
            
            // Decrypt
            byte[] decrypted = decrypt(encrypted);
            
            // Verify
            String decryptedString = new String(decrypted, "UTF-8");
            boolean isValid = testData.equals(decryptedString);
            
            if (isValid) {
                Pokecobbleclaim.LOGGER.debug("Encryption validation passed");
            } else {
                Pokecobbleclaim.LOGGER.error("Encryption validation failed: data mismatch");
            }
            
            return isValid;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Encryption validation failed with exception", e);
            return false;
        }
    }
    
    /**
     * Securely clears the encryption key from memory.
     */
    public void clearKey() {
        if (encryptionKey != null) {
            Arrays.fill(encryptionKey.getEncoded(), (byte) 0);
            encryptionKey = null;
        }
        initialized = false;
    }
    
    /**
     * Checks if the encryption manager is initialized.
     * 
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}
