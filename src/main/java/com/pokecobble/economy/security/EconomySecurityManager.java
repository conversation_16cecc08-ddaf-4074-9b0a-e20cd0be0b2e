package com.pokecobble.economy.security;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.EconomyConfig;
import com.pokecobble.economy.core.PlayerEconomyData;
import com.pokecobble.economy.core.SecurityFlags;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Manages security aspects of the economy system including monitoring,
 * threat detection, and automated responses to suspicious activity.
 */
public class EconomySecurityManager {
    
    // Security monitoring data
    private final Map<UUID, SecurityProfile> playerSecurityProfiles;
    private final Map<String, Long> suspiciousActivityLog;
    private final Set<UUID> watchedPlayers;
    
    // Threat detection
    private final ThreatDetector threatDetector;
    private final AnomalyDetector anomalyDetector;
    
    // Monitoring scheduler
    private ScheduledExecutorService securityScheduler;
    
    private boolean initialized;
    
    /**
     * Creates a new economy security manager.
     */
    public EconomySecurityManager() {
        this.playerSecurityProfiles = new ConcurrentHashMap<>();
        this.suspiciousActivityLog = new ConcurrentHashMap<>();
        this.watchedPlayers = ConcurrentHashMap.newKeySet();
        this.threatDetector = new ThreatDetector();
        this.anomalyDetector = new AnomalyDetector();
        this.initialized = false;
    }
    
    /**
     * Initializes the security manager.
     */
    public void initialize() {
        if (initialized) {
            return;
        }
        
        try {
            // Initialize components
            threatDetector.initialize();
            anomalyDetector.initialize();
            
            // Start security monitoring if enabled
            if (EconomyConfig.ENABLE_SECURITY_MONITORING) {
                startSecurityMonitoring();
            }
            
            this.initialized = true;
            Pokecobbleclaim.LOGGER.info("Economy security manager initialized successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize economy security manager", e);
            throw new RuntimeException("Economy security manager initialization failed", e);
        }
    }
    
    /**
     * Shuts down the security manager.
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        try {
            // Shutdown monitoring scheduler
            if (securityScheduler != null && !securityScheduler.isShutdown()) {
                securityScheduler.shutdown();
                if (!securityScheduler.awaitTermination(30, TimeUnit.SECONDS)) {
                    securityScheduler.shutdownNow();
                }
            }
            
            // Clear data
            playerSecurityProfiles.clear();
            suspiciousActivityLog.clear();
            watchedPlayers.clear();
            
            this.initialized = false;
            Pokecobbleclaim.LOGGER.info("Economy security manager shut down successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during economy security manager shutdown", e);
        }
    }
    
    /**
     * Validates a player's security status before allowing transactions.
     * 
     * @param playerId The player's UUID
     * @param playerData The player's economy data
     * @return The security validation result
     */
    public SecurityValidationResult validatePlayerSecurity(UUID playerId, PlayerEconomyData playerData) {
        if (!initialized) {
            return SecurityValidationResult.allow("Security manager not initialized");
        }
        
        try {
            // Check if player is frozen
            SecurityFlags securityFlags = playerData.getSecurityFlags();
            if (securityFlags.isFrozen()) {
                return SecurityValidationResult.deny("Account is frozen: " + securityFlags.getAutoFreezeReason());
            }
            
            // Check if player is flagged for review
            if (securityFlags.isFlagged()) {
                return SecurityValidationResult.warn("Account is flagged for review");
            }
            
            // Get or create security profile
            SecurityProfile profile = getOrCreateSecurityProfile(playerId);
            
            // Check for suspicious patterns
            if (profile.hasSuspiciousPatterns()) {
                recordSuspiciousActivity(playerId, "Suspicious transaction patterns detected");
                return SecurityValidationResult.warn("Suspicious activity patterns detected");
            }
            
            // Check threat level
            ThreatLevel threatLevel = threatDetector.assessThreatLevel(playerId, playerData);
            if (threatLevel == ThreatLevel.HIGH) {
                return SecurityValidationResult.deny("High threat level detected");
            } else if (threatLevel == ThreatLevel.MEDIUM) {
                return SecurityValidationResult.warn("Medium threat level detected");
            }
            
            // Check for anomalies
            if (anomalyDetector.detectAnomalies(playerId, playerData)) {
                return SecurityValidationResult.warn("Anomalous behavior detected");
            }
            
            return SecurityValidationResult.allow("Security validation passed");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during security validation for player " + playerId, e);
            return SecurityValidationResult.allow("Security validation error - allowing transaction");
        }
    }
    
    /**
     * Records suspicious activity for a player.
     * 
     * @param playerId The player's UUID
     * @param description Description of the suspicious activity
     */
    public void recordSuspiciousActivity(UUID playerId, String description) {
        if (!initialized) {
            return;
        }
        
        try {
            // Log the activity
            String logKey = playerId + ":" + System.currentTimeMillis();
            suspiciousActivityLog.put(logKey, System.currentTimeMillis());
            
            // Update security profile
            SecurityProfile profile = getOrCreateSecurityProfile(playerId);
            profile.recordSuspiciousActivity(description);
            
            // Add to watched players
            watchedPlayers.add(playerId);
            
            Pokecobbleclaim.LOGGER.warn("Suspicious activity recorded for player " + playerId + ": " + description);
            
            // Check if automatic action is needed
            if (profile.getSuspiciousActivityCount() >= 5) {
                // Auto-flag for review
                Pokecobbleclaim.LOGGER.warn("Auto-flagging player " + playerId + " due to excessive suspicious activity");
            }
            
            if (profile.getSuspiciousActivityCount() >= 10) {
                // Consider auto-freeze (would need to be implemented in the calling code)
                Pokecobbleclaim.LOGGER.error("Player " + playerId + " has excessive suspicious activity - consider manual review");
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error recording suspicious activity for player " + playerId, e);
        }
    }
    
    /**
     * Monitors a player's transaction for security issues.
     * 
     * @param player The player entity
     * @param transactionAmount The transaction amount
     * @param transactionType The type of transaction
     */
    public void monitorTransaction(ServerPlayerEntity player, long transactionAmount, String transactionType) {
        if (!initialized || player == null) {
            return;
        }
        
        UUID playerId = player.getUuid();
        SecurityProfile profile = getOrCreateSecurityProfile(playerId);
        
        // Record transaction for pattern analysis
        profile.recordTransaction(transactionAmount, transactionType);
        
        // Check for suspicious patterns
        if (isTransactionSuspicious(profile, transactionAmount, transactionType)) {
            recordSuspiciousActivity(playerId, "Suspicious transaction: " + transactionType + " amount: " + transactionAmount);
        }
        
        // Update last activity time
        profile.updateLastActivity();
    }
    
    /**
     * Checks if a transaction appears suspicious based on patterns.
     * 
     * @param profile The player's security profile
     * @param amount The transaction amount
     * @param type The transaction type
     * @return true if suspicious, false otherwise
     */
    private boolean isTransactionSuspicious(SecurityProfile profile, long amount, String type) {
        // Check for unusually large transactions
        if (amount > EconomyConfig.MAX_TRANSACTION_AMOUNT * 0.8) {
            return true;
        }
        
        // Check for rapid repeated transactions
        if (profile.getRecentTransactionCount(60000) > 20) { // More than 20 in 1 minute
            return true;
        }
        
        // Check for unusual transaction patterns
        if (profile.hasUnusualPattern()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Gets or creates a security profile for a player.
     * 
     * @param playerId The player's UUID
     * @return The security profile
     */
    private SecurityProfile getOrCreateSecurityProfile(UUID playerId) {
        return playerSecurityProfiles.computeIfAbsent(playerId, k -> new SecurityProfile(playerId));
    }
    
    /**
     * Starts the security monitoring scheduler.
     */
    private void startSecurityMonitoring() {
        securityScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "EconomySecurityMonitor");
            t.setDaemon(true);
            return t;
        });
        
        // Schedule periodic security checks
        securityScheduler.scheduleAtFixedRate(
            this::performSecurityCheck,
            EconomyConfig.MONITORING_INTERVAL_MS,
            EconomyConfig.MONITORING_INTERVAL_MS,
            TimeUnit.MILLISECONDS
        );
        
        Pokecobbleclaim.LOGGER.info("Security monitoring started (interval: " + 
                                  EconomyConfig.MONITORING_INTERVAL_MS + "ms)");
    }
    
    /**
     * Performs periodic security checks.
     */
    private void performSecurityCheck() {
        try {
            // Clean up old suspicious activity logs
            cleanupOldLogs();
            
            // Check watched players
            checkWatchedPlayers();
            
            // Update threat assessments
            updateThreatAssessments();
            
            // Log security statistics
            if (EconomyConfig.VERBOSE_LOGGING) {
                logSecurityStatistics();
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during security check", e);
        }
    }
    
    /**
     * Cleans up old suspicious activity logs.
     */
    private void cleanupOldLogs() {
        long cutoffTime = System.currentTimeMillis() - 86400000L; // 24 hours
        suspiciousActivityLog.entrySet().removeIf(entry -> entry.getValue() < cutoffTime);
    }
    
    /**
     * Checks watched players for continued suspicious activity.
     */
    private void checkWatchedPlayers() {
        for (UUID playerId : new HashSet<>(watchedPlayers)) {
            SecurityProfile profile = playerSecurityProfiles.get(playerId);
            if (profile != null) {
                // Remove from watch list if no recent suspicious activity
                if (profile.getTimeSinceLastSuspiciousActivity() > 3600000L) { // 1 hour
                    watchedPlayers.remove(playerId);
                }
            }
        }
    }
    
    /**
     * Updates threat assessments for all players.
     */
    private void updateThreatAssessments() {
        // This would typically involve more complex analysis
        // For now, just update the threat detector
        threatDetector.updateThreatAssessments();
    }
    
    /**
     * Logs security statistics.
     */
    private void logSecurityStatistics() {
        int totalProfiles = playerSecurityProfiles.size();
        int watchedCount = watchedPlayers.size();
        int suspiciousLogs = suspiciousActivityLog.size();
        
        Pokecobbleclaim.LOGGER.debug("Security Statistics - Profiles: " + totalProfiles + 
                                   ", Watched: " + watchedCount + 
                                   ", Suspicious Logs: " + suspiciousLogs);
    }
    
    /**
     * Gets security statistics.
     * 
     * @return A map of security statistics
     */
    public Map<String, Object> getSecurityStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("initialized", initialized);
        stats.put("total_profiles", playerSecurityProfiles.size());
        stats.put("watched_players", watchedPlayers.size());
        stats.put("suspicious_activity_logs", suspiciousActivityLog.size());
        
        // Count flagged and frozen accounts
        long flaggedCount = playerSecurityProfiles.values().stream()
                .mapToLong(profile -> profile.isFlagged() ? 1 : 0)
                .sum();
        stats.put("flagged_accounts", flaggedCount);
        
        return stats;
    }
    
    /**
     * Manually flags a player for review.
     * 
     * @param playerId The player's UUID
     * @param reason The reason for flagging
     */
    public void flagPlayer(UUID playerId, String reason) {
        if (!initialized) {
            return;
        }
        
        SecurityProfile profile = getOrCreateSecurityProfile(playerId);
        profile.setFlagged(true, reason);
        watchedPlayers.add(playerId);
        
        Pokecobbleclaim.LOGGER.info("Player " + playerId + " flagged for review: " + reason);
    }
    
    /**
     * Manually unflag a player.
     * 
     * @param playerId The player's UUID
     */
    public void unflagPlayer(UUID playerId) {
        if (!initialized) {
            return;
        }
        
        SecurityProfile profile = playerSecurityProfiles.get(playerId);
        if (profile != null) {
            profile.setFlagged(false, null);
            watchedPlayers.remove(playerId);
            Pokecobbleclaim.LOGGER.info("Player " + playerId + " unflagged");
        }
    }
    
    /**
     * Gets the threat detector.
     * 
     * @return The threat detector
     */
    public ThreatDetector getThreatDetector() {
        return threatDetector;
    }
    
    /**
     * Gets the anomaly detector.
     * 
     * @return The anomaly detector
     */
    public AnomalyDetector getAnomalyDetector() {
        return anomalyDetector;
    }
    
    /**
     * Checks if the security manager is initialized.
     * 
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}
