package com.pokecobble.economy.security;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.PlayerEconomyData;
import com.pokecobble.economy.core.TransactionRecord;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Detects anomalous behavior in player economy activities.
 * Uses statistical analysis to identify unusual patterns.
 */
public class AnomalyDetector {
    
    // Statistical baselines for normal behavior
    private final Map<String, StatisticalBaseline> behaviorBaselines;
    
    // Player behavior profiles
    private final Map<UUID, PlayerBehaviorProfile> playerProfiles;
    
    private boolean initialized;
    
    /**
     * Creates a new anomaly detector.
     */
    public AnomalyDetector() {
        this.behaviorBaselines = new ConcurrentHashMap<>();
        this.playerProfiles = new ConcurrentHashMap<>();
        this.initialized = false;
    }
    
    /**
     * Initializes the anomaly detector.
     */
    public void initialize() {
        if (initialized) {
            return;
        }
        
        // Initialize statistical baselines
        initializeBaselines();
        
        this.initialized = true;
        Pokecobbleclaim.LOGGER.info("Anomaly detector initialized");
    }
    
    /**
     * Detects anomalies in player behavior.
     * 
     * @param playerId The player's UUID
     * @param playerData The player's economy data
     * @return true if anomalies detected, false otherwise
     */
    public boolean detectAnomalies(UUID playerId, PlayerEconomyData playerData) {
        if (!initialized) {
            return false;
        }
        
        try {
            // Get or create player behavior profile
            PlayerBehaviorProfile profile = getOrCreateProfile(playerId, playerData);
            
            // Update profile with current data
            updateProfile(profile, playerData);
            
            // Check for various types of anomalies
            boolean hasAnomalies = false;
            
            // Balance anomalies
            if (detectBalanceAnomalies(profile, playerData)) {
                hasAnomalies = true;
            }
            
            // Transaction pattern anomalies
            if (detectTransactionPatternAnomalies(profile, playerData)) {
                hasAnomalies = true;
            }
            
            // Temporal anomalies
            if (detectTemporalAnomalies(profile, playerData)) {
                hasAnomalies = true;
            }
            
            // Behavioral anomalies
            if (detectBehavioralAnomalies(profile, playerData)) {
                hasAnomalies = true;
            }
            
            return hasAnomalies;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error detecting anomalies for player " + playerId, e);
            return false;
        }
    }
    
    /**
     * Detects balance-related anomalies.
     * 
     * @param profile The player's behavior profile
     * @param playerData The player's economy data
     * @return true if anomalies detected, false otherwise
     */
    private boolean detectBalanceAnomalies(PlayerBehaviorProfile profile, PlayerEconomyData playerData) {
        long currentBalance = playerData.getBalance();
        
        // Check against player's historical balance range
        if (profile.getBalanceHistory().size() > 10) {
            double avgBalance = profile.getAverageBalance();
            double stdDev = profile.getBalanceStandardDeviation();
            
            // If current balance is more than 3 standard deviations from average
            if (Math.abs(currentBalance - avgBalance) > 3 * stdDev && stdDev > 0) {
                Pokecobbleclaim.LOGGER.debug("Balance anomaly detected for player " + profile.getPlayerId() + 
                                           ": current=" + currentBalance + ", avg=" + avgBalance + ", stdDev=" + stdDev);
                return true;
            }
        }
        
        // Check against global baseline
        StatisticalBaseline balanceBaseline = behaviorBaselines.get("BALANCE");
        if (balanceBaseline != null && balanceBaseline.isOutlier(currentBalance)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Detects transaction pattern anomalies.
     * 
     * @param profile The player's behavior profile
     * @param playerData The player's economy data
     * @return true if anomalies detected, false otherwise
     */
    private boolean detectTransactionPatternAnomalies(PlayerBehaviorProfile profile, PlayerEconomyData playerData) {
        List<TransactionRecord> recentTransactions = playerData.getRecentTransactions();
        
        if (recentTransactions.size() < 5) {
            return false; // Not enough data
        }
        
        // Check transaction frequency
        long timeSpan = recentTransactions.get(0).getTimestamp() - 
                       recentTransactions.get(recentTransactions.size() - 1).getTimestamp();
        
        if (timeSpan > 0) {
            double transactionRate = (double) recentTransactions.size() / (timeSpan / 3600000.0); // per hour
            
            if (transactionRate > profile.getAverageTransactionRate() * 5) {
                Pokecobbleclaim.LOGGER.debug("Transaction rate anomaly detected for player " + profile.getPlayerId() + 
                                           ": rate=" + transactionRate + ", avg=" + profile.getAverageTransactionRate());
                return true;
            }
        }
        
        // Check transaction amounts
        List<Long> amounts = new ArrayList<>();
        for (TransactionRecord tx : recentTransactions) {
            amounts.add(tx.getAmount());
        }
        
        double avgAmount = amounts.stream().mapToLong(Long::longValue).average().orElse(0.0);
        double variance = amounts.stream()
                .mapToDouble(amount -> Math.pow(amount - avgAmount, 2))
                .average().orElse(0.0);
        double stdDev = Math.sqrt(variance);
        
        // Check for unusually consistent amounts (low variance)
        if (amounts.size() > 5 && stdDev < avgAmount * 0.1 && avgAmount > 100) {
            Pokecobbleclaim.LOGGER.debug("Consistent transaction amount anomaly detected for player " + profile.getPlayerId());
            return true;
        }
        
        return false;
    }
    
    /**
     * Detects temporal anomalies.
     * 
     * @param profile The player's behavior profile
     * @param playerData The player's economy data
     * @return true if anomalies detected, false otherwise
     */
    private boolean detectTemporalAnomalies(PlayerBehaviorProfile profile, PlayerEconomyData playerData) {
        List<TransactionRecord> recentTransactions = playerData.getRecentTransactions();
        
        if (recentTransactions.isEmpty()) {
            return false;
        }
        
        // Check for unusual activity times
        Map<Integer, Integer> hourlyActivity = new HashMap<>();
        for (TransactionRecord tx : recentTransactions) {
            Calendar cal = Calendar.getInstance();
            cal.setTimeInMillis(tx.getTimestamp());
            int hour = cal.get(Calendar.HOUR_OF_DAY);
            hourlyActivity.merge(hour, 1, Integer::sum);
        }
        
        // Check if most activity is during unusual hours (2 AM - 6 AM)
        int unusualHourActivity = 0;
        for (int hour = 2; hour <= 6; hour++) {
            unusualHourActivity += hourlyActivity.getOrDefault(hour, 0);
        }
        
        if (unusualHourActivity > recentTransactions.size() * 0.7) {
            Pokecobbleclaim.LOGGER.debug("Temporal anomaly detected for player " + profile.getPlayerId() + 
                                       ": unusual hour activity ratio=" + 
                                       ((double) unusualHourActivity / recentTransactions.size()));
            return true;
        }
        
        return false;
    }
    
    /**
     * Detects behavioral anomalies.
     * 
     * @param profile The player's behavior profile
     * @param playerData The player's economy data
     * @return true if anomalies detected, false otherwise
     */
    private boolean detectBehavioralAnomalies(PlayerBehaviorProfile profile, PlayerEconomyData playerData) {
        // Check spending/earning ratio
        long totalEarned = playerData.getTotalEarned();
        long totalSpent = playerData.getTotalSpent();
        
        if (totalEarned > 10000 && totalSpent == 0) {
            // High earner with no spending is unusual
            return true;
        }
        
        if (totalSpent > totalEarned * 2) {
            // Spending more than twice what was earned is suspicious
            return true;
        }
        
        // Check transaction type distribution
        List<TransactionRecord> recentTransactions = playerData.getRecentTransactions();
        Map<TransactionRecord.TransactionType, Integer> typeDistribution = new HashMap<>();
        
        for (TransactionRecord tx : recentTransactions) {
            typeDistribution.merge(tx.getType(), 1, Integer::sum);
        }
        
        // If 90% or more of transactions are of the same type, it's unusual
        for (Integer count : typeDistribution.values()) {
            if (count > recentTransactions.size() * 0.9 && recentTransactions.size() > 10) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Gets or creates a behavior profile for a player.
     * 
     * @param playerId The player's UUID
     * @param playerData The player's economy data
     * @return The behavior profile
     */
    private PlayerBehaviorProfile getOrCreateProfile(UUID playerId, PlayerEconomyData playerData) {
        return playerProfiles.computeIfAbsent(playerId, k -> new PlayerBehaviorProfile(playerId));
    }
    
    /**
     * Updates a player's behavior profile with current data.
     * 
     * @param profile The behavior profile
     * @param playerData The player's economy data
     */
    private void updateProfile(PlayerBehaviorProfile profile, PlayerEconomyData playerData) {
        profile.updateBalance(playerData.getBalance());
        profile.updateTransactionCount(playerData.getTransactionCount());
        profile.updateLastActivity(playerData.getLastUpdated());
    }
    
    /**
     * Initializes statistical baselines for normal behavior.
     */
    private void initializeBaselines() {
        // Initialize balance baseline (these would typically be learned from data)
        StatisticalBaseline balanceBaseline = new StatisticalBaseline("BALANCE");
        balanceBaseline.setMean(5000.0);
        balanceBaseline.setStandardDeviation(10000.0);
        behaviorBaselines.put("BALANCE", balanceBaseline);
        
        // Initialize transaction rate baseline
        StatisticalBaseline rateBaseline = new StatisticalBaseline("TRANSACTION_RATE");
        rateBaseline.setMean(2.0); // 2 transactions per hour
        rateBaseline.setStandardDeviation(3.0);
        behaviorBaselines.put("TRANSACTION_RATE", rateBaseline);
    }
    
    /**
     * Gets statistics about anomaly detection.
     * 
     * @return A map of statistics
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("initialized", initialized);
        stats.put("tracked_profiles", playerProfiles.size());
        stats.put("baselines", behaviorBaselines.size());
        
        return stats;
    }
    
    /**
     * Checks if the anomaly detector is initialized.
     * 
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}

/**
 * Represents statistical baseline for normal behavior.
 */
class StatisticalBaseline {
    private final String name;
    private double mean;
    private double standardDeviation;
    private double outlierThreshold;
    
    public StatisticalBaseline(String name) {
        this.name = name;
        this.outlierThreshold = 3.0; // 3 standard deviations
    }
    
    public void setMean(double mean) { this.mean = mean; }
    public void setStandardDeviation(double standardDeviation) { this.standardDeviation = standardDeviation; }
    
    public boolean isOutlier(double value) {
        if (standardDeviation == 0) return false;
        return Math.abs(value - mean) > outlierThreshold * standardDeviation;
    }
    
    public String getName() { return name; }
    public double getMean() { return mean; }
    public double getStandardDeviation() { return standardDeviation; }
}

/**
 * Tracks behavior patterns for a specific player.
 */
class PlayerBehaviorProfile {
    private final UUID playerId;
    private final List<Long> balanceHistory;
    private final List<Long> activityTimestamps;
    private int lastTransactionCount;
    
    public PlayerBehaviorProfile(UUID playerId) {
        this.playerId = playerId;
        this.balanceHistory = new ArrayList<>();
        this.activityTimestamps = new ArrayList<>();
        this.lastTransactionCount = 0;
    }
    
    public void updateBalance(long balance) {
        balanceHistory.add(balance);
        // Keep only recent history
        if (balanceHistory.size() > 100) {
            balanceHistory.remove(0);
        }
    }
    
    public void updateTransactionCount(int count) {
        this.lastTransactionCount = count;
    }
    
    public void updateLastActivity(long timestamp) {
        activityTimestamps.add(timestamp);
        // Keep only recent activity
        if (activityTimestamps.size() > 50) {
            activityTimestamps.remove(0);
        }
    }
    
    public double getAverageBalance() {
        return balanceHistory.stream().mapToLong(Long::longValue).average().orElse(0.0);
    }
    
    public double getBalanceStandardDeviation() {
        if (balanceHistory.size() < 2) return 0.0;
        
        double mean = getAverageBalance();
        double variance = balanceHistory.stream()
                .mapToDouble(balance -> Math.pow(balance - mean, 2))
                .average().orElse(0.0);
        return Math.sqrt(variance);
    }
    
    public double getAverageTransactionRate() {
        if (activityTimestamps.size() < 2) return 0.0;
        
        long timeSpan = activityTimestamps.get(activityTimestamps.size() - 1) - activityTimestamps.get(0);
        if (timeSpan <= 0) return 0.0;
        
        return (double) activityTimestamps.size() / (timeSpan / 3600000.0); // per hour
    }
    
    public UUID getPlayerId() { return playerId; }
    public List<Long> getBalanceHistory() { return new ArrayList<>(balanceHistory); }
}
