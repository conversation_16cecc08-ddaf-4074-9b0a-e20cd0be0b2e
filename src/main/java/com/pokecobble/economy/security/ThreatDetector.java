package com.pokecobble.economy.security;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.EconomyConfig;
import com.pokecobble.economy.core.PlayerEconomyData;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Detects various types of threats to the economy system.
 */
public class ThreatDetector {
    
    // Threat level cache
    private final Map<UUID, ThreatAssessment> threatAssessments;
    
    // Known threat patterns
    private final Set<String> knownThreatPatterns;
    
    private boolean initialized;
    
    /**
     * Creates a new threat detector.
     */
    public ThreatDetector() {
        this.threatAssessments = new ConcurrentHashMap<>();
        this.knownThreatPatterns = ConcurrentHashMap.newKeySet();
        this.initialized = false;
    }
    
    /**
     * Initializes the threat detector.
     */
    public void initialize() {
        if (initialized) {
            return;
        }
        
        // Initialize known threat patterns
        initializeKnownPatterns();
        
        this.initialized = true;
        Pokecobbleclaim.LOGGER.info("Threat detector initialized with " + knownThreatPatterns.size() + " known patterns");
    }
    
    /**
     * Assesses the threat level for a player.
     * 
     * @param playerId The player's UUID
     * @param playerData The player's economy data
     * @return The threat level
     */
    public ThreatLevel assessThreatLevel(UUID playerId, PlayerEconomyData playerData) {
        if (!initialized) {
            return ThreatLevel.LOW;
        }
        
        try {
            ThreatAssessment assessment = calculateThreatAssessment(playerId, playerData);
            threatAssessments.put(playerId, assessment);
            
            return assessment.getThreatLevel();
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error assessing threat level for player " + playerId, e);
            return ThreatLevel.LOW;
        }
    }
    
    /**
     * Calculates a comprehensive threat assessment.
     * 
     * @param playerId The player's UUID
     * @param playerData The player's economy data
     * @return The threat assessment
     */
    private ThreatAssessment calculateThreatAssessment(UUID playerId, PlayerEconomyData playerData) {
        ThreatAssessment assessment = new ThreatAssessment(playerId);
        
        // Check balance-related threats
        assessBalanceThreats(assessment, playerData);
        
        // Check transaction-related threats
        assessTransactionThreats(assessment, playerData);
        
        // Check security flag threats
        assessSecurityFlagThreats(assessment, playerData);
        
        // Check temporal threats
        assessTemporalThreats(assessment, playerData);
        
        // Calculate overall threat level
        assessment.calculateOverallThreatLevel();
        
        return assessment;
    }
    
    /**
     * Assesses balance-related threats.
     * 
     * @param assessment The threat assessment
     * @param playerData The player data
     */
    private void assessBalanceThreats(ThreatAssessment assessment, PlayerEconomyData playerData) {
        long balance = playerData.getBalance();
        
        // Extremely high balance
        if (balance > EconomyConfig.MAX_PLAYER_BALANCE * 0.9) {
            assessment.addThreat("EXTREME_BALANCE", ThreatLevel.HIGH, 
                               "Balance extremely high: " + balance);
        } else if (balance > EconomyConfig.MAX_PLAYER_BALANCE * 0.7) {
            assessment.addThreat("HIGH_BALANCE", ThreatLevel.MEDIUM, 
                               "Balance unusually high: " + balance);
        }
        
        // Suspicious balance patterns
        long totalEarned = playerData.getTotalEarned();
        long totalSpent = playerData.getTotalSpent();
        
        if (totalEarned > 0 && totalSpent == 0 && balance > 100000) {
            assessment.addThreat("NO_SPENDING", ThreatLevel.MEDIUM, 
                               "High balance with no spending activity");
        }
        
        // Rapid balance growth
        long accountAge = System.currentTimeMillis() - playerData.getCreatedAt();
        if (accountAge < 86400000L && balance > 50000) { // Less than 1 day old, high balance
            assessment.addThreat("RAPID_GROWTH", ThreatLevel.HIGH, 
                               "Rapid balance growth in new account");
        }
    }
    
    /**
     * Assesses transaction-related threats.
     * 
     * @param assessment The threat assessment
     * @param playerData The player data
     */
    private void assessTransactionThreats(ThreatAssessment assessment, PlayerEconomyData playerData) {
        int transactionCount = playerData.getTransactionCount();
        long totalEarned = playerData.getTotalEarned();
        
        // Unusual transaction patterns
        if (transactionCount > 1000 && totalEarned / transactionCount > 10000) {
            assessment.addThreat("HIGH_VALUE_TRANSACTIONS", ThreatLevel.MEDIUM, 
                               "Unusually high average transaction value");
        }
        
        // Check recent transactions for patterns
        List<com.pokecobble.economy.core.TransactionRecord> recentTransactions = 
            playerData.getRecentTransactions();
        
        if (recentTransactions.size() > 10) {
            // Check for duplicate amounts
            Map<Long, Integer> amountCounts = new HashMap<>();
            for (com.pokecobble.economy.core.TransactionRecord tx : recentTransactions) {
                amountCounts.merge(tx.getAmount(), 1, Integer::sum);
            }
            
            // If any amount appears more than 50% of the time
            int totalRecent = recentTransactions.size();
            for (Map.Entry<Long, Integer> entry : amountCounts.entrySet()) {
                if (entry.getValue() > totalRecent * 0.5) {
                    assessment.addThreat("REPETITIVE_AMOUNTS", ThreatLevel.MEDIUM, 
                                       "Repetitive transaction amounts detected");
                    break;
                }
            }
        }
    }
    
    /**
     * Assesses security flag threats.
     * 
     * @param assessment The threat assessment
     * @param playerData The player data
     */
    private void assessSecurityFlagThreats(ThreatAssessment assessment, PlayerEconomyData playerData) {
        com.pokecobble.economy.core.SecurityFlags securityFlags = playerData.getSecurityFlags();
        
        if (securityFlags.isFrozen()) {
            assessment.addThreat("ACCOUNT_FROZEN", ThreatLevel.HIGH, 
                               "Account is currently frozen");
        }
        
        if (securityFlags.isFlagged()) {
            assessment.addThreat("ACCOUNT_FLAGGED", ThreatLevel.MEDIUM, 
                               "Account is flagged for review");
        }
        
        if (securityFlags.getSuspiciousActivityCount() > 5) {
            assessment.addThreat("SUSPICIOUS_ACTIVITY", ThreatLevel.MEDIUM, 
                               "Multiple suspicious activities recorded");
        }
        
        if (securityFlags.getRateLimitViolations() > 3) {
            assessment.addThreat("RATE_LIMIT_VIOLATIONS", ThreatLevel.MEDIUM, 
                               "Multiple rate limit violations");
        }
        
        if (securityFlags.getDuplicateTransactionAttempts() > 0) {
            assessment.addThreat("DUPLICATE_ATTEMPTS", ThreatLevel.HIGH, 
                               "Duplicate transaction attempts detected");
        }
    }
    
    /**
     * Assesses temporal threats (time-based patterns).
     * 
     * @param assessment The threat assessment
     * @param playerData The player data
     */
    private void assessTemporalThreats(ThreatAssessment assessment, PlayerEconomyData playerData) {
        long currentTime = System.currentTimeMillis();
        long lastLogin = playerData.getLastLogin();
        long lastUpdated = playerData.getLastUpdated();
        
        // Account dormancy followed by sudden activity
        if (lastLogin > 0 && currentTime - lastLogin > 2592000000L) { // 30 days
            if (currentTime - lastUpdated < 3600000L) { // Activity in last hour
                assessment.addThreat("DORMANT_REACTIVATION", ThreatLevel.MEDIUM, 
                                   "Dormant account suddenly active");
            }
        }
        
        // Off-hours activity (this would need server timezone configuration)
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(lastUpdated);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        
        if (hour >= 2 && hour <= 6) { // 2 AM to 6 AM
            assessment.addThreat("OFF_HOURS_ACTIVITY", ThreatLevel.LOW, 
                               "Activity during unusual hours");
        }
    }
    
    /**
     * Initializes known threat patterns.
     */
    private void initializeKnownPatterns() {
        // Add known suspicious patterns
        knownThreatPatterns.add("RAPID_BALANCE_INCREASE");
        knownThreatPatterns.add("DUPLICATE_TRANSACTION_PATTERN");
        knownThreatPatterns.add("UNUSUAL_TRANSACTION_TIMING");
        knownThreatPatterns.add("SUSPICIOUS_TRANSFER_PATTERN");
        knownThreatPatterns.add("AUTOMATED_BEHAVIOR");
        knownThreatPatterns.add("MONEY_LAUNDERING_PATTERN");
    }
    
    /**
     * Updates threat assessments for all tracked players.
     */
    public void updateThreatAssessments() {
        // Clean up old assessments
        long cutoffTime = System.currentTimeMillis() - 3600000L; // 1 hour
        threatAssessments.entrySet().removeIf(entry -> 
            entry.getValue().getTimestamp() < cutoffTime);
    }
    
    /**
     * Gets the current threat assessment for a player.
     * 
     * @param playerId The player's UUID
     * @return The threat assessment, or null if not available
     */
    public ThreatAssessment getThreatAssessment(UUID playerId) {
        return threatAssessments.get(playerId);
    }
    
    /**
     * Gets statistics about threat detection.
     * 
     * @return A map of statistics
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("initialized", initialized);
        stats.put("tracked_assessments", threatAssessments.size());
        stats.put("known_patterns", knownThreatPatterns.size());
        
        // Count threat levels
        long highThreats = threatAssessments.values().stream()
                .filter(assessment -> assessment.getThreatLevel() == ThreatLevel.HIGH)
                .count();
        long mediumThreats = threatAssessments.values().stream()
                .filter(assessment -> assessment.getThreatLevel() == ThreatLevel.MEDIUM)
                .count();
        
        stats.put("high_threat_players", highThreats);
        stats.put("medium_threat_players", mediumThreats);
        
        return stats;
    }
    
    /**
     * Checks if the threat detector is initialized.
     * 
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}

/**
 * Represents different threat levels.
 */
enum ThreatLevel {
    LOW,
    MEDIUM,
    HIGH
}

/**
 * Represents a comprehensive threat assessment for a player.
 */
class ThreatAssessment {
    private final UUID playerId;
    private final long timestamp;
    private final List<ThreatIndicator> threats;
    private ThreatLevel overallThreatLevel;
    
    public ThreatAssessment(UUID playerId) {
        this.playerId = playerId;
        this.timestamp = System.currentTimeMillis();
        this.threats = new ArrayList<>();
        this.overallThreatLevel = ThreatLevel.LOW;
    }
    
    public void addThreat(String type, ThreatLevel level, String description) {
        threats.add(new ThreatIndicator(type, level, description));
    }
    
    public void calculateOverallThreatLevel() {
        if (threats.isEmpty()) {
            overallThreatLevel = ThreatLevel.LOW;
            return;
        }
        
        boolean hasHigh = threats.stream().anyMatch(t -> t.level == ThreatLevel.HIGH);
        boolean hasMedium = threats.stream().anyMatch(t -> t.level == ThreatLevel.MEDIUM);
        
        if (hasHigh) {
            overallThreatLevel = ThreatLevel.HIGH;
        } else if (hasMedium) {
            overallThreatLevel = ThreatLevel.MEDIUM;
        } else {
            overallThreatLevel = ThreatLevel.LOW;
        }
    }
    
    public UUID getPlayerId() { return playerId; }
    public long getTimestamp() { return timestamp; }
    public List<ThreatIndicator> getThreats() { return new ArrayList<>(threats); }
    public ThreatLevel getThreatLevel() { return overallThreatLevel; }
    
    private static class ThreatIndicator {
        final String type;
        final ThreatLevel level;
        final String description;
        
        ThreatIndicator(String type, ThreatLevel level, String description) {
            this.type = type;
            this.level = level;
            this.description = description;
        }
    }
}
