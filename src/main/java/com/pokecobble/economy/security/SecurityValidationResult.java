package com.pokecobble.economy.security;

/**
 * Represents the result of a security validation check.
 */
public class SecurityValidationResult {
    
    /**
     * Security validation status.
     */
    public enum Status {
        ALLOW,      // Transaction is allowed
        WARN,       // Transaction is allowed but flagged for monitoring
        DENY        // Transaction is denied
    }
    
    private final Status status;
    private final String message;
    private final long timestamp;
    
    /**
     * Creates a new security validation result.
     * 
     * @param status The validation status
     * @param message The validation message
     */
    private SecurityValidationResult(Status status, String message) {
        this.status = status;
        this.message = message;
        this.timestamp = System.currentTimeMillis();
    }
    
    /**
     * Creates an allow result.
     * 
     * @param message The message
     * @return The validation result
     */
    public static SecurityValidationResult allow(String message) {
        return new SecurityValidationResult(Status.ALLOW, message);
    }
    
    /**
     * Creates a warn result.
     * 
     * @param message The message
     * @return The validation result
     */
    public static SecurityValidationResult warn(String message) {
        return new SecurityValidationResult(Status.WARN, message);
    }
    
    /**
     * Creates a deny result.
     * 
     * @param message The message
     * @return The validation result
     */
    public static SecurityValidationResult deny(String message) {
        return new SecurityValidationResult(Status.DENY, message);
    }
    
    /**
     * Gets the validation status.
     * 
     * @return The status
     */
    public Status getStatus() {
        return status;
    }
    
    /**
     * Gets the validation message.
     * 
     * @return The message
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * Gets the validation timestamp.
     * 
     * @return The timestamp
     */
    public long getTimestamp() {
        return timestamp;
    }
    
    /**
     * Checks if the validation allows the transaction.
     * 
     * @return true if allowed, false otherwise
     */
    public boolean isAllowed() {
        return status == Status.ALLOW || status == Status.WARN;
    }
    
    /**
     * Checks if the validation denies the transaction.
     * 
     * @return true if denied, false otherwise
     */
    public boolean isDenied() {
        return status == Status.DENY;
    }
    
    /**
     * Checks if the validation is a warning.
     * 
     * @return true if warning, false otherwise
     */
    public boolean isWarning() {
        return status == Status.WARN;
    }
    
    @Override
    public String toString() {
        return "SecurityValidationResult{" +
                "status=" + status +
                ", message='" + message + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
