package com.pokecobble.economy.security;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.economy.core.EconomyConfig;
import com.pokecobble.economy.core.PlayerEconomyData;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * Handles data integrity checking using checksums.
 * Ensures that economy data has not been corrupted or tampered with.
 */
public class IntegrityChecker {
    
    private MessageDigest messageDigest;
    private boolean initialized;
    
    /**
     * Creates a new integrity checker.
     */
    public IntegrityChecker() {
        this.initialized = false;
    }
    
    /**
     * Initializes the integrity checker.
     */
    public void initialize() {
        if (initialized) {
            return;
        }
        
        try {
            this.messageDigest = MessageDigest.getInstance(EconomyConfig.CHECKSUM_ALGORITHM);
            this.initialized = true;
            Pokecobbleclaim.LOGGER.info("Integrity checker initialized with algorithm: " + EconomyConfig.CHECKSUM_ALGORITHM);
            
        } catch (NoSuchAlgorithmException e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize integrity checker", e);
            throw new RuntimeException("Integrity checker initialization failed", e);
        }
    }
    
    /**
     * Calculates a checksum for player economy data.
     * 
     * @param playerData The player data to calculate checksum for
     * @return The calculated checksum as a hex string
     */
    public String calculateChecksum(PlayerEconomyData playerData) {
        if (!initialized) {
            throw new IllegalStateException("Integrity checker not initialized");
        }
        
        try {
            // Create a string representation of the critical data
            StringBuilder dataBuilder = new StringBuilder();
            dataBuilder.append(playerData.getPlayerId().toString());
            dataBuilder.append("|");
            dataBuilder.append(playerData.getPlayerName());
            dataBuilder.append("|");
            dataBuilder.append(playerData.getBalance());
            dataBuilder.append("|");
            dataBuilder.append(playerData.getTotalEarned());
            dataBuilder.append("|");
            dataBuilder.append(playerData.getTotalSpent());
            dataBuilder.append("|");
            dataBuilder.append(playerData.getTransactionCount());
            dataBuilder.append("|");
            dataBuilder.append(playerData.getDataVersion());
            dataBuilder.append("|");
            dataBuilder.append(playerData.getCreatedAt());
            
            // Calculate hash
            byte[] dataBytes = dataBuilder.toString().getBytes(StandardCharsets.UTF_8);
            byte[] hashBytes = messageDigest.digest(dataBytes);
            
            // Convert to hex string
            return bytesToHex(hashBytes);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error calculating checksum for player data", e);
            return null;
        }
    }
    
    /**
     * Verifies the checksum of player economy data.
     * 
     * @param playerData The player data to verify
     * @return true if checksum is valid, false otherwise
     */
    public boolean verifyChecksum(PlayerEconomyData playerData) {
        if (!initialized) {
            throw new IllegalStateException("Integrity checker not initialized");
        }
        
        if (playerData == null) {
            return false;
        }
        
        String storedChecksum = playerData.getChecksum();
        if (storedChecksum == null || storedChecksum.isEmpty()) {
            // No checksum stored, consider it valid for backward compatibility
            return true;
        }
        
        String calculatedChecksum = calculateChecksum(playerData);
        if (calculatedChecksum == null) {
            return false;
        }
        
        boolean isValid = storedChecksum.equals(calculatedChecksum);
        
        if (!isValid) {
            Pokecobbleclaim.LOGGER.warn("Checksum verification failed for player " + playerData.getPlayerId() + 
                                      ". Stored: " + storedChecksum + ", Calculated: " + calculatedChecksum);
        }
        
        return isValid;
    }
    
    /**
     * Calculates a checksum for arbitrary data.
     * 
     * @param data The data to calculate checksum for
     * @return The calculated checksum as a hex string
     */
    public String calculateChecksum(byte[] data) {
        if (!initialized) {
            throw new IllegalStateException("Integrity checker not initialized");
        }
        
        try {
            byte[] hashBytes = messageDigest.digest(data);
            return bytesToHex(hashBytes);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error calculating checksum for data", e);
            return null;
        }
    }
    
    /**
     * Verifies a checksum against data.
     * 
     * @param data The data to verify
     * @param expectedChecksum The expected checksum
     * @return true if checksum matches, false otherwise
     */
    public boolean verifyChecksum(byte[] data, String expectedChecksum) {
        if (!initialized) {
            throw new IllegalStateException("Integrity checker not initialized");
        }
        
        if (data == null || expectedChecksum == null) {
            return false;
        }
        
        String calculatedChecksum = calculateChecksum(data);
        return expectedChecksum.equals(calculatedChecksum);
    }
    
    /**
     * Converts byte array to hexadecimal string.
     * 
     * @param bytes The byte array to convert
     * @return The hexadecimal string representation
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    /**
     * Converts hexadecimal string to byte array.
     * 
     * @param hex The hexadecimal string to convert
     * @return The byte array representation
     */
    private byte[] hexToBytes(String hex) {
        int length = hex.length();
        byte[] data = new byte[length / 2];
        for (int i = 0; i < length; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                                + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }
    
    /**
     * Validates the integrity of multiple player data objects.
     * 
     * @param playerDataArray Array of player data to validate
     * @return true if all data is valid, false otherwise
     */
    public boolean validateBatch(PlayerEconomyData[] playerDataArray) {
        if (!initialized) {
            throw new IllegalStateException("Integrity checker not initialized");
        }
        
        if (playerDataArray == null || playerDataArray.length == 0) {
            return true;
        }
        
        boolean allValid = true;
        int validCount = 0;
        int invalidCount = 0;
        
        for (PlayerEconomyData playerData : playerDataArray) {
            if (playerData != null) {
                if (verifyChecksum(playerData)) {
                    validCount++;
                } else {
                    invalidCount++;
                    allValid = false;
                    Pokecobbleclaim.LOGGER.warn("Invalid checksum detected for player: " + playerData.getPlayerId());
                }
            }
        }
        
        Pokecobbleclaim.LOGGER.info("Batch integrity check completed. Valid: " + validCount + ", Invalid: " + invalidCount);
        
        return allValid;
    }
    
    /**
     * Performs a comprehensive integrity check on player data.
     * This includes checksum verification and data consistency checks.
     * 
     * @param playerData The player data to check
     * @return true if all checks pass, false otherwise
     */
    public boolean comprehensiveCheck(PlayerEconomyData playerData) {
        if (!initialized) {
            throw new IllegalStateException("Integrity checker not initialized");
        }
        
        if (playerData == null) {
            return false;
        }
        
        try {
            // 1. Checksum verification
            if (!verifyChecksum(playerData)) {
                Pokecobbleclaim.LOGGER.warn("Checksum verification failed for player: " + playerData.getPlayerId());
                return false;
            }
            
            // 2. Data consistency checks
            if (playerData.getBalance() < EconomyConfig.MIN_PLAYER_BALANCE || 
                playerData.getBalance() > EconomyConfig.MAX_PLAYER_BALANCE) {
                Pokecobbleclaim.LOGGER.warn("Invalid balance for player: " + playerData.getPlayerId() + 
                                          " (Balance: " + playerData.getBalance() + ")");
                return false;
            }
            
            if (playerData.getTotalEarned() < 0 || playerData.getTotalSpent() < 0) {
                Pokecobbleclaim.LOGGER.warn("Invalid total earned/spent for player: " + playerData.getPlayerId());
                return false;
            }
            
            if (playerData.getTransactionCount() < 0) {
                Pokecobbleclaim.LOGGER.warn("Invalid transaction count for player: " + playerData.getPlayerId());
                return false;
            }
            
            if (playerData.getCreatedAt() <= 0 || playerData.getLastUpdated() <= 0) {
                Pokecobbleclaim.LOGGER.warn("Invalid timestamps for player: " + playerData.getPlayerId());
                return false;
            }
            
            if (playerData.getLastUpdated() < playerData.getCreatedAt()) {
                Pokecobbleclaim.LOGGER.warn("Last updated timestamp is before creation timestamp for player: " + playerData.getPlayerId());
                return false;
            }
            
            // 3. UUID validation
            if (playerData.getPlayerId() == null) {
                Pokecobbleclaim.LOGGER.warn("Null player ID detected");
                return false;
            }
            
            // 4. Name validation
            if (playerData.getPlayerName() == null || playerData.getPlayerName().trim().isEmpty()) {
                Pokecobbleclaim.LOGGER.warn("Invalid player name for player: " + playerData.getPlayerId());
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during comprehensive integrity check for player: " + playerData.getPlayerId(), e);
            return false;
        }
    }
    
    /**
     * Repairs player data by recalculating and updating the checksum.
     * 
     * @param playerData The player data to repair
     * @return true if repair was successful, false otherwise
     */
    public boolean repairChecksum(PlayerEconomyData playerData) {
        if (!initialized) {
            throw new IllegalStateException("Integrity checker not initialized");
        }
        
        if (playerData == null) {
            return false;
        }
        
        try {
            String newChecksum = calculateChecksum(playerData);
            if (newChecksum != null) {
                playerData.setChecksum(newChecksum);
                Pokecobbleclaim.LOGGER.info("Repaired checksum for player: " + playerData.getPlayerId());
                return true;
            }
            return false;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error repairing checksum for player: " + playerData.getPlayerId(), e);
            return false;
        }
    }
    
    /**
     * Checks if the integrity checker is initialized.
     * 
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}
