package com.pokecobble.economy.security;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * Tracks security-related information for a player.
 */
public class SecurityProfile {
    
    private final UUID playerId;
    private final long creationTime;
    
    // Activity tracking
    private long lastActivity;
    private final Queue<TransactionInfo> recentTransactions;
    private final List<String> suspiciousActivities;
    
    // Security status
    private boolean flagged;
    private String flagReason;
    private long flagTimestamp;
    
    // Statistics
    private int totalTransactions;
    private long totalAmountTransacted;
    private int suspiciousActivityCount;
    private long lastSuspiciousActivity;
    
    /**
     * Creates a new security profile.
     * 
     * @param playerId The player's UUID
     */
    public SecurityProfile(UUID playerId) {
        this.playerId = playerId;
        this.creationTime = System.currentTimeMillis();
        this.lastActivity = creationTime;
        this.recentTransactions = new ConcurrentLinkedQueue<>();
        this.suspiciousActivities = new ArrayList<>();
        this.flagged = false;
        this.totalTransactions = 0;
        this.totalAmountTransacted = 0L;
        this.suspiciousActivityCount = 0;
        this.lastSuspiciousActivity = 0L;
    }
    
    /**
     * Records a transaction for analysis.
     * 
     * @param amount The transaction amount
     * @param type The transaction type
     */
    public void recordTransaction(long amount, String type) {
        TransactionInfo info = new TransactionInfo(amount, type, System.currentTimeMillis());
        recentTransactions.offer(info);
        
        // Keep only recent transactions (last 100)
        while (recentTransactions.size() > 100) {
            recentTransactions.poll();
        }
        
        // Update statistics
        totalTransactions++;
        totalAmountTransacted += amount;
        updateLastActivity();
    }
    
    /**
     * Records suspicious activity.
     * 
     * @param description Description of the activity
     */
    public void recordSuspiciousActivity(String description) {
        suspiciousActivities.add(System.currentTimeMillis() + ": " + description);
        suspiciousActivityCount++;
        lastSuspiciousActivity = System.currentTimeMillis();
        
        // Keep only recent suspicious activities (last 50)
        if (suspiciousActivities.size() > 50) {
            suspiciousActivities.remove(0);
        }
    }
    
    /**
     * Updates the last activity timestamp.
     */
    public void updateLastActivity() {
        this.lastActivity = System.currentTimeMillis();
    }
    
    /**
     * Gets the number of recent transactions within a time window.
     * 
     * @param timeWindowMs The time window in milliseconds
     * @return The transaction count
     */
    public int getRecentTransactionCount(long timeWindowMs) {
        long cutoffTime = System.currentTimeMillis() - timeWindowMs;
        return (int) recentTransactions.stream()
                .filter(tx -> tx.timestamp > cutoffTime)
                .count();
    }
    
    /**
     * Checks if the player has suspicious patterns.
     * 
     * @return true if suspicious patterns detected, false otherwise
     */
    public boolean hasSuspiciousPatterns() {
        // Check for rapid transactions
        if (getRecentTransactionCount(60000) > 30) { // More than 30 in 1 minute
            return true;
        }
        
        // Check for unusual amounts
        if (hasUnusualTransactionAmounts()) {
            return true;
        }
        
        // Check for repetitive patterns
        if (hasRepetitivePatterns()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Checks for unusual transaction patterns.
     * 
     * @return true if unusual patterns detected, false otherwise
     */
    public boolean hasUnusualPattern() {
        // Check for alternating large/small transactions
        List<TransactionInfo> recent = new ArrayList<>(recentTransactions);
        if (recent.size() < 4) {
            return false;
        }
        
        // Sort by timestamp
        recent.sort(Comparator.comparing(tx -> tx.timestamp));
        
        // Check for alternating pattern
        boolean alternating = true;
        for (int i = 1; i < Math.min(recent.size(), 10); i++) {
            long current = recent.get(i).amount;
            long previous = recent.get(i - 1).amount;
            
            if (i % 2 == 1) {
                // Odd index - should be different pattern
                if (Math.abs(current - previous) < 100) {
                    alternating = false;
                    break;
                }
            }
        }
        
        return alternating && recent.size() >= 6;
    }
    
    /**
     * Checks for unusual transaction amounts.
     * 
     * @return true if unusual amounts detected, false otherwise
     */
    private boolean hasUnusualTransactionAmounts() {
        if (recentTransactions.size() < 5) {
            return false;
        }
        
        // Calculate average and check for outliers
        double average = recentTransactions.stream()
                .mapToLong(tx -> tx.amount)
                .average()
                .orElse(0.0);
        
        long outlierCount = recentTransactions.stream()
                .filter(tx -> Math.abs(tx.amount - average) > average * 5) // 5x deviation
                .count();
        
        return outlierCount > recentTransactions.size() * 0.3; // More than 30% outliers
    }
    
    /**
     * Checks for repetitive transaction patterns.
     * 
     * @return true if repetitive patterns detected, false otherwise
     */
    private boolean hasRepetitivePatterns() {
        if (recentTransactions.size() < 10) {
            return false;
        }
        
        // Check for repeated amounts
        Map<Long, Integer> amountCounts = new HashMap<>();
        for (TransactionInfo tx : recentTransactions) {
            amountCounts.merge(tx.amount, 1, Integer::sum);
        }
        
        // If any amount appears more than 50% of the time, it's suspicious
        int totalTransactions = recentTransactions.size();
        return amountCounts.values().stream()
                .anyMatch(count -> count > totalTransactions * 0.5);
    }
    
    /**
     * Gets the player ID.
     * 
     * @return The player ID
     */
    public UUID getPlayerId() {
        return playerId;
    }
    
    /**
     * Gets the creation time.
     * 
     * @return The creation time
     */
    public long getCreationTime() {
        return creationTime;
    }
    
    /**
     * Gets the last activity time.
     * 
     * @return The last activity time
     */
    public long getLastActivity() {
        return lastActivity;
    }
    
    /**
     * Gets the total number of transactions.
     * 
     * @return The total transactions
     */
    public int getTotalTransactions() {
        return totalTransactions;
    }
    
    /**
     * Gets the total amount transacted.
     * 
     * @return The total amount
     */
    public long getTotalAmountTransacted() {
        return totalAmountTransacted;
    }
    
    /**
     * Gets the suspicious activity count.
     * 
     * @return The suspicious activity count
     */
    public int getSuspiciousActivityCount() {
        return suspiciousActivityCount;
    }
    
    /**
     * Gets the time since last suspicious activity.
     * 
     * @return The time in milliseconds
     */
    public long getTimeSinceLastSuspiciousActivity() {
        if (lastSuspiciousActivity == 0L) {
            return Long.MAX_VALUE;
        }
        return System.currentTimeMillis() - lastSuspiciousActivity;
    }
    
    /**
     * Checks if the player is flagged.
     * 
     * @return true if flagged, false otherwise
     */
    public boolean isFlagged() {
        return flagged;
    }
    
    /**
     * Sets the flagged status.
     * 
     * @param flagged true to flag, false to unflag
     * @param reason The reason for flagging
     */
    public void setFlagged(boolean flagged, String reason) {
        this.flagged = flagged;
        this.flagReason = reason;
        this.flagTimestamp = flagged ? System.currentTimeMillis() : 0L;
    }
    
    /**
     * Gets the flag reason.
     * 
     * @return The flag reason
     */
    public String getFlagReason() {
        return flagReason;
    }
    
    /**
     * Gets the flag timestamp.
     * 
     * @return The flag timestamp
     */
    public long getFlagTimestamp() {
        return flagTimestamp;
    }
    
    /**
     * Gets the suspicious activities list.
     * 
     * @return The suspicious activities
     */
    public List<String> getSuspiciousActivities() {
        return new ArrayList<>(suspiciousActivities);
    }
    
    /**
     * Represents information about a transaction.
     */
    private static class TransactionInfo {
        final long amount;
        final String type;
        final long timestamp;
        
        TransactionInfo(long amount, String type, long timestamp) {
            this.amount = amount;
            this.type = type;
            this.timestamp = timestamp;
        }
    }
    
    @Override
    public String toString() {
        return "SecurityProfile{" +
                "playerId=" + playerId +
                ", totalTransactions=" + totalTransactions +
                ", suspiciousActivityCount=" + suspiciousActivityCount +
                ", flagged=" + flagged +
                '}';
    }
}
