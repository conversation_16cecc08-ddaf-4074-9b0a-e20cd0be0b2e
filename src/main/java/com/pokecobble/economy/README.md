# PokeCobbleClaim Economy System

A comprehensive, secure, and optimized player economy system for Minecraft servers with real-time synchronization and anti-duplication measures.

## Features

### 🔒 Security & Anti-Cheat
- **Encryption**: AES-GCM encryption for all stored data
- **Integrity Checking**: SHA-256 checksums for data validation
- **Duplicate Detection**: Prevents money duplication exploits
- **Rate Limiting**: Prevents transaction spam and abuse
- **Threat Detection**: AI-powered anomaly detection
- **Account Freezing**: Automatic security responses

### 💾 Data Storage
- **Server-side File Storage**: Secure file-based persistence
- **Atomic Operations**: Prevents data corruption
- **Automatic Backups**: Configurable backup system
- **Data Recovery**: Restore from backups on corruption
- **Compression**: Optional data compression

### 🌐 Network Synchronization
- **Real-time Sync**: Instant balance updates
- **Packet Authentication**: Secure network communication
- **Client Caching**: Efficient data synchronization
- **Retry Logic**: Reliable network operations

### 📊 Transaction Management
- **Full Logging**: Complete transaction audit trail
- **Rollback Support**: Transaction reversal capability
- **Validation**: Comprehensive input validation
- **History Tracking**: Per-player transaction history

## Architecture

```
economy/
├── api/                    # Public API for other mods
├── core/                   # Core data structures
├── data/                   # Data storage and persistence
├── manager/                # Main economy manager
├── network/                # Network synchronization
├── security/               # Security and anti-cheat
├── transaction/            # Transaction processing
└── command/                # Admin commands
```

## Quick Start

### Basic Usage

```java
// Get the economy API
EconomyAPI api = EconomyAPI.getInstance();

// Check if system is available
if (!api.isAvailable()) {
    return;
}

// Get player balance
long balance = api.getBalance(playerId);

// Add money
api.addMoney(playerId, 1000, "Reward for completing quest");

// Transfer money
api.transferMoney(fromPlayerId, toPlayerId, 500, "Payment for services");

// Check if player has enough money
if (api.hasEnoughMoney(playerId, 100)) {
    api.subtractMoney(playerId, 100, "Purchase item");
}
```

### Admin Commands

```
/economy balance [player]           - Check balance
/economy set <player> <amount>      - Set balance
/economy add <player> <amount>      - Add money
/economy remove <player> <amount>   - Remove money
/economy transfer <from> <to> <amount> - Transfer money
/economy info <player>              - Detailed player info
/economy stats                      - System statistics
```

## Configuration

The system is configured through `EconomyConfig.java`:

```java
// Balance limits
public static final long MAX_PLAYER_BALANCE = 10_000_000_000L;
public static final long DEFAULT_STARTING_BALANCE = 1000L;

// Security settings
public static final boolean ENABLE_ENCRYPTION = true;
public static final boolean ENABLE_DUPLICATE_DETECTION = true;
public static final int MAX_TRANSACTIONS_PER_MINUTE = 60;

// Backup settings
public static final boolean AUTO_BACKUP_ENABLED = true;
public static final int MAX_BACKUP_FILES = 10;
```

## Data Storage

### File Structure
```
economy/
├── players/                # Individual player data files
│   ├── uuid1.econ         # Encrypted player data
│   └── uuid2.econ
├── transactions/           # Transaction logs
│   ├── transactions_2024-01-01.log
│   └── transactions_2024-01-02.log
├── backups/               # Automatic backups
│   ├── uuid1/
│   │   ├── uuid1_2024-01-01_12-00-00.backup
│   │   └── uuid1_2024-01-01_13-00-00.backup
│   └── full_backup_2024-01-01_12-00-00/
└── economy.key           # Encryption key (keep secure!)
```

### Player Data Format
```json
{
  "player_id": "uuid",
  "player_name": "PlayerName",
  "balance": 5000,
  "total_earned": 10000,
  "total_spent": 5000,
  "transaction_count": 25,
  "created_at": 1640995200000,
  "last_updated": 1640995200000,
  "checksum": "sha256hash",
  "security_flags": {
    "is_frozen": false,
    "suspicious_activity_count": 0
  }
}
```

## Security Features

### Encryption
- All player data is encrypted using AES-GCM
- Unique encryption key generated per server
- IV (Initialization Vector) randomized per file

### Integrity Checking
- SHA-256 checksums for all data
- Verification on load and save
- Automatic corruption detection

### Anti-Duplication
- Transaction signature tracking
- Duplicate detection within time windows
- Automatic blocking of suspicious patterns

### Rate Limiting
- Per-player transaction limits
- Configurable time windows
- Automatic temporary restrictions

### Threat Detection
- Statistical analysis of player behavior
- Anomaly detection algorithms
- Automatic flagging and freezing

## Performance

### Optimizations
- Asynchronous I/O operations
- Efficient caching system
- Batch operations support
- Connection pooling

### Monitoring
- Performance metrics collection
- Memory usage tracking
- Transaction throughput monitoring
- Error rate tracking

## Integration

### With Other Mods
```java
// Check if economy is available
if (EconomyAPI.getInstance().isAvailable()) {
    // Use economy features
    long balance = EconomyAPI.getInstance().getBalance(playerId);
}
```

### Event Handling
```java
// Listen for economy events (if implemented)
EconomyEvents.BALANCE_CHANGED.register((playerId, oldBalance, newBalance) -> {
    // Handle balance change
});
```

## Troubleshooting

### Common Issues

1. **Economy system not available**
   - Check server logs for initialization errors
   - Verify file permissions in economy folder
   - Ensure no conflicting economy mods

2. **Data corruption**
   - System automatically attempts backup restoration
   - Check backup folder for recent backups
   - Verify disk space and permissions

3. **Performance issues**
   - Monitor transaction rate limits
   - Check for excessive backup creation
   - Review security monitoring settings

### Debug Commands
```
/economy stats              # System statistics
/economy info <player>      # Detailed player information
```

### Log Files
- Main log: Check server console for economy-related messages
- Transaction logs: `economy/transactions/` folder
- Error logs: Server log files with "Economy" prefix

## Development

### Adding New Features
1. Extend the appropriate manager class
2. Add API methods to `EconomyAPI`
3. Update network handlers if needed
4. Add security validation
5. Write tests

### Custom Transaction Types
```java
// Define new transaction type
public enum CustomTransactionType {
    SHOP_PURCHASE,
    AUCTION_SALE,
    LOTTERY_WIN
}

// Create custom transaction
TransactionRequest request = new TransactionRequest(
    CustomTransactionType.SHOP_PURCHASE,
    amount, fromPlayer, null, "Shop purchase"
);
```

## License

This economy system is part of the PokeCobbleClaim mod and follows the same license terms.

## Support

For issues, feature requests, or questions:
1. Check the troubleshooting section
2. Review server logs for error messages
3. Create an issue with detailed information
4. Include relevant log files and configuration
