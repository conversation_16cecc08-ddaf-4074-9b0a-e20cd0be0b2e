package com.pokecobble.economy.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.economy.api.EconomyAPI;
import com.pokecobble.economy.core.PlayerEconomyData;
import net.minecraft.command.argument.EntityArgumentType;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;

import java.util.Map;
import java.util.UUID;
import com.mojang.authlib.GameProfile;

/**
 * Commands for managing the economy system.
 * Supports both online and offline players.
 */
public class EconomyCommand {

    /**
     * Helper class to represent a player (online or offline).
     */
    private static class PlayerInfo {
        private final UUID uuid;
        private final String name;
        private final boolean isOnline;

        public PlayerInfo(UUID uuid, String name, boolean isOnline) {
            this.uuid = uuid;
            this.name = name;
            this.isOnline = isOnline;
        }

        public UUID getUuid() { return uuid; }
        public String getName() { return name; }
        public boolean isOnline() { return isOnline; }
    }
    
    /**
     * Registers economy commands.
     * 
     * @param dispatcher The command dispatcher
     */
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher) {
        dispatcher.register(CommandManager.literal("economy")
            .requires(source -> source.hasPermissionLevel(2)) // Require OP level
            .then(CommandManager.literal("balance")
                // Support online players
                .then(CommandManager.argument("player", EntityArgumentType.player())
                    .executes(EconomyCommand::getBalance))
                // Support offline players by name
                .then(CommandManager.argument("playername", StringArgumentType.string())
                    .executes(EconomyCommand::getBalanceByName))
                .executes(EconomyCommand::getSelfBalance))
            .then(CommandManager.literal("set")
                // Support online players
                .then(CommandManager.argument("player", EntityArgumentType.player())
                    .then(CommandManager.argument("amount", IntegerArgumentType.integer(0))
                        .executes(EconomyCommand::setBalance)))
                // Support offline players by name
                .then(CommandManager.argument("playername", StringArgumentType.string())
                    .then(CommandManager.argument("amount", IntegerArgumentType.integer(0))
                        .executes(EconomyCommand::setBalanceByName))))
            .then(CommandManager.literal("add")
                // Support online players
                .then(CommandManager.argument("player", EntityArgumentType.player())
                    .then(CommandManager.argument("amount", IntegerArgumentType.integer(1))
                        .executes(EconomyCommand::addMoney)))
                // Support offline players by name
                .then(CommandManager.argument("playername", StringArgumentType.string())
                    .then(CommandManager.argument("amount", IntegerArgumentType.integer(1))
                        .executes(EconomyCommand::addMoneyByName))))
            .then(CommandManager.literal("remove")
                // Support online players
                .then(CommandManager.argument("player", EntityArgumentType.player())
                    .then(CommandManager.argument("amount", IntegerArgumentType.integer(1))
                        .executes(EconomyCommand::removeMoney)))
                // Support offline players by name
                .then(CommandManager.argument("playername", StringArgumentType.string())
                    .then(CommandManager.argument("amount", IntegerArgumentType.integer(1))
                        .executes(EconomyCommand::removeMoneyByName))))
            .then(CommandManager.literal("transfer")
                // Support online players
                .then(CommandManager.argument("from", EntityArgumentType.player())
                    .then(CommandManager.argument("to", EntityArgumentType.player())
                        .then(CommandManager.argument("amount", IntegerArgumentType.integer(1))
                            .executes(EconomyCommand::transferMoney))))
                // Support mixed online/offline players
                .then(CommandManager.argument("fromname", StringArgumentType.string())
                    .then(CommandManager.argument("toname", StringArgumentType.string())
                        .then(CommandManager.argument("amount", IntegerArgumentType.integer(1))
                            .executes(EconomyCommand::transferMoneyByName)))))
            .then(CommandManager.literal("info")
                // Support online players
                .then(CommandManager.argument("player", EntityArgumentType.player())
                    .executes(EconomyCommand::getPlayerInfo))
                // Support offline players by name
                .then(CommandManager.argument("playername", StringArgumentType.string())
                    .executes(EconomyCommand::getPlayerInfoByName)))
            .then(CommandManager.literal("stats")
                .executes(EconomyCommand::getStats))
            .then(CommandManager.literal("reload")
                .executes(EconomyCommand::reload)));
    }
    
    /**
     * Resolves a player name to PlayerInfo (UUID and name).
     * Works for both online and offline players.
     */
    private static PlayerInfo resolvePlayer(CommandContext<ServerCommandSource> context, String playerName) {
        // First try to find online player
        try {
            ServerPlayerEntity onlinePlayer = context.getSource().getServer().getPlayerManager().getPlayer(playerName);
            if (onlinePlayer != null) {
                return new PlayerInfo(onlinePlayer.getUuid(), onlinePlayer.getName().getString(), true);
            }
        } catch (Exception e) {
            // Player not online, continue to offline lookup
        }

        // Try to find offline player using the user cache
        try {
            GameProfile profile = context.getSource().getServer().getUserCache().findByName(playerName).orElse(null);
            if (profile != null) {
                return new PlayerInfo(profile.getId(), profile.getName(), false);
            }
        } catch (Exception e) {
            // Failed to find player
        }

        return null;
    }

    /**
     * Gets a player's balance.
     */
    private static int getBalance(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity targetPlayer = EntityArgumentType.getPlayer(context, "player");
            EconomyAPI api = EconomyAPI.getInstance();
            
            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }
            
            long balance = api.getBalance(targetPlayer);
            String formattedBalance = api.formatMoney(balance);
            
            context.getSource().sendFeedback(() -> Text.literal(
                targetPlayer.getName().getString() + "'s balance: " + 
                api.getCurrencySymbol() + formattedBalance + " " + api.getCurrencyName()
            ), false);
            
            return 1;
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error getting balance: " + e.getMessage()));
            return 0;
        }
    }
    
    /**
     * Gets a player's balance by name (supports offline players).
     */
    private static int getBalanceByName(CommandContext<ServerCommandSource> context) {
        try {
            String playerName = StringArgumentType.getString(context, "playername");
            PlayerInfo playerInfo = resolvePlayer(context, playerName);

            if (playerInfo == null) {
                context.getSource().sendError(Text.literal("Player '" + playerName + "' not found"));
                return 0;
            }

            EconomyAPI api = EconomyAPI.getInstance();

            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }

            long balance = api.getBalance(playerInfo.getUuid());
            String formattedBalance = api.formatMoney(balance);

            context.getSource().sendFeedback(() -> Text.literal(
                playerInfo.getName() + "'s balance: " +
                api.getCurrencySymbol() + formattedBalance + " " + api.getCurrencyName() +
                (playerInfo.isOnline() ? " (online)" : " (offline)")
            ), false);

            return 1;
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error getting balance: " + e.getMessage()));
            return 0;
        }
    }

    /**
     * Gets the command sender's balance.
     */
    private static int getSelfBalance(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity player = context.getSource().getPlayerOrThrow();
            EconomyAPI api = EconomyAPI.getInstance();

            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }

            long balance = api.getBalance(player);
            String formattedBalance = api.formatMoney(balance);

            context.getSource().sendFeedback(() -> Text.literal(
                "Your balance: " + api.getCurrencySymbol() + formattedBalance + " " + api.getCurrencyName()
            ), false);

            return 1;
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error getting balance: " + e.getMessage()));
            return 0;
        }
    }
    
    /**
     * Sets a player's balance.
     */
    private static int setBalance(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity targetPlayer = EntityArgumentType.getPlayer(context, "player");
            int amount = IntegerArgumentType.getInteger(context, "amount");
            EconomyAPI api = EconomyAPI.getInstance();

            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }

            boolean success = api.setBalance(targetPlayer.getUuid(), amount, "Balance set by admin command");

            if (success) {
                String formattedAmount = api.formatMoney(amount);
                context.getSource().sendFeedback(() -> Text.literal(
                    "Set " + targetPlayer.getName().getString() + "'s balance to " +
                    api.getCurrencySymbol() + formattedAmount + " " + api.getCurrencyName()
                ), true);
                return 1;
            } else {
                context.getSource().sendError(Text.literal("Failed to set balance"));
                return 0;
            }
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error setting balance: " + e.getMessage()));
            return 0;
        }
    }

    /**
     * Sets a player's balance by name (supports offline players).
     */
    private static int setBalanceByName(CommandContext<ServerCommandSource> context) {
        try {
            String playerName = StringArgumentType.getString(context, "playername");
            int amount = IntegerArgumentType.getInteger(context, "amount");
            PlayerInfo playerInfo = resolvePlayer(context, playerName);

            if (playerInfo == null) {
                context.getSource().sendError(Text.literal("Player '" + playerName + "' not found"));
                return 0;
            }

            EconomyAPI api = EconomyAPI.getInstance();

            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }

            boolean success = api.setBalance(playerInfo.getUuid(), amount, "Balance set by admin command");

            if (success) {
                String formattedAmount = api.formatMoney(amount);
                context.getSource().sendFeedback(() -> Text.literal(
                    "Set " + playerInfo.getName() + "'s balance to " +
                    api.getCurrencySymbol() + formattedAmount + " " + api.getCurrencyName() +
                    (playerInfo.isOnline() ? " (online)" : " (offline)")
                ), true);
                return 1;
            } else {
                context.getSource().sendError(Text.literal("Failed to set balance"));
                return 0;
            }
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error setting balance: " + e.getMessage()));
            return 0;
        }
    }
    
    /**
     * Adds money to a player's balance.
     */
    private static int addMoney(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity targetPlayer = EntityArgumentType.getPlayer(context, "player");
            int amount = IntegerArgumentType.getInteger(context, "amount");
            EconomyAPI api = EconomyAPI.getInstance();

            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }

            boolean success = api.addMoney(targetPlayer.getUuid(), amount, "Money added by admin command");

            if (success) {
                String formattedAmount = api.formatMoney(amount);
                long newBalance = api.getBalance(targetPlayer);
                String formattedBalance = api.formatMoney(newBalance);

                context.getSource().sendFeedback(() -> Text.literal(
                    "Added " + api.getCurrencySymbol() + formattedAmount + " to " +
                    targetPlayer.getName().getString() + "'s balance. New balance: " +
                    api.getCurrencySymbol() + formattedBalance + " " + api.getCurrencyName()
                ), true);
                return 1;
            } else {
                context.getSource().sendError(Text.literal("Failed to add money"));
                return 0;
            }
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error adding money: " + e.getMessage()));
            return 0;
        }
    }

    /**
     * Adds money to a player's balance by name (supports offline players).
     */
    private static int addMoneyByName(CommandContext<ServerCommandSource> context) {
        try {
            String playerName = StringArgumentType.getString(context, "playername");
            int amount = IntegerArgumentType.getInteger(context, "amount");
            PlayerInfo playerInfo = resolvePlayer(context, playerName);

            if (playerInfo == null) {
                context.getSource().sendError(Text.literal("Player '" + playerName + "' not found"));
                return 0;
            }

            EconomyAPI api = EconomyAPI.getInstance();

            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }

            boolean success = api.addMoney(playerInfo.getUuid(), amount, "Money added by admin command");

            if (success) {
                String formattedAmount = api.formatMoney(amount);
                long newBalance = api.getBalance(playerInfo.getUuid());
                String formattedBalance = api.formatMoney(newBalance);

                context.getSource().sendFeedback(() -> Text.literal(
                    "Added " + api.getCurrencySymbol() + formattedAmount + " to " +
                    playerInfo.getName() + "'s balance. New balance: " +
                    api.getCurrencySymbol() + formattedBalance + " " + api.getCurrencyName() +
                    (playerInfo.isOnline() ? " (online)" : " (offline)")
                ), true);
                return 1;
            } else {
                context.getSource().sendError(Text.literal("Failed to add money"));
                return 0;
            }
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error adding money: " + e.getMessage()));
            return 0;
        }
    }
    
    /**
     * Removes money from a player's balance.
     */
    private static int removeMoney(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity targetPlayer = EntityArgumentType.getPlayer(context, "player");
            int amount = IntegerArgumentType.getInteger(context, "amount");
            EconomyAPI api = EconomyAPI.getInstance();

            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }

            boolean success = api.subtractMoney(targetPlayer.getUuid(), amount, "Money removed by admin command");

            if (success) {
                String formattedAmount = api.formatMoney(amount);
                long newBalance = api.getBalance(targetPlayer);
                String formattedBalance = api.formatMoney(newBalance);

                context.getSource().sendFeedback(() -> Text.literal(
                    "Removed " + api.getCurrencySymbol() + formattedAmount + " from " +
                    targetPlayer.getName().getString() + "'s balance. New balance: " +
                    api.getCurrencySymbol() + formattedBalance + " " + api.getCurrencyName()
                ), true);
                return 1;
            } else {
                context.getSource().sendError(Text.literal("Failed to remove money (insufficient balance?)"));
                return 0;
            }
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error removing money: " + e.getMessage()));
            return 0;
        }
    }

    /**
     * Removes money from a player's balance by name (supports offline players).
     */
    private static int removeMoneyByName(CommandContext<ServerCommandSource> context) {
        try {
            String playerName = StringArgumentType.getString(context, "playername");
            int amount = IntegerArgumentType.getInteger(context, "amount");
            PlayerInfo playerInfo = resolvePlayer(context, playerName);

            if (playerInfo == null) {
                context.getSource().sendError(Text.literal("Player '" + playerName + "' not found"));
                return 0;
            }

            EconomyAPI api = EconomyAPI.getInstance();

            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }

            boolean success = api.subtractMoney(playerInfo.getUuid(), amount, "Money removed by admin command");

            if (success) {
                String formattedAmount = api.formatMoney(amount);
                long newBalance = api.getBalance(playerInfo.getUuid());
                String formattedBalance = api.formatMoney(newBalance);

                context.getSource().sendFeedback(() -> Text.literal(
                    "Removed " + api.getCurrencySymbol() + formattedAmount + " from " +
                    playerInfo.getName() + "'s balance. New balance: " +
                    api.getCurrencySymbol() + formattedBalance + " " + api.getCurrencyName() +
                    (playerInfo.isOnline() ? " (online)" : " (offline)")
                ), true);
                return 1;
            } else {
                context.getSource().sendError(Text.literal("Failed to remove money (insufficient balance?)"));
                return 0;
            }
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error removing money: " + e.getMessage()));
            return 0;
        }
    }
    
    /**
     * Transfers money between players.
     */
    private static int transferMoney(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity fromPlayer = EntityArgumentType.getPlayer(context, "from");
            ServerPlayerEntity toPlayer = EntityArgumentType.getPlayer(context, "to");
            int amount = IntegerArgumentType.getInteger(context, "amount");
            EconomyAPI api = EconomyAPI.getInstance();

            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }

            boolean success = api.transferMoney(fromPlayer.getUuid(), toPlayer.getUuid(), amount, "Money transferred by admin command");

            if (success) {
                String formattedAmount = api.formatMoney(amount);

                context.getSource().sendFeedback(() -> Text.literal(
                    "Transferred " + api.getCurrencySymbol() + formattedAmount + " from " +
                    fromPlayer.getName().getString() + " to " + toPlayer.getName().getString()
                ), true);
                return 1;
            } else {
                context.getSource().sendError(Text.literal("Failed to transfer money (insufficient balance?)"));
                return 0;
            }
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error transferring money: " + e.getMessage()));
            return 0;
        }
    }

    /**
     * Transfers money between players by name (supports offline players).
     */
    private static int transferMoneyByName(CommandContext<ServerCommandSource> context) {
        try {
            String fromName = StringArgumentType.getString(context, "fromname");
            String toName = StringArgumentType.getString(context, "toname");
            int amount = IntegerArgumentType.getInteger(context, "amount");

            PlayerInfo fromPlayer = resolvePlayer(context, fromName);
            PlayerInfo toPlayer = resolvePlayer(context, toName);

            if (fromPlayer == null) {
                context.getSource().sendError(Text.literal("Source player '" + fromName + "' not found"));
                return 0;
            }

            if (toPlayer == null) {
                context.getSource().sendError(Text.literal("Target player '" + toName + "' not found"));
                return 0;
            }

            EconomyAPI api = EconomyAPI.getInstance();

            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }

            boolean success = api.transferMoney(fromPlayer.getUuid(), toPlayer.getUuid(), amount, "Money transferred by admin command");

            if (success) {
                String formattedAmount = api.formatMoney(amount);

                context.getSource().sendFeedback(() -> Text.literal(
                    "Transferred " + api.getCurrencySymbol() + formattedAmount + " from " +
                    fromPlayer.getName() + (fromPlayer.isOnline() ? " (online)" : " (offline)") +
                    " to " + toPlayer.getName() + (toPlayer.isOnline() ? " (online)" : " (offline)")
                ), true);
                return 1;
            } else {
                context.getSource().sendError(Text.literal("Failed to transfer money (insufficient balance?)"));
                return 0;
            }
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error transferring money: " + e.getMessage()));
            return 0;
        }
    }
    
    /**
     * Gets detailed player information.
     */
    private static int getPlayerInfo(CommandContext<ServerCommandSource> context) {
        try {
            ServerPlayerEntity targetPlayer = EntityArgumentType.getPlayer(context, "player");
            EconomyAPI api = EconomyAPI.getInstance();

            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }

            PlayerEconomyData data = api.getPlayerData(targetPlayer.getUuid());
            if (data == null) {
                context.getSource().sendError(Text.literal("Player data not found"));
                return 0;
            }

            String playerName = targetPlayer.getName().getString();
            String balance = api.formatMoney(data.getBalance());
            String totalEarned = api.formatMoney(data.getTotalEarned());
            String totalSpent = api.formatMoney(data.getTotalSpent());

            context.getSource().sendFeedback(() -> Text.literal(
                "=== Economy Info for " + playerName + " (online) ===\n" +
                "Balance: " + api.getCurrencySymbol() + balance + " " + api.getCurrencyName() + "\n" +
                "Total Earned: " + api.getCurrencySymbol() + totalEarned + "\n" +
                "Total Spent: " + api.getCurrencySymbol() + totalSpent + "\n" +
                "Transactions: " + data.getTransactionCount() + "\n" +
                "Account Created: " + new java.util.Date(data.getCreatedAt()) + "\n" +
                "Last Updated: " + new java.util.Date(data.getLastUpdated())
            ), false);

            return 1;
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error getting player info: " + e.getMessage()));
            return 0;
        }
    }

    /**
     * Gets detailed player information by name (supports offline players).
     */
    private static int getPlayerInfoByName(CommandContext<ServerCommandSource> context) {
        try {
            String playerName = StringArgumentType.getString(context, "playername");
            PlayerInfo playerInfo = resolvePlayer(context, playerName);

            if (playerInfo == null) {
                context.getSource().sendError(Text.literal("Player '" + playerName + "' not found"));
                return 0;
            }

            EconomyAPI api = EconomyAPI.getInstance();

            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }

            PlayerEconomyData data = api.getPlayerData(playerInfo.getUuid());
            if (data == null) {
                context.getSource().sendError(Text.literal("Player data not found"));
                return 0;
            }

            String balance = api.formatMoney(data.getBalance());
            String totalEarned = api.formatMoney(data.getTotalEarned());
            String totalSpent = api.formatMoney(data.getTotalSpent());

            context.getSource().sendFeedback(() -> Text.literal(
                "=== Economy Info for " + playerInfo.getName() +
                (playerInfo.isOnline() ? " (online)" : " (offline)") + " ===\n" +
                "Balance: " + api.getCurrencySymbol() + balance + " " + api.getCurrencyName() + "\n" +
                "Total Earned: " + api.getCurrencySymbol() + totalEarned + "\n" +
                "Total Spent: " + api.getCurrencySymbol() + totalSpent + "\n" +
                "Transactions: " + data.getTransactionCount() + "\n" +
                "Account Created: " + new java.util.Date(data.getCreatedAt()) + "\n" +
                "Last Updated: " + new java.util.Date(data.getLastUpdated())
            ), false);

            return 1;
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error getting player info: " + e.getMessage()));
            return 0;
        }
    }
    
    /**
     * Gets economy system statistics.
     */
    private static int getStats(CommandContext<ServerCommandSource> context) {
        try {
            EconomyAPI api = EconomyAPI.getInstance();
            
            if (!api.isAvailable()) {
                context.getSource().sendError(Text.literal("Economy system is not available"));
                return 0;
            }
            
            Map<String, Object> stats = api.getStatistics();
            
            StringBuilder statsText = new StringBuilder("=== Economy System Statistics ===\n");
            statsText.append("Available: ").append(stats.get("available")).append("\n");
            statsText.append("Initialized: ").append(stats.get("initialized")).append("\n");
            
            if (stats.containsKey("security")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> securityStats = (Map<String, Object>) stats.get("security");
                statsText.append("Security - Tracked Players: ").append(securityStats.get("total_profiles")).append("\n");
                statsText.append("Security - Watched Players: ").append(securityStats.get("watched_players")).append("\n");
            }
            
            if (stats.containsKey("network")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> networkStats = (Map<String, Object>) stats.get("network");
                statsText.append("Network - Tracked Players: ").append(networkStats.get("tracked_players")).append("\n");
                statsText.append("Network - Watching Players: ").append(networkStats.get("watching_players")).append("\n");
            }
            
            context.getSource().sendFeedback(() -> Text.literal(statsText.toString()), false);
            
            return 1;
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error getting stats: " + e.getMessage()));
            return 0;
        }
    }
    
    /**
     * Reloads the economy system.
     */
    private static int reload(CommandContext<ServerCommandSource> context) {
        try {
            context.getSource().sendFeedback(() -> Text.literal("Economy system reload is not implemented yet"), false);
            return 1;
        } catch (Exception e) {
            context.getSource().sendError(Text.literal("Error reloading economy: " + e.getMessage()));
            return 0;
        }
    }
}
