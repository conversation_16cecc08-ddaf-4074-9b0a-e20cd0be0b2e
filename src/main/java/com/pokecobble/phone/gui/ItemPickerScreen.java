package com.pokecobble.phone.gui;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.registry.Registries;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * Screen for picking items from all available items (vanilla + modded).
 * Features search functionality and categorized display.
 */
public class ItemPickerScreen extends Screen {
    private final Screen parent;
    private final Consumer<ItemStack> onItemSelected;
    
    // UI Components
    private TextFieldWidget searchField;
    
    // Item data
    private List<ItemStack> allItems = new ArrayList<>();
    private List<ItemStack> filteredItems = new ArrayList<>();
    private String searchQuery = "";
    
    // Layout - will be calculated dynamically
    private int panelWidth = 600;
    private int panelHeight = 500;
    private int scrollOffset = 0;
    private static final int SCROLL_AMOUNT = 20;

    // Grid layout
    private static final int ITEM_SIZE = 32;
    private static final int ITEM_SPACING = 4;
    private int itemsPerRow = 16;
    
    // Colors - matching farmer app style
    private static final int PANEL_BACKGROUND = 0xF0121212;
    private static final int GLASS_CONTENT_BG = 0xF0181818;
    private static final int TEXT_PRIMARY = 0xFFE8E8E8;
    private static final int TEXT_SECONDARY = 0xFFB8BCC8;
    private static final int BORDER_COLOR = 0xFF3A3A3A;
    private static final int BORDER_ACCENT = 0xFF4CAF50;
    private static final int ITEM_HOVER_BG = 0x80FFFFFF;
    
    // Spacing
    private static final int SPACING_XS = 4;
    private static final int SPACING_SM = 8;
    private static final int SPACING_MD = 12;
    private static final int HEADER_HEIGHT = 60;
    
    public ItemPickerScreen(Screen parent, Consumer<ItemStack> onItemSelected) {
        super(Text.literal("Item Picker"));
        this.parent = parent;
        this.onItemSelected = onItemSelected;
        
        loadAllItems();
        updateFilteredItems();
    }
    
    private void loadAllItems() {
        allItems.clear();
        
        // Load all items from the registry (vanilla + modded)
        for (Item item : Registries.ITEM) {
            try {
                // Skip air and other invalid items
                if (item == Items.AIR || item == null) {
                    continue;
                }
                
                ItemStack stack = new ItemStack(item);
                if (!stack.isEmpty()) {
                    allItems.add(stack);
                }
            } catch (Exception e) {
                // Skip items that cause errors
                Pokecobbleclaim.LOGGER.debug("Skipping item due to error: {}", item, e);
            }
        }
        
        Pokecobbleclaim.LOGGER.info("Loaded {} items for picker", allItems.size());
    }
    
    private void updateFilteredItems() {
        filteredItems.clear();
        
        String query = searchQuery.toLowerCase().trim();
        
        for (ItemStack item : allItems) {
            if (query.isEmpty() || matchesSearch(item, query)) {
                filteredItems.add(item);
            }
        }
        
        // Reset scroll when filter changes
        scrollOffset = 0;
        enforceScrollBounds();
    }
    
    private boolean matchesSearch(ItemStack item, String query) {
        // Search by item name
        String itemName = item.getName().getString().toLowerCase();
        if (itemName.contains(query)) {
            return true;
        }
        
        // Search by item ID
        Identifier itemId = Registries.ITEM.getId(item.getItem());
        if (itemId.toString().toLowerCase().contains(query)) {
            return true;
        }
        
        // Search by namespace (mod name)
        if (itemId.getNamespace().toLowerCase().contains(query)) {
            return true;
        }
        
        return false;
    }
    
    @Override
    protected void init() {
        super.init();

        // Make panel responsive to screen size with proper constraints
        int minWidth = 400; // Minimum width for item grid
        int minHeight = 300; // Minimum height for usability
        int margin = 40; // Minimum margin from screen edges

        panelWidth = Math.max(minWidth, Math.min(800, width - margin)); // Max 800px for better UX
        panelHeight = Math.max(minHeight, Math.min(600, height - margin)); // Max 600px

        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;
        
        // Search field
        int searchWidth = panelWidth - SPACING_MD * 2 - 100; // Leave space for close button
        searchField = new TextFieldWidget(this.textRenderer, leftX + SPACING_MD, topY + SPACING_MD + 20, 
                                         searchWidth, 20, Text.literal("Search"));
        searchField.setPlaceholder(Text.literal("Search items..."));
        searchField.setText(searchQuery);
        searchField.setMaxLength(50);
        searchField.setEditable(true);
        searchField.setChangedListener(this::onSearchChanged);
        this.addSelectableChild(searchField);
        this.addDrawableChild(searchField);
        
        // Close button
        this.addDrawableChild(ButtonWidget.builder(Text.literal("❌ Close"), button -> {
            this.close();
        }).dimensions(leftX + panelWidth - 90, topY + SPACING_MD + 20, 80, 20).build());
        
        // Calculate items per row based on panel width
        int availableWidth = panelWidth - SPACING_MD * 2;
        itemsPerRow = Math.max(1, availableWidth / (ITEM_SIZE + ITEM_SPACING));
    }
    
    private void onSearchChanged(String newSearch) {
        this.searchQuery = newSearch;
        updateFilteredItems();
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render background
        this.renderBackground(context);
        
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;
        
        // Draw professional glass panel
        drawGlassPanel(context, leftX, topY, panelWidth, panelHeight);
        
        // Draw header
        drawHeader(context, leftX, topY, panelWidth);
        
        // Draw item grid
        int gridY = topY + HEADER_HEIGHT;
        int gridHeight = panelHeight - HEADER_HEIGHT - SPACING_MD;
        drawItemGrid(context, leftX + SPACING_MD, gridY, panelWidth - SPACING_MD * 2, gridHeight, mouseX, mouseY);
        
        super.render(context, mouseX, mouseY, delta);
    }
    
    private void drawGlassPanel(DrawContext context, int x, int y, int width, int height) {
        // Drop shadow for depth
        context.fill(x + 2, y + 2, x + width + 2, y + height + 2, 0x60000000);
        
        // Main panel background
        context.fill(x, y, x + width, y + height, PANEL_BACKGROUND);
        
        // Glass effect highlights and shadows
        context.fill(x, y, x + width, y + 1, 0x40FFFFFF); // Top highlight
        context.fill(x, y, x + 1, y + height, 0x20FFFFFF); // Left highlight
        context.fill(x, y + height - 1, x + width, y + height, 0x60000000); // Bottom shadow
        context.fill(x + width - 1, y, x + width, y + height, 0x40000000); // Right shadow
        
        // Professional border
        context.drawBorder(x, y, width, height, BORDER_COLOR);
        
        // Inner border for premium look
        context.drawBorder(x + 1, y + 1, width - 2, height - 2, 0x20FFFFFF);
    }
    
    private void drawHeader(DrawContext context, int x, int y, int width) {
        // Header background
        int headerX = x + SPACING_XS;
        int headerY = y + SPACING_XS;
        int headerWidth = width - SPACING_XS * 2;
        int headerHeight = HEADER_HEIGHT - SPACING_XS;
        
        context.fill(headerX, headerY, headerX + headerWidth, headerY + headerHeight, GLASS_CONTENT_BG);
        context.drawBorder(headerX, headerY, headerWidth, headerHeight, BORDER_COLOR);
        
        // Title
        String title = "🎯 Item Picker - " + filteredItems.size() + " items";
        context.drawTextWithShadow(this.textRenderer, title, headerX + SPACING_SM, headerY + SPACING_SM, TEXT_PRIMARY);
        
        // Instructions
        String instructions = "Search and click an item to select it";
        context.drawTextWithShadow(this.textRenderer, instructions, headerX + SPACING_SM, headerY + SPACING_SM + 12, TEXT_SECONDARY);
        
        // Accent line under header
        context.fill(x, y + HEADER_HEIGHT - 1, x + width, y + HEADER_HEIGHT, BORDER_ACCENT);
    }
    
    private void drawItemGrid(DrawContext context, int x, int y, int width, int height, int mouseX, int mouseY) {
        // Enable scissor for clipping
        context.enableScissor(x, y, x + width, y + height);
        
        try {
            if (filteredItems.isEmpty()) {
                // Empty state
                String emptyText = searchQuery.isEmpty() ? "No items available" : "No items match your search";
                int textWidth = this.textRenderer.getWidth(emptyText);
                context.drawTextWithShadow(this.textRenderer, emptyText, 
                    x + (width - textWidth) / 2, y + height / 2, TEXT_SECONDARY);
                return;
            }
            
            // Calculate grid layout
            int totalRows = (filteredItems.size() + itemsPerRow - 1) / itemsPerRow;
            int rowHeight = ITEM_SIZE + ITEM_SPACING;
            
            int startY = y - scrollOffset;
            int itemIndex = 0;
            
            for (int row = 0; row < totalRows; row++) {
                int rowY = startY + row * rowHeight;
                
                // Skip rows that are not visible
                if (rowY + ITEM_SIZE < y || rowY > y + height) {
                    itemIndex += Math.min(itemsPerRow, filteredItems.size() - itemIndex);
                    continue;
                }
                
                for (int col = 0; col < itemsPerRow && itemIndex < filteredItems.size(); col++) {
                    ItemStack item = filteredItems.get(itemIndex);
                    
                    int itemX = x + col * (ITEM_SIZE + ITEM_SPACING);
                    
                    drawItemSlot(context, item, itemX, rowY, mouseX, mouseY);
                    itemIndex++;
                }
            }
        } finally {
            context.disableScissor();
        }
        
        // Draw scroll indicator
        drawScrollIndicator(context, x + width - 6, y, height);
    }
    
    private void drawItemSlot(DrawContext context, ItemStack item, int x, int y, int mouseX, int mouseY) {
        boolean isHovered = mouseX >= x && mouseX <= x + ITEM_SIZE && mouseY >= y && mouseY <= y + ITEM_SIZE;
        
        // Slot background
        int slotBg = isHovered ? ITEM_HOVER_BG : 0x80000000;
        context.fill(x, y, x + ITEM_SIZE, y + ITEM_SIZE, slotBg);
        context.drawBorder(x, y, ITEM_SIZE, ITEM_SIZE, BORDER_COLOR);
        
        // Draw item
        try {
            context.drawItem(item, x + 8, y + 8);
        } catch (Exception e) {
            // Fallback for items that can't be rendered
            context.fill(x + 4, y + 4, x + ITEM_SIZE - 4, y + ITEM_SIZE - 4, 0xFFFF0000);
        }
        
        // Draw tooltip on hover
        if (isHovered) {
            // We'll render the tooltip after the main render to ensure it's on top
        }
    }
    
    private void drawScrollIndicator(DrawContext context, int x, int y, int height) {
        int maxScroll = calculateMaxScroll();
        if (maxScroll <= 0) return;
        
        int indicatorHeight = Math.max(20, (height * height) / (height + maxScroll));
        int indicatorY = y + (int)((double)scrollOffset / maxScroll * (height - indicatorHeight));
        
        // Draw scroll track
        context.fill(x, y, x + 4, y + height, 0x40000000);
        
        // Draw scroll thumb
        context.fill(x, indicatorY, x + 4, indicatorY + indicatorHeight, 0x80FFFFFF);
    }
    
    private int calculateMaxScroll() {
        if (filteredItems.isEmpty()) return 0;
        
        int totalRows = (filteredItems.size() + itemsPerRow - 1) / itemsPerRow;
        int totalHeight = totalRows * (ITEM_SIZE + ITEM_SPACING);
        int availableHeight = panelHeight - HEADER_HEIGHT - SPACING_MD;
        
        return Math.max(0, totalHeight - availableHeight);
    }
    
    private void enforceScrollBounds() {
        int maxScroll = calculateMaxScroll();
        scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            // Check if clicking on an item
            handleItemClick(mouseX, mouseY);
        }
        
        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    private void handleItemClick(double mouseX, double mouseY) {
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;
        int gridX = leftX + SPACING_MD;
        int gridY = topY + HEADER_HEIGHT;
        int gridWidth = panelWidth - SPACING_MD * 2;
        int gridHeight = panelHeight - HEADER_HEIGHT - SPACING_MD;
        
        // Check if click is within grid area
        if (mouseX < gridX || mouseX > gridX + gridWidth || mouseY < gridY || mouseY > gridY + gridHeight) {
            return;
        }
        
        // Calculate which item was clicked
        int relativeY = (int)(mouseY - gridY + scrollOffset);
        int relativeX = (int)(mouseX - gridX);
        
        int row = relativeY / (ITEM_SIZE + ITEM_SPACING);
        int col = relativeX / (ITEM_SIZE + ITEM_SPACING);
        
        int itemIndex = row * itemsPerRow + col;
        
        if (itemIndex >= 0 && itemIndex < filteredItems.size()) {
            ItemStack selectedItem = filteredItems.get(itemIndex);
            
            // Call the callback with the selected item
            onItemSelected.accept(selectedItem.copy());
            
            // Close the picker
            this.close();
        }
    }
    
    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        scrollOffset -= (int)(amount * SCROLL_AMOUNT);
        enforceScrollBounds();
        return true;
    }
    
    @Override
    public void close() {
        this.client.setScreen(parent);
    }
    
    @Override
    public void resize(net.minecraft.client.MinecraftClient client, int width, int height) {
        // Store current search query before resize
        String currentSearch = searchField != null ? searchField.getText() : searchQuery;

        // Resize the screen
        super.resize(client, width, height);

        // Restore search query after resize
        this.searchQuery = currentSearch;
        if (searchField != null) {
            searchField.setText(currentSearch);
        }

        // Update filtered items in case the search was active
        updateFilteredItems();
    }

    @Override
    public boolean shouldPause() {
        return false;
    }
}
