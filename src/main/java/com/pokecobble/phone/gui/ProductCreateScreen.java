package com.pokecobble.phone.gui;

import com.pokecobble.phone.network.ProductCreatePacket;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;

import java.util.HashMap;
import java.util.Map;

/**
 * Screen for creating new products in the farmer app.
 */
public class ProductCreateScreen extends Screen {
    private static final Identifier BACKGROUND_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/phone_background.png");
    
    // Layout constants
    private static final int SCREEN_WIDTH = 240;
    private static final int SCREEN_HEIGHT = 320;
    private static final int FIELD_WIDTH = 180;
    private static final int FIELD_HEIGHT = 20;
    private static final int BUTTON_WIDTH = 80;
    private static final int BUTTON_HEIGHT = 20;
    
    // Colors
    private static final int BACKGROUND_COLOR = 0xFF1E1E1E;
    private static final int HEADER_COLOR = 0xFF4CAF50;
    private static final int TEXT_COLOR = 0xFFFFFFFF;
    private static final int LABEL_COLOR = 0xFFCCCCCC;
    
    private final Screen parent;
    private final FarmerAppScreen.ProductType selectedType;
    
    // UI Components
    private TextFieldWidget nameField;
    private TextFieldWidget descriptionField;
    private TextFieldWidget priceField;
    private TextFieldWidget requiredLevelField;
    private ButtonWidget createButton;
    private ButtonWidget cancelButton;
    private ButtonWidget typeButton;
    
    // Product type selection
    private FarmerAppScreen.ProductType currentType = FarmerAppScreen.ProductType.FOOD;
    private final FarmerAppScreen.ProductType[] availableTypes = FarmerAppScreen.ProductType.values();
    private int typeIndex = 0;
    
    public ProductCreateScreen(Screen parent, FarmerAppScreen.ProductType initialType) {
        super(Text.literal("Create New Product"));
        this.parent = parent;
        this.selectedType = initialType;
        this.currentType = initialType != null ? initialType : FarmerAppScreen.ProductType.FOOD;
        
        // Find the index of the current type
        for (int i = 0; i < availableTypes.length; i++) {
            if (availableTypes[i] == this.currentType) {
                this.typeIndex = i;
                break;
            }
        }
    }
    
    @Override
    protected void init() {
        super.init();
        
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int startY = centerY - 120;
        
        // Product name field
        this.nameField = new TextFieldWidget(this.textRenderer, centerX - FIELD_WIDTH / 2, startY, FIELD_WIDTH, FIELD_HEIGHT, Text.literal("Product Name"));
        this.nameField.setMaxLength(50);
        this.nameField.setPlaceholder(Text.literal("Enter product name..."));
        this.addSelectableChild(this.nameField);
        this.addDrawableChild(this.nameField);

        // Product description field
        this.descriptionField = new TextFieldWidget(this.textRenderer, centerX - FIELD_WIDTH / 2, startY + 40, FIELD_WIDTH, FIELD_HEIGHT, Text.literal("Description"));
        this.descriptionField.setMaxLength(100);
        this.descriptionField.setPlaceholder(Text.literal("Enter description..."));
        this.addSelectableChild(this.descriptionField);
        this.addDrawableChild(this.descriptionField);

        // Price field
        this.priceField = new TextFieldWidget(this.textRenderer, centerX - FIELD_WIDTH / 2, startY + 80, FIELD_WIDTH, FIELD_HEIGHT, Text.literal("Price"));
        this.priceField.setMaxLength(10);
        this.priceField.setPlaceholder(Text.literal("Enter price..."));
        this.addSelectableChild(this.priceField);
        this.addDrawableChild(this.priceField);

        // Required level field
        this.requiredLevelField = new TextFieldWidget(this.textRenderer, centerX - FIELD_WIDTH / 2, startY + 120, FIELD_WIDTH, FIELD_HEIGHT, Text.literal("Required Level"));
        this.requiredLevelField.setMaxLength(3);
        this.requiredLevelField.setPlaceholder(Text.literal("Enter level..."));
        this.requiredLevelField.setText("1"); // Default to level 1
        this.addSelectableChild(this.requiredLevelField);
        this.addDrawableChild(this.requiredLevelField);
        
        // Product type button
        this.typeButton = ButtonWidget.builder(Text.literal("Type: " + currentType.name()), button -> cycleProductType())
            .dimensions(centerX - FIELD_WIDTH / 2, startY + 160, FIELD_WIDTH, FIELD_HEIGHT)
            .build();
        this.addDrawableChild(this.typeButton);

        // Create button
        this.createButton = ButtonWidget.builder(Text.literal("Create"), button -> createProduct())
            .dimensions(centerX - BUTTON_WIDTH - 5, startY + 200, BUTTON_WIDTH, BUTTON_HEIGHT)
            .build();
        this.addDrawableChild(this.createButton);

        // Cancel button
        this.cancelButton = ButtonWidget.builder(Text.literal("Cancel"), button -> close())
            .dimensions(centerX + 5, startY + 200, BUTTON_WIDTH, BUTTON_HEIGHT)
            .build();
        this.addDrawableChild(this.cancelButton);
        
        // Set initial focus
        this.setInitialFocus(this.nameField);
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw background
        this.renderBackground(context);
        
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int startY = centerY - 140;
        
        // Draw phone-style background
        context.fill(centerX - SCREEN_WIDTH / 2, centerY - SCREEN_HEIGHT / 2, 
                    centerX + SCREEN_WIDTH / 2, centerY + SCREEN_HEIGHT / 2, BACKGROUND_COLOR);
        
        // Draw header
        context.fill(centerX - SCREEN_WIDTH / 2, centerY - SCREEN_HEIGHT / 2, 
                    centerX + SCREEN_WIDTH / 2, centerY - SCREEN_HEIGHT / 2 + 30, HEADER_COLOR);
        
        // Draw title
        String title = "Create New Product";
        int titleWidth = this.textRenderer.getWidth(title);
        context.drawTextWithShadow(this.textRenderer, title, centerX - titleWidth / 2, centerY - SCREEN_HEIGHT / 2 + 10, TEXT_COLOR);
        
        // Draw field labels
        context.drawTextWithShadow(this.textRenderer, "Name:", centerX - FIELD_WIDTH / 2, startY - 10, LABEL_COLOR);
        context.drawTextWithShadow(this.textRenderer, "Description:", centerX - FIELD_WIDTH / 2, startY + 30, LABEL_COLOR);
        context.drawTextWithShadow(this.textRenderer, "Price:", centerX - FIELD_WIDTH / 2, startY + 70, LABEL_COLOR);
        context.drawTextWithShadow(this.textRenderer, "Required Level:", centerX - FIELD_WIDTH / 2, startY + 110, LABEL_COLOR);
        context.drawTextWithShadow(this.textRenderer, "Product Type:", centerX - FIELD_WIDTH / 2, startY + 150, LABEL_COLOR);
        
        // Draw type description
        String typeDesc = getTypeDescription(currentType);
        if (!typeDesc.isEmpty()) {
            context.drawTextWithShadow(this.textRenderer, typeDesc, centerX - FIELD_WIDTH / 2, startY + 180, 0xFF888888);
        }
        
        super.render(context, mouseX, mouseY, delta);
    }
    
    private void cycleProductType() {
        typeIndex = (typeIndex + 1) % availableTypes.length;
        currentType = availableTypes[typeIndex];
        typeButton.setMessage(Text.literal("Type: " + currentType.name()));
    }
    
    private String getTypeDescription(FarmerAppScreen.ProductType type) {
        switch (type) {
            case SEED: return "Seeds that can be planted and grown";
            case FOOD: return "Food items that can be consumed";
            case TOOL: return "Tools for farming and other activities";
            case UPGRADE: return "Upgrades for farming equipment";
            case UNLOCK: return "Unlockable features and abilities";
            default: return "";
        }
    }
    
    private void createProduct() {
        try {
            // Validate input
            String name = nameField.getText().trim();
            String description = descriptionField.getText().trim();
            String priceText = priceField.getText().trim();
            String levelText = requiredLevelField.getText().trim();
            
            if (name.isEmpty()) {
                showError("Product name cannot be empty!");
                return;
            }
            
            if (description.isEmpty()) {
                showError("Product description cannot be empty!");
                return;
            }
            
            int price;
            try {
                price = Integer.parseInt(priceText);
                if (price < 0) {
                    showError("Price must be a positive number!");
                    return;
                }
            } catch (NumberFormatException e) {
                showError("Invalid price format!");
                return;
            }
            
            int requiredLevel;
            try {
                requiredLevel = Integer.parseInt(levelText);
                if (requiredLevel < 1 || requiredLevel > 100) {
                    showError("Required level must be between 1 and 100!");
                    return;
                }
            } catch (NumberFormatException e) {
                showError("Invalid level format!");
                return;
            }
            
            // Create category-specific data
            Map<String, Object> categoryData = createDefaultCategoryData(currentType, price);
            
            // Send product creation packet to server
            ProductCreatePacket.sendToServer(name, description, price, currentType, requiredLevel, 
                                           null, null, categoryData);
            
            // Show success message and close
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(Text.literal("§aCreating product: " + name), false);
            }
            
            this.close();
            
        } catch (Exception e) {
            showError("Failed to create product: " + e.getMessage());
        }
    }
    
    private Map<String, Object> createDefaultCategoryData(FarmerAppScreen.ProductType type, int price) {
        Map<String, Object> data = new HashMap<>();
        
        switch (type) {
            case SEED:
                data.put("growth_time", 120);
                break;
            case FOOD:
                data.put("nutrition_value", 5);
                break;
            case TOOL:
                data.put("durability", 100);
                break;
            case UPGRADE:
                data.put("upgrade_discount_percent", 15);
                break;
            case UNLOCK:
                data.put("unlock_cost", price);
                break;
        }
        
        return data;
    }
    
    private void showError(String message) {
        if (this.client != null && this.client.player != null) {
            this.client.player.sendMessage(Text.literal("§c" + message), true);
        }
    }
    
    @Override
    public void close() {
        if (this.client != null) {
            this.client.setScreen(parent);
        }
    }
    
    @Override
    public boolean shouldPause() {
        return false;
    }
}
