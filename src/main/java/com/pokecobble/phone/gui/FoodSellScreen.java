package com.pokecobble.phone.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.FarmerEditModeManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;

import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.item.ItemStack;

import java.util.ArrayList;
import java.util.List;

/**
 * Professional food selling screen with quantity selection and price history graph.
 * Features modern UI design with quantity controls, max button, and 10-day price chart.
 */
public class FoodSellScreen extends Screen {
    private final Screen parent;
    private final FarmerAppScreen.Product product;
    private ItemStack playerInventoryStack;
    
    // Compact panel dimensions to fit more content
    private int panelWidth = 380;
    private int panelHeight = 280;

    // Smaller action bar dimensions
    private static final int BOTTOM_ACTION_BAR_HEIGHT = 26;

    // Professional Balanced Color Palette - Strategic color usage
    private static final int PANEL_BACKGROUND = 0xF8101014;        // Deep charcoal
    private static final int GLASS_CONTENT_BG = 0xF5181820;        // Rich dark surface
    private static final int CARD_BACKGROUND = 0xF2242429;         // Elevated card surface
    private static final int SURFACE_ELEVATED = 0xF0303038;        // Higher elevation surface
    private static final int TEXT_PRIMARY = 0xFFFAFAFA;           // Premium white
    private static final int TEXT_SECONDARY = 0xFFE4E4E7;         // High contrast secondary
    private static final int TEXT_MUTED = 0xFFA1A1AA;             // Professional muted
    private static final int TEXT_SUCCESS = 0xFF10B981;           // Professional green (strategic use)
    private static final int TEXT_WARNING = 0xFFD97706;           // Professional amber (strategic use)
    private static final int TEXT_ERROR = 0xFFDC2626;             // Professional red (strategic use)
    private static final int TEXT_ACCENT = 0xFF3B82F6;            // Professional blue accent
    private static final int BORDER_COLOR = 0xFF3F3F46;           // Professional border
    private static final int BORDER_ACCENT = 0xFF52525B;          // Subtle accent border
    private static final int BORDER_LIGHT = 0xFF52525B;           // Light professional border
    private static final int BORDER_FOCUS = 0xFF3B82F6;           // Blue focus indicator

    // Professional balanced button system - strategic color
    private static final int BUTTON_PRIMARY = 0xFF3B82F6;         // Professional blue primary
    private static final int BUTTON_PRIMARY_HOVER = 0xFF2563EB;   // Darker blue hover
    private static final int BUTTON_SECONDARY = 0xFF6B7280;       // Neutral secondary
    private static final int BUTTON_SECONDARY_HOVER = 0xFF4B5563; // Darker neutral hover
    private static final int BUTTON_TERTIARY = 0xFF6B7280;        // Neutral tertiary
    private static final int BUTTON_TERTIARY_HOVER = 0xFF4B5563;  // Darker neutral hover
    private static final int BUTTON_SUCCESS = 0xFF10B981;         // Green for success actions
    private static final int BUTTON_SUCCESS_HOVER = 0xFF059669;   // Darker green hover
    private static final int BUTTON_DISABLED = 0xFF374151;        // Professional disabled
    private static final int BUTTON_DANGER = 0xFFDC2626;          // Red for danger actions
    private static final int BUTTON_DANGER_HOVER = 0xFFB91C1C;    // Darker red hover

    // Professional graph system with strategic color
    private static final int GRAPH_LINE_COLOR = 0xFF3B82F6;       // Professional blue line
    private static final int GRAPH_FILL_COLOR = 0x203B82F6;       // Subtle blue fill
    private static final int GRAPH_GRID_COLOR = 0xFF3F3F46;       // Professional grid
    private static final int GRAPH_POINT_COLOR = 0xFF2563EB;      // Professional blue data points
    private static final int GRAPH_POINT_HIGHLIGHT = 0xFF60A5FA;  // Highlighted blue points

    // Professional shadows and effects
    private static final int SHADOW_LIGHT = 0x20000000;           // Light shadow
    private static final int SHADOW_MEDIUM = 0x40000000;          // Medium shadow
    private static final int SHADOW_HEAVY = 0x60000000;           // Heavy shadow
    private static final int HIGHLIGHT_SUBTLE = 0x10FFFFFF;       // Subtle highlight
    private static final int HIGHLIGHT_MEDIUM = 0x20FFFFFF;       // Medium highlight

    // Compact spacing system for better content density
    private static final int SPACING_XS = 2;   // 2px
    private static final int SPACING_SM = 4;   // 4px
    private static final int SPACING_MD = 6;   // 6px
    private static final int SPACING_LG = 8;   // 8px
    private static final int SPACING_XL = 12;  // 12px
    private static final int SPACING_2XL = 16; // 16px
    
    // Quantity selection
    private int selectedQuantity = 1;
    private int maxQuantity = 64; // Will be set based on inventory
    private TextFieldWidget quantityField;

    // Base positions for scrollable content
    private int baseControlsX, baseControlsY;
    private int leftX, topY; // Panel position

    // Compact button dimensions and positions
    private int minusButtonX, minusButtonY, minusButtonWidth = 20, minusButtonHeight = 18;
    private int plusButtonX, plusButtonY, plusButtonWidth = 20, plusButtonHeight = 18;
    private int maxButtonX, maxButtonY, maxButtonWidth = 30, maxButtonHeight = 18;
    private int sellButtonX, sellButtonY, sellButtonWidth = 60, sellButtonHeight = 18;
    private int cancelButtonX, cancelButtonY, cancelButtonWidth = 45, cancelButtonHeight = 18;
    private int editButtonX, editButtonY, editButtonWidth = 35, editButtonHeight = 18;

    private boolean minusButtonHovered = false;
    private boolean plusButtonHovered = false;
    private boolean maxButtonHovered = false;
    private boolean sellButtonHovered = false;
    private boolean cancelButtonHovered = false;
    private boolean editButtonHovered = false;
    
    // Price history data (mock data for demonstration)
    private final List<PricePoint> priceHistory = new ArrayList<>();

    // Enhanced price history features
    private int hoveredDataPoint = -1;
    private long lastPriceUpdateTime = 0;
    private static final long PRICE_UPDATE_INTERVAL = 100; // Animation timing
    private double averagePrice = 0.0;
    private double priceChangePercent = 0.0;
    private boolean isPriceRising = false;

    // Scrolling support
    private int scrollOffset = 0;
    private static final int SCROLL_AMOUNT = 15;
    private int contentHeight = 0;

    // Price update listener
    private com.pokecobble.phone.food.client.ClientFoodPriceManager.PriceUpdateListener priceUpdateListener;
    
    // New constructor that doesn't require ItemStack - for server-side validation approach
    public FoodSellScreen(Screen parent, FarmerAppScreen.Product product) {
        super(Text.literal("Sell " + product.getName()));
        this.parent = parent;
        this.product = product;
        this.playerInventoryStack = product.getItemStack(); // Use product's ItemStack as reference

        // Always initialize the screen - server will validate inventory
        // Scan player's entire inventory for initial quantity display
        this.maxQuantity = calculateTotalInventoryQuantity();

        // If no items available, set quantity to 0 but still show the screen
        if (this.maxQuantity <= 0) {
            this.maxQuantity = 0;
            this.selectedQuantity = 0;
            Pokecobbleclaim.LOGGER.debug("Player has no {} to sell - showing empty sell screen", product.getName());
        }

        // Always load price history and setup listeners
        loadRealPriceHistory();
        setupPriceUpdateListener();
        requestPriceDataIfNeeded();
    }

    // Legacy constructor for backward compatibility
    public FoodSellScreen(Screen parent, FarmerAppScreen.Product product, ItemStack playerInventoryStack) {
        this(parent, product); // Delegate to new constructor
        // Override the playerInventoryStack if provided
        if (playerInventoryStack != null) {
            this.playerInventoryStack = playerInventoryStack;
        }
    }

    /**
     * Calculates the total quantity of the product item available in the player's inventory.
     */
    private int calculateTotalInventoryQuantity() {
        if (this.client == null || this.client.player == null || product.getItemStack() == null) {
            return 0;
        }

        ItemStack targetItem = product.getItemStack();
        int totalQuantity = 0;

        // Scan through the player's entire inventory
        for (int i = 0; i < this.client.player.getInventory().size(); i++) {
            ItemStack stack = this.client.player.getInventory().getStack(i);

            // Check if this stack matches our target item
            if (!stack.isEmpty() && ItemStack.canCombine(stack, targetItem)) {
                totalQuantity += stack.getCount();
            }
        }

        Pokecobbleclaim.LOGGER.debug("Found {} {} in player inventory", totalQuantity, product.getName());
        return Math.max(0, totalQuantity);
    }

    /**
     * Refreshes the inventory quantity and adjusts selected quantity if needed.
     */
    public void refreshInventoryQuantity() {
        int newMaxQuantity = calculateTotalInventoryQuantity();

        if (newMaxQuantity != maxQuantity) {
            Pokecobbleclaim.LOGGER.debug("Inventory quantity changed: {} → {} for {}",
                maxQuantity, newMaxQuantity, product.getName());

            maxQuantity = newMaxQuantity;

            // Adjust selected quantity if it exceeds the new maximum
            if (selectedQuantity > maxQuantity) {
                selectedQuantity = Math.max(1, maxQuantity);
                updateQuantityField(); // Update the text field to reflect the change
            }

            // If no items available, show message but keep screen open for better UX
            if (maxQuantity <= 0) {
                this.selectedQuantity = 0;
                updateQuantityField();
                // Don't close the screen - let players see the interface even with no items
                Pokecobbleclaim.LOGGER.debug("Player has no {} to sell - keeping screen open", product.getName());
            }
        }
    }

    private void loadRealPriceHistory() {
        priceHistory.clear();

        try {
            // Get price history from client price manager
            var clientPriceManager = com.pokecobble.phone.food.client.ClientFoodPriceManager.getInstance();
            var historyEntries = clientPriceManager.getPriceHistory(product.getId());

            Pokecobbleclaim.LOGGER.info("Loading price history for {}: client manager has {} entries",
                product.getName(), historyEntries != null ? historyEntries.size() : 0);

            if (historyEntries == null || historyEntries.isEmpty()) {
                // If no server data available, generate fallback data
                Pokecobbleclaim.LOGGER.warn("No price history available for {}, generating fallback data",
                    product.getName());
                generateFallbackPriceHistory();
                return;
            }

            // Process and aggregate price data by day
            processAndAggregateHistoryData(historyEntries);

            // Calculate enhanced metrics
            calculatePriceMetrics();

            Pokecobbleclaim.LOGGER.info("Successfully processed {} price history entries for {} into {} daily points",
                historyEntries.size(), product.getName(), priceHistory.size());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Failed to load real price history, using fallback: " + e.getMessage());
            generateFallbackPriceHistory();
        }
    }

    private void processAndAggregateHistoryData(java.util.List<com.pokecobble.phone.food.data.FoodPriceData.PriceHistoryEntry> historyEntries) {
        // Use proper date handling for day calculation
        java.time.LocalDate today = java.time.LocalDate.now();
        java.util.Map<Integer, Integer> dailyPrices = new java.util.HashMap<>();
        java.util.Map<Integer, Long> dailyTimestamps = new java.util.HashMap<>();

        // Group entries by day and keep the most recent price for each day
        for (var entry : historyEntries) {
            java.time.LocalDate entryDate = java.time.Instant.ofEpochMilli(entry.getTimestamp())
                .atZone(java.time.ZoneId.systemDefault())
                .toLocalDate();

            int daysAgo = (int) java.time.temporal.ChronoUnit.DAYS.between(entryDate, today);

            // Only keep data from the last 10 days
            if (daysAgo >= 0 && daysAgo <= 9) {
                // Keep the most recent price for each day (latest timestamp)
                if (!dailyTimestamps.containsKey(daysAgo) || entry.getTimestamp() > dailyTimestamps.get(daysAgo)) {
                    dailyPrices.put(daysAgo, entry.getPrice());
                    dailyTimestamps.put(daysAgo, entry.getTimestamp());
                }
            }
        }

        // Create exactly 10 days of data (0-9 days ago)
        int currentPrice = getCurrentPrice();
        int lastKnownPrice = currentPrice;

        for (int daysAgo = 9; daysAgo >= 0; daysAgo--) {
            int price;
            long timestamp;

            if (dailyPrices.containsKey(daysAgo)) {
                // Use real data if available
                price = dailyPrices.get(daysAgo);
                timestamp = dailyTimestamps.get(daysAgo);
                lastKnownPrice = price;
            } else {
                // Fill missing days with last known price plus small variation
                double variation = (Math.random() - 0.5) * 0.1; // 10% variation
                price = Math.max(1, (int) Math.round(lastKnownPrice * (1 + variation)));
                timestamp = System.currentTimeMillis() - (daysAgo * 24L * 60L * 60L * 1000L);
            }

            priceHistory.add(new PricePoint(daysAgo, price, timestamp));
        }

        // Sort by days ago (descending) to ensure proper order
        priceHistory.sort((a, b) -> Integer.compare(b.getDaysAgo(), a.getDaysAgo()));
    }

    private void generateFallbackPriceHistory() {
        priceHistory.clear();
        int currentPrice = getCurrentPrice();

        // Generate realistic 10 days of fallback price history with market-like behavior
        java.util.Random random = new java.util.Random();
        int previousPrice = currentPrice;

        // Create a realistic trend for the fake data
        double overallTrend = (random.nextDouble() - 0.5) * 0.3; // -15% to +15% overall trend

        for (int daysAgo = 9; daysAgo >= 0; daysAgo--) {
            int price;

            if (daysAgo == 0) {
                // Today should be the current price
                price = currentPrice;
            } else {
                // Generate realistic price variations
                double dailyVariation = (random.nextGaussian() * 0.08); // 8% daily volatility
                double trendComponent = overallTrend * (9 - daysAgo) / 9.0; // Gradual trend

                // Apply variation to previous price
                double priceMultiplier = 1 + dailyVariation + trendComponent;
                price = Math.max(1, (int) Math.round(previousPrice * priceMultiplier));

                // Ensure price doesn't deviate too much from current price
                int maxDeviation = (int) Math.max(1, currentPrice * 0.4); // Max 40% deviation
                price = Math.max(currentPrice - maxDeviation, Math.min(currentPrice + maxDeviation, price));
            }

            long timestamp = System.currentTimeMillis() - (daysAgo * 24L * 60L * 60L * 1000L);
            priceHistory.add(new PricePoint(daysAgo, price, timestamp));
            previousPrice = price;
        }

        // Sort by days ago (descending) to ensure proper order
        priceHistory.sort((a, b) -> Integer.compare(b.getDaysAgo(), a.getDaysAgo()));

        // Calculate enhanced metrics
        calculatePriceMetrics();

        Pokecobbleclaim.LOGGER.info("Generated fallback price history for {} with {} data points",
            product.getName(), priceHistory.size());
    }

    private void calculatePriceMetrics() {
        if (priceHistory.isEmpty()) return;

        // Calculate average price
        averagePrice = priceHistory.stream()
            .mapToInt(PricePoint::getPrice)
            .average()
            .orElse(0.0);

        // Calculate price change percentage (comparing first and last prices)
        if (priceHistory.size() >= 2) {
            int oldestPrice = priceHistory.get(0).getPrice(); // 9 days ago
            int newestPrice = priceHistory.get(priceHistory.size() - 1).getPrice(); // Today
            priceChangePercent = ((double)(newestPrice - oldestPrice) / oldestPrice) * 100;
            isPriceRising = priceChangePercent > 0;
        }
    }
    
    @Override
    protected void init() {
        super.init();

        // Calculate responsive panel dimensions with compact proportions
        panelWidth = Math.min(width - 40, 380);
        panelHeight = Math.min(height - 40, 280);

        // Store panel positions with compact centering
        leftX = (width - panelWidth) / 2;
        topY = Math.max(SPACING_SM, (height - panelHeight) / 2);

        // Store base positions for scrollable content with compact spacing
        baseControlsX = leftX + panelWidth / 2 - 70;
        baseControlsY = 55; // Relative to content area with compact space

        // Compact quantity text field
        quantityField = new TextFieldWidget(this.textRenderer, 0, 0, 45, 18, Text.literal("Quantity"));
        quantityField.setText(String.valueOf(selectedQuantity));
        quantityField.setChangedListener(this::onQuantityChanged);
        this.addDrawableChild(quantityField);

        // Action buttons - positioned in compact action bar
        int actionBarY = topY + panelHeight - BOTTOM_ACTION_BAR_HEIGHT;
        int buttonY = actionBarY + (BOTTOM_ACTION_BAR_HEIGHT - sellButtonHeight) / 2;

        // Check if edit mode is enabled
        boolean editModeEnabled = FarmerEditModeManager.getInstance().isEditModeEnabled();

        // Set action button positions with compact spacing
        cancelButtonX = leftX + SPACING_SM;
        cancelButtonY = buttonY;

        // Position edit button next to cancel if edit mode is enabled
        if (editModeEnabled) {
            editButtonX = cancelButtonX + cancelButtonWidth + SPACING_XS;
            editButtonY = buttonY;
        }

        sellButtonX = leftX + panelWidth - sellButtonWidth - SPACING_SM;
        sellButtonY = buttonY;

        // Calculate content height for scrolling (header + content)
        contentHeight = 200; // Approximate total content height including header
    }

    private void updateQuantityField() {
        if (quantityField != null) {
            quantityField.setText(String.valueOf(selectedQuantity));
        }
    }
    
    private void onQuantityChanged(String text) {
        try {
            int quantity = Integer.parseInt(text);
            if (quantity >= 1 && quantity <= maxQuantity) {
                selectedQuantity = quantity;
            }
        } catch (NumberFormatException e) {
            // Invalid input, revert to current quantity
            updateQuantityField();
        }
    }
    
    private void performSell() {
        if (this.client == null || this.client.player == null) {
            return;
        }

        // Validate quantity selection
        if (selectedQuantity <= 0) {
            this.client.player.sendMessage(
                net.minecraft.text.Text.literal("§cPlease select a valid quantity to sell!"),
                true
            );
            return;
        }

        // Calculate transaction details
        int currentPrice = getCurrentPrice();
        int totalValue = selectedQuantity * currentPrice;

        // Send food sale request to server - server will validate inventory and handle removal
        // This prevents client-side inventory manipulation exploits

        // Send food sale request to server using new economy system
        try {
            com.pokecobble.economy.api.EconomyAPI economyAPI = com.pokecobble.economy.api.EconomyAPI.getInstance();
            if (economyAPI.isAvailable()) {
                // Create enhanced food sale packet for server processing with item validation
                net.minecraft.network.PacketByteBuf buf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();
                buf.writeString(product.getId()); // Product ID for server validation
                buf.writeString(product.getName()); // Product name for display
                buf.writeInt(selectedQuantity); // Requested quantity
                buf.writeLong(totalValue); // Expected total value
                buf.writeInt(currentPrice); // Expected price per item
                buf.writeString("Sold " + selectedQuantity + " " + product.getName() + " to farmer");

                // Send to server for processing - server will validate inventory and handle removal
                net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                    com.pokecobble.economy.network.EconomyNetworkManager.ECONOMY_FOOD_SALE, buf);

                // Show pending message - server will send success/failure response
                this.client.player.sendMessage(
                    net.minecraft.text.Text.literal("§eProcessing sale of " + selectedQuantity + " " + product.getName() + "..."),
                    true
                );

                Pokecobbleclaim.LOGGER.info("Player {} requested to sell {} {} for {} coins (price: {} each) - awaiting server validation",
                    this.client.player.getName().getString(), selectedQuantity, product.getName(), totalValue, currentPrice);

            } else {
                // Economy system not available
                this.client.player.sendMessage(
                    net.minecraft.text.Text.literal("§cEconomy system not available - sale cancelled!"),
                    true
                );
                return; // Don't complete the sale if economy is not available
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error processing food sale", e);
            this.client.player.sendMessage(
                net.minecraft.text.Text.literal("§cError processing sale - please try again!"),
                true
            );
            return; // Don't complete the sale if there's an error
        }

        // Close the screen
        this.client.setScreen(parent);
    }

    /**
     * Gets the current price for this food item from the client price manager.
     */
    private int getCurrentPrice() {
        try {
            var clientPriceManager = com.pokecobble.phone.food.client.ClientFoodPriceManager.getInstance();
            int serverPrice = clientPriceManager.getCurrentPrice(product.getId());

            // Use server price if available, otherwise fall back to product price
            return serverPrice > 0 ? serverPrice : product.getPrice();

        } catch (Exception e) {
            // Fall back to static product price if client manager is not available
            return product.getPrice();
        }
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Refresh inventory quantity to ensure accuracy
        refreshInventoryQuantity();

        // Render background with subtle gradient
        this.renderBackground(context);

        // Calculate panel position with compact centering
        int leftX = (width - panelWidth) / 2;
        int topY = Math.max(SPACING_SM, (height - panelHeight) / 2);

        // Draw modern panel with enhanced styling
        drawModernPanel(context, leftX, topY, panelWidth, panelHeight);

        // Enable scissor for scrollable content with compact spacing
        int contentAreaY = topY + SPACING_SM;
        int contentAreaHeight = panelHeight - BOTTOM_ACTION_BAR_HEIGHT - SPACING_SM * 2;
        context.enableScissor(leftX, contentAreaY, leftX + panelWidth, contentAreaY + contentAreaHeight);

        try {
            // Draw scrollable content with offset
            int currentY = contentAreaY - scrollOffset;

            // Draw compact header
            drawModernHeader(context, leftX, currentY, panelWidth);

            // Draw compact product info card
            drawModernProductInfo(context, leftX, currentY + 22, panelWidth);

            // Draw compact quantity selection
            drawModernQuantitySelection(context, leftX, currentY + 50, panelWidth);

            // Update quantity control positions with scroll offset
            updateQuantityControlPositions(currentY);

            // Draw compact total calculation (moved above price history)
            drawModernTotalCalculation(context, leftX, currentY + 80, panelWidth);

            // Draw enhanced price history graph (moved below total)
            drawEnhancedPriceHistoryGraph(context, leftX + SPACING_SM, currentY + 110, panelWidth - SPACING_SM * 2, 90);

        } finally {
            // Always disable scissor
            context.disableScissor();
        }

        // Draw modern action bar with enhanced buttons
        drawModernActionBar(context, leftX, topY, panelWidth, panelHeight);

        // Draw modern custom buttons
        drawModernCustomButtons(context, mouseX, mouseY);

        super.render(context, mouseX, mouseY, delta);
    }

    private void updateQuantityControlPositions(int currentY) {
        // Update button positions with scroll offset and compact spacing
        int controlsY = currentY + baseControlsY;

        minusButtonX = baseControlsX;
        minusButtonY = controlsY;

        // Compact spacing between controls
        plusButtonX = baseControlsX + minusButtonWidth + 45 + SPACING_XS; // Space for text field
        plusButtonY = controlsY;

        maxButtonX = plusButtonX + plusButtonWidth + SPACING_XS;
        maxButtonY = controlsY;

        // Update text field position with compact spacing
        quantityField.setX(baseControlsX + minusButtonWidth + SPACING_XS);
        quantityField.setY(controlsY);
    }

    private void drawModernCustomButtons(DrawContext context, int mouseX, int mouseY) {
        // Check if edit mode is enabled
        boolean editModeEnabled = FarmerEditModeManager.getInstance().isEditModeEnabled();

        // Update hover states
        minusButtonHovered = isMouseOver(mouseX, mouseY, minusButtonX, minusButtonY, minusButtonWidth, minusButtonHeight);
        plusButtonHovered = isMouseOver(mouseX, mouseY, plusButtonX, plusButtonY, plusButtonWidth, plusButtonHeight);
        maxButtonHovered = isMouseOver(mouseX, mouseY, maxButtonX, maxButtonY, maxButtonWidth, maxButtonHeight);
        sellButtonHovered = isMouseOver(mouseX, mouseY, sellButtonX, sellButtonY, sellButtonWidth, sellButtonHeight);
        cancelButtonHovered = isMouseOver(mouseX, mouseY, cancelButtonX, cancelButtonY, cancelButtonWidth, cancelButtonHeight);

        if (editModeEnabled) {
            editButtonHovered = isMouseOver(mouseX, mouseY, editButtonX, editButtonY, editButtonWidth, editButtonHeight);
        }

        // Draw modern quantity control buttons
        boolean minusActive = selectedQuantity > 1;
        boolean plusActive = selectedQuantity < maxQuantity;
        boolean maxActive = selectedQuantity < maxQuantity;

        drawModernButton(context, minusButtonX, minusButtonY, minusButtonWidth, minusButtonHeight,
                       BUTTON_SECONDARY, BUTTON_SECONDARY_HOVER, minusButtonHovered && minusActive, minusActive, "-");
        drawModernButton(context, plusButtonX, plusButtonY, plusButtonWidth, plusButtonHeight,
                       BUTTON_SECONDARY, BUTTON_SECONDARY_HOVER, plusButtonHovered && plusActive, plusActive, "+");
        drawModernButton(context, maxButtonX, maxButtonY, maxButtonWidth, maxButtonHeight,
                       BUTTON_SECONDARY, BUTTON_SECONDARY_HOVER, maxButtonHovered && maxActive, maxActive, "MAX");

        // Draw modern action buttons with balanced colors
        boolean sellActive = selectedQuantity > 0 && selectedQuantity <= maxQuantity;
        drawModernButton(context, sellButtonX, sellButtonY, sellButtonWidth, sellButtonHeight,
                       BUTTON_SUCCESS, BUTTON_SUCCESS_HOVER, sellButtonHovered && sellActive, sellActive, "Sell Items");
        drawModernButton(context, cancelButtonX, cancelButtonY, cancelButtonWidth, cancelButtonHeight,
                       BUTTON_TERTIARY, BUTTON_TERTIARY_HOVER, cancelButtonHovered, true, "Cancel");

        // Draw edit button if edit mode is enabled
        if (editModeEnabled) {
            drawModernButton(context, editButtonX, editButtonY, editButtonWidth, editButtonHeight,
                           BUTTON_PRIMARY, BUTTON_PRIMARY_HOVER, editButtonHovered, true, "Edit");
        }
    }

    private boolean isMouseOver(int mouseX, int mouseY, int x, int y, int width, int height) {
        return mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;
    }

    private void drawModernButton(DrawContext context, int x, int y, int width, int height, int baseColor, int hoverColor, boolean isHovered, boolean isActive, String text) {
        // Professional button shadow system
        if (isActive) {
            context.fill(x + 2, y + 2, x + width + 2, y + height + 2, SHADOW_MEDIUM);
            context.fill(x + 1, y + 1, x + width + 1, y + height + 1, SHADOW_LIGHT);
        }

        // Determine professional button color based on state
        int buttonColor = baseColor;
        if (!isActive) {
            buttonColor = BUTTON_DISABLED;
        } else if (isHovered) {
            buttonColor = hoverColor;
        }

        // Professional button background with premium gradient
        context.fill(x, y, x + width, y + height, buttonColor);

        // Professional gradient system for depth
        if (isActive) {
            context.fill(x, y, x + width, y + height/4, HIGHLIGHT_MEDIUM); // Top highlight
            context.fill(x, y + height/4, x + width, y + height/2, HIGHLIGHT_SUBTLE); // Mid highlight
            context.fill(x, y + height*3/4, x + width, y + height, SHADOW_LIGHT); // Bottom shadow

            // Professional inner glow when hovered
            if (isHovered) {
                context.fill(x + 1, y + 1, x + width - 1, y + height - 1, HIGHLIGHT_SUBTLE);
                context.fill(x + 2, y + 2, x + width - 2, y + height - 2, 0x10FFFFFF);
            }
        }

        // Professional border system
        int borderColor = BORDER_LIGHT;
        if (isActive && isHovered) {
            borderColor = BORDER_FOCUS;
        } else if (isActive) {
            borderColor = BORDER_ACCENT;
        } else {
            borderColor = BORDER_COLOR;
        }

        context.drawBorder(x, y, width, height, borderColor);

        // Professional inner border system for depth
        if (isActive) {
            context.drawBorder(x + 1, y + 1, width - 2, height - 2, HIGHLIGHT_SUBTLE);
            context.fill(x + 2, y + 2, x + width - 2, y + 3, HIGHLIGHT_MEDIUM); // Inner top highlight
            context.fill(x + 2, y + 2, x + 3, y + height - 2, HIGHLIGHT_SUBTLE); // Inner left highlight
        }

        // Professional typography with enhanced depth
        int textColor = isActive ? TEXT_PRIMARY : TEXT_MUTED;
        int textX = x + (width - this.textRenderer.getWidth(text)) / 2;
        int textY = y + (height - 8) / 2;

        // Professional text shadow system
        if (isActive) {
            // Subtle text shadow for depth
            context.drawText(this.textRenderer, text, textX + 1, textY + 1, SHADOW_LIGHT, false);
            context.drawTextWithShadow(this.textRenderer, text, textX, textY, textColor);
        } else {
            context.drawText(this.textRenderer, text, textX, textY, textColor, false);
        }
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // ESC to cancel
        if (keyCode == 256) { // ESC key
            this.client.setScreen(parent);
            return true;
        }

        // Enter to sell
        if (keyCode == 257 || keyCode == 335) { // Enter or Numpad Enter
            if (selectedQuantity > 0 && selectedQuantity <= maxQuantity) {
                performSell();
                return true;
            }
        }

        // M key for max quantity
        if (keyCode == 77) { // M key
            selectedQuantity = maxQuantity;
            updateQuantityField();
            return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle custom button clicks
        if (button == 0) { // Left click
            // Minus button
            if (isMouseOver((int)mouseX, (int)mouseY, minusButtonX, minusButtonY, minusButtonWidth, minusButtonHeight)) {
                if (selectedQuantity > 1) {
                    selectedQuantity--;
                    updateQuantityField();
                    return true;
                }
            }

            // Plus button
            if (isMouseOver((int)mouseX, (int)mouseY, plusButtonX, plusButtonY, plusButtonWidth, plusButtonHeight)) {
                if (selectedQuantity < maxQuantity) {
                    selectedQuantity++;
                    updateQuantityField();
                    return true;
                }
            }

            // Max button
            if (isMouseOver((int)mouseX, (int)mouseY, maxButtonX, maxButtonY, maxButtonWidth, maxButtonHeight)) {
                if (selectedQuantity < maxQuantity) {
                    selectedQuantity = maxQuantity;
                    updateQuantityField();
                    return true;
                }
            }

            // Sell button
            if (isMouseOver((int)mouseX, (int)mouseY, sellButtonX, sellButtonY, sellButtonWidth, sellButtonHeight)) {
                if (selectedQuantity > 0 && selectedQuantity <= maxQuantity) {
                    performSell();
                    return true;
                }
            }

            // Cancel button
            if (isMouseOver((int)mouseX, (int)mouseY, cancelButtonX, cancelButtonY, cancelButtonWidth, cancelButtonHeight)) {
                this.client.setScreen(parent);
                return true;
            }

            // Edit button (only if edit mode is enabled)
            if (FarmerEditModeManager.getInstance().isEditModeEnabled() &&
                isMouseOver((int)mouseX, (int)mouseY, editButtonX, editButtonY, editButtonWidth, editButtonHeight)) {
                openProductEditScreen();
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Calculate max scroll based on content
        int maxScroll = Math.max(0, contentHeight - (panelHeight - 55));

        // Apply scroll with bounds checking
        scrollOffset -= (int)(amount * SCROLL_AMOUNT);
        scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));

        return true;
    }

    @Override
    public void mouseMoved(double mouseX, double mouseY) {
        // Update hovered data point for price history graph
        updateHoveredDataPoint((int)mouseX, (int)mouseY);
        super.mouseMoved(mouseX, mouseY);
    }

    private void updateHoveredDataPoint(int mouseX, int mouseY) {
        // Calculate graph area
        int leftX = (this.width - panelWidth) / 2;
        int topY = (this.height - panelHeight) / 2;
        int currentY = topY + 110 - scrollOffset; // Same as in render method

        int graphX = leftX + SPACING_SM + SPACING_LG;
        int graphY = currentY + SPACING_LG + 12;
        int graphWidth = panelWidth - SPACING_SM * 2 - SPACING_LG * 2;
        int graphHeight = 90 - SPACING_LG * 2 - 16; // Same height as in render method

        hoveredDataPoint = -1;

        if (mouseX >= graphX && mouseX <= graphX + graphWidth &&
            mouseY >= graphY && mouseY <= graphY + graphHeight &&
            !priceHistory.isEmpty()) {

            // Find closest data point
            int closestIndex = -1;
            int closestDistance = Integer.MAX_VALUE;

            for (int i = 0; i < priceHistory.size(); i++) {
                int pointX = graphX + (graphWidth * i / Math.max(1, priceHistory.size() - 1));
                int distance = Math.abs(mouseX - pointX);

                if (distance < closestDistance && distance < 20) { // 20px tolerance
                    closestDistance = distance;
                    closestIndex = i;
                }
            }

            hoveredDataPoint = closestIndex;
        }
    }
    
    private void drawModernPanel(DrawContext context, int x, int y, int width, int height) {
        // Professional multi-layer shadow system
        context.fill(x + 4, y + 4, x + width + 4, y + height + 4, SHADOW_LIGHT);
        context.fill(x + 3, y + 3, x + width + 3, y + height + 3, SHADOW_MEDIUM);
        context.fill(x + 2, y + 2, x + width + 2, y + height + 2, SHADOW_HEAVY);

        // Premium panel background with professional gradient
        context.fill(x, y, x + width, y + height, PANEL_BACKGROUND);

        // Professional depth gradient system
        context.fill(x, y, x + width, y + height/4, HIGHLIGHT_MEDIUM); // Top highlight
        context.fill(x, y + height/4, x + width, y + height/2, HIGHLIGHT_SUBTLE); // Mid highlight
        context.fill(x, y + height*3/4, x + width, y + height, SHADOW_LIGHT); // Bottom shadow

        // Professional border system with multiple layers
        context.drawBorder(x, y, width, height, BORDER_COLOR);
        context.drawBorder(x + 1, y + 1, width - 2, height - 2, BORDER_LIGHT);

        // Premium inner highlights for glass effect
        context.fill(x + 2, y + 2, x + width - 2, y + 3, HIGHLIGHT_MEDIUM); // Inner top highlight
        context.fill(x + 2, y + 2, x + 3, y + height - 2, HIGHLIGHT_SUBTLE); // Inner left highlight

        // Professional accent system - top accent bar
        context.fill(x, y, x + width, y + 3, BORDER_ACCENT);
        context.fill(x, y, x + width, y + 1, HIGHLIGHT_MEDIUM); // Accent highlight
    }
    
    private void drawModernHeader(DrawContext context, int x, int y, int width) {
        // Professional header card with elevation
        int headerHeight = 22;
        int cardX = x + SPACING_XS;
        int cardY = y + SPACING_XS;
        int cardWidth = width - SPACING_XS * 2;

        // Professional card shadow
        context.fill(cardX + 1, cardY + 1, cardX + cardWidth + 1, cardY + headerHeight + 1, SHADOW_MEDIUM);

        // Premium header background
        context.fill(cardX, cardY, cardX + cardWidth, cardY + headerHeight, CARD_BACKGROUND);

        // Professional gradient system
        context.fill(cardX, cardY, cardX + cardWidth, cardY + headerHeight/3, HIGHLIGHT_MEDIUM);
        context.fill(cardX, cardY + headerHeight*2/3, cardX + cardWidth, cardY + headerHeight, SHADOW_LIGHT);

        // Professional border system
        context.drawBorder(cardX, cardY, cardWidth, headerHeight, BORDER_LIGHT);
        context.fill(cardX + 1, cardY + 1, cardX + cardWidth - 1, cardY + 2, HIGHLIGHT_SUBTLE);

        // Professional accent bar
        context.fill(cardX, cardY, cardX + cardWidth, cardY + 2, BORDER_ACCENT);
        context.fill(cardX, cardY, cardX + cardWidth, cardY + 1, HIGHLIGHT_MEDIUM);

        // Professional typography with enhanced contrast
        String title = "Sell " + product.getName();
        int titleWidth = this.textRenderer.getWidth(title);
        int titleX = x + (width - titleWidth) / 2;
        int titleY = cardY + (headerHeight - 8) / 2;

        // Text shadow for professional depth
        context.drawText(this.textRenderer, title, titleX + 1, titleY + 1, SHADOW_LIGHT, false);
        context.drawTextWithShadow(this.textRenderer, title, titleX, titleY, TEXT_PRIMARY);
    }
    
    private void drawModernProductInfo(DrawContext context, int x, int y, int width) {
        // Professional product card with elevation
        int cardHeight = 26;
        int cardX = x + SPACING_XS;
        int cardY = y;
        int cardWidth = width - SPACING_XS * 2;

        // Professional card shadow system
        context.fill(cardX + 1, cardY + 1, cardX + cardWidth + 1, cardY + cardHeight + 1, SHADOW_MEDIUM);

        // Premium card background
        context.fill(cardX, cardY, cardX + cardWidth, cardY + cardHeight, CARD_BACKGROUND);

        // Professional gradient system
        context.fill(cardX, cardY, cardX + cardWidth, cardY + cardHeight/3, HIGHLIGHT_MEDIUM);
        context.fill(cardX, cardY + cardHeight*2/3, cardX + cardWidth, cardY + cardHeight, SHADOW_LIGHT);

        // Professional border system
        context.drawBorder(cardX, cardY, cardWidth, cardHeight, BORDER_LIGHT);
        context.fill(cardX + 1, cardY + 1, cardX + cardWidth - 1, cardY + 2, HIGHLIGHT_SUBTLE);

        // Professional icon positioning with backdrop
        int iconX = cardX + SPACING_SM;
        int iconY = cardY + (cardHeight - 16) / 2;

        // Icon backdrop for professional look
        context.fill(iconX - 1, iconY - 1, iconX + 17, iconY + 17, SURFACE_ELEVATED);
        context.drawBorder(iconX - 1, iconY - 1, 18, 18, BORDER_COLOR);

        if (product.hasItemStack()) {
            context.drawItem(product.getItemStack(), iconX, iconY);
        }

        // Professional typography with enhanced contrast
        int textX = iconX + 22;
        int textY = cardY + (cardHeight - 8) / 2;

        // Text shadow for depth
        context.drawText(this.textRenderer, product.getName(), textX + 1, textY + 1, SHADOW_LIGHT, false);
        context.drawTextWithShadow(this.textRenderer, product.getName(), textX, textY, TEXT_PRIMARY);

        // Professional inventory badge system with special handling for no items
        String inventoryText;
        int badgeColor;
        int textColor;

        if (maxQuantity <= 0) {
            inventoryText = "None Available";
            badgeColor = 0xFF8B0000; // Dark red background for no items
            textColor = 0xFFFFFFFF; // White text for contrast
        } else {
            inventoryText = "Available: " + maxQuantity;
            badgeColor = BUTTON_TERTIARY;
            textColor = TEXT_PRIMARY;
        }

        int inventoryWidth = this.textRenderer.getWidth(inventoryText);
        int badgeX = cardX + cardWidth - inventoryWidth - SPACING_SM - 6;
        int badgeY = cardY + (cardHeight - 12) / 2;

        // Professional badge with gradient
        context.fill(badgeX - 3, badgeY - 2, badgeX + inventoryWidth + 3, badgeY + 10, badgeColor);
        if (maxQuantity > 0) {
            context.fill(badgeX - 3, badgeY - 2, badgeX + inventoryWidth + 3, badgeY + 2, HIGHLIGHT_SUBTLE);
        }
        context.drawBorder(badgeX - 3, badgeY - 2, inventoryWidth + 6, 12, BORDER_COLOR);

        context.drawTextWithShadow(this.textRenderer, inventoryText, badgeX, badgeY, textColor);
    }

    private void drawModernQuantitySelection(DrawContext context, int x, int y, int width) {
        // Professional quantity selection section with elevation
        int sectionHeight = 26;
        int cardX = x + SPACING_XS;
        int cardY = y;
        int cardWidth = width - SPACING_XS * 2;

        // Professional section shadow
        context.fill(cardX + 1, cardY + 1, cardX + cardWidth + 1, cardY + sectionHeight + 1, SHADOW_MEDIUM);

        // Premium section background
        context.fill(cardX, cardY, cardX + cardWidth, cardY + sectionHeight, GLASS_CONTENT_BG);

        // Professional gradient system
        context.fill(cardX, cardY, cardX + cardWidth, cardY + sectionHeight/3, HIGHLIGHT_MEDIUM);
        context.fill(cardX, cardY + sectionHeight*2/3, cardX + cardWidth, cardY + sectionHeight, SHADOW_LIGHT);

        // Professional border system
        context.drawBorder(cardX, cardY, cardWidth, sectionHeight, BORDER_LIGHT);
        context.fill(cardX + 1, cardY + 1, cardX + cardWidth - 1, cardY + 2, HIGHLIGHT_SUBTLE);

        // Professional quantity label with enhanced typography
        String quantityLabel = "Quantity:";
        int labelY = cardY + (sectionHeight - 8) / 2;

        // Label shadow for depth
        context.drawText(this.textRenderer, quantityLabel, cardX + SPACING_SM + 1, labelY + 1, SHADOW_LIGHT, false);
        context.drawTextWithShadow(this.textRenderer, quantityLabel, cardX + SPACING_SM, labelY, TEXT_PRIMARY);

        // Professional price badge system
        String priceLabel = getCurrentPrice() + "💰";
        int priceLabelWidth = this.textRenderer.getWidth(priceLabel);
        int badgeX = cardX + cardWidth - priceLabelWidth - SPACING_SM - 8;
        int badgeY = cardY + (sectionHeight - 14) / 2;

        // Premium price badge with professional styling - using tertiary color
        context.fill(badgeX - 4, badgeY - 2, badgeX + priceLabelWidth + 4, badgeY + 12, BUTTON_TERTIARY);
        context.fill(badgeX - 4, badgeY - 2, badgeX + priceLabelWidth + 4, badgeY + 4, HIGHLIGHT_MEDIUM);
        context.fill(badgeX - 4, badgeY + 8, badgeX + priceLabelWidth + 4, badgeY + 12, SHADOW_LIGHT);
        context.drawBorder(badgeX - 4, badgeY - 2, priceLabelWidth + 8, 14, BORDER_LIGHT);

        // Badge inner highlight
        context.fill(badgeX - 3, badgeY - 1, badgeX + priceLabelWidth + 3, badgeY, HIGHLIGHT_SUBTLE);

        context.drawTextWithShadow(this.textRenderer, priceLabel, badgeX, badgeY + 2, TEXT_PRIMARY);
    }

    private void drawEnhancedPriceHistoryGraph(DrawContext context, int x, int y, int width, int height) {
        // Professional graph card with premium styling
        context.fill(x + 1, y + 1, x + width + 1, y + height + 1, SHADOW_MEDIUM);
        context.fill(x, y, x + width, y + height, CARD_BACKGROUND);

        // Professional gradient system
        context.fill(x, y, x + width, y + height/4, HIGHLIGHT_MEDIUM);
        context.fill(x, y + height/4, x + width, y + height/2, HIGHLIGHT_SUBTLE);
        context.fill(x, y + height*3/4, x + width, y + height, SHADOW_LIGHT);

        // Professional border system
        context.drawBorder(x, y, width, height, BORDER_LIGHT);
        context.drawBorder(x + 1, y + 1, width - 2, height - 2, BORDER_COLOR);

        // Professional accent with subtle color
        context.fill(x, y, x + width, y + 3, TEXT_ACCENT);
        context.fill(x, y, x + width, y + 1, HIGHLIGHT_MEDIUM);

        // Professional inner highlights
        context.fill(x + 2, y + 4, x + width - 2, y + 5, HIGHLIGHT_SUBTLE);

        // Professional title with subtle trend indication
        String trendSymbol = isPriceRising ? "↗" : "↘";
        String changeText = String.format("%.1f%%", Math.abs(priceChangePercent));
        String title = "Price History " + trendSymbol + " " + changeText;
        int titleX = x + SPACING_SM;
        int titleY = y + SPACING_SM;

        // Professional title with subtle trend color
        int titleColor = isPriceRising ? TEXT_SUCCESS : TEXT_ERROR;
        context.drawText(this.textRenderer, title, titleX + 1, titleY + 1, SHADOW_LIGHT, false);
        context.drawTextWithShadow(this.textRenderer, title, titleX, titleY, titleColor);

        // Average price indicator with accent color
        String avgText = String.format("Avg: %.0f💰", averagePrice);
        int avgWidth = this.textRenderer.getWidth(avgText);
        int avgX = x + width - avgWidth - SPACING_SM;
        context.drawTextWithShadow(this.textRenderer, avgText, avgX, titleY, TEXT_ACCENT);

        // Enhanced graph area with better margins
        int graphX = x + SPACING_LG;
        int graphY = y + SPACING_LG + 12;
        int graphWidth = width - SPACING_LG * 2;
        int graphHeight = height - SPACING_LG * 2 - 16;

        if (priceHistory.isEmpty()) return;

        // Enhanced price scaling with padding for better visualization
        int minPrice = priceHistory.stream().mapToInt(PricePoint::getPrice).min().orElse(0);
        int maxPrice = priceHistory.stream().mapToInt(PricePoint::getPrice).max().orElse(100);
        int priceRange = Math.max(1, maxPrice - minPrice);

        // Add padding to price range for better visual spacing
        int padding = Math.max(1, priceRange / 10);
        minPrice = Math.max(0, minPrice - padding);
        maxPrice = maxPrice + padding;
        priceRange = maxPrice - minPrice;

        // Draw modern grid lines
        for (int i = 0; i <= 3; i++) {
            int gridY = graphY + (graphHeight * i / 3);
            context.fill(graphX, gridY, graphX + graphWidth, gridY + 1, GRAPH_GRID_COLOR);

            // Modern price labels with better positioning
            int price = maxPrice - (priceRange * i / 3);
            String priceText = price + "💰";
            context.drawTextWithShadow(this.textRenderer, priceText, x + SPACING_XS, gridY - 4, TEXT_MUTED);
        }

        // Draw vertical grid lines and day labels with improved spacing
        int labelStep = Math.max(1, priceHistory.size() / 5); // Show max 5 labels
        for (int i = 0; i < priceHistory.size(); i += labelStep) {
            int gridX = graphX + (graphWidth * i / Math.max(1, priceHistory.size() - 1));
            context.fill(gridX, graphY, gridX + 1, graphY + graphHeight, GRAPH_GRID_COLOR);

            // Improved day labels with better text
            if (i < priceHistory.size()) {
                PricePoint point = priceHistory.get(i);
                String dayText = getDayLabel(point.getDaysAgo());
                int dayTextWidth = this.textRenderer.getWidth(dayText);
                context.drawTextWithShadow(this.textRenderer, dayText, gridX - dayTextWidth / 2, y + height - SPACING_SM, TEXT_MUTED);
            }
        }

        // Always show "Today" label at the end if not already shown
        if (priceHistory.size() > 0) {
            PricePoint lastPoint = priceHistory.get(priceHistory.size() - 1);
            if (lastPoint.getDaysAgo() == 0) {
                int lastGridX = graphX + graphWidth;
                String todayText = "Today";
                int todayTextWidth = this.textRenderer.getWidth(todayText);
                context.drawTextWithShadow(this.textRenderer, todayText, lastGridX - todayTextWidth / 2, y + height - SPACING_SM, TEXT_ACCENT);
            }
        }

        // Professional data visualization with strategic trend coloring
        if (priceHistory.size() > 1) {
            for (int i = 0; i < priceHistory.size() - 1; i++) {
                PricePoint current = priceHistory.get(i);
                PricePoint next = priceHistory.get(i + 1);

                int x1 = graphX + (graphWidth * i / (priceHistory.size() - 1));
                int y1 = graphY + graphHeight - ((current.getPrice() - minPrice) * graphHeight / priceRange);
                int x2 = graphX + (graphWidth * (i + 1) / (priceHistory.size() - 1));
                int y2 = graphY + graphHeight - ((next.getPrice() - minPrice) * graphHeight / priceRange);

                // Determine segment trend for subtle coloring
                boolean segmentRising = next.getPrice() > current.getPrice();
                int segmentColor = segmentRising ? TEXT_SUCCESS : TEXT_ERROR;

                // Professional gradient fill with subtle trend colors
                int fillSteps = Math.max(1, Math.abs(y1 - (graphY + graphHeight)) / 3);
                for (int fillY = Math.min(y1, y2); fillY < graphY + graphHeight; fillY += fillSteps) {
                    double intensity = 1.0 - (double)(fillY - Math.min(y1, y2)) / (graphY + graphHeight - Math.min(y1, y2));
                    int alpha = (int)(0x10 * intensity); // Very subtle opacity
                    int fillColor = (alpha << 24) | (segmentColor & 0xFFFFFF);
                    context.fill(x1, fillY, x2, fillY + fillSteps, fillColor);
                }

                // Professional line rendering with main graph color
                drawLine(context, x1, y1 - 1, x2, y2 - 1, SHADOW_LIGHT); // Shadow line
                drawLine(context, x1, y1, x2, y2, GRAPH_LINE_COLOR); // Main blue line
                drawLine(context, x1, y1 + 1, x2, y2 + 1, GRAPH_LINE_COLOR); // Thickness
                drawLine(context, x1 - 1, y1, x2 - 1, y2, HIGHLIGHT_SUBTLE); // Highlight edge

                // Professional data points with main graph color
                context.fill(x1 - 3, y1 - 3, x1 + 4, y1 + 4, SHADOW_MEDIUM); // Point shadow
                context.fill(x1 - 2, y1 - 2, x1 + 3, y1 + 3, GRAPH_POINT_COLOR); // Point background
                context.fill(x1 - 1, y1 - 1, x1 + 2, y1 + 2, GRAPH_LINE_COLOR); // Point center
                context.fill(x1 - 1, y1 - 1, x1 + 1, y1 + 1, GRAPH_POINT_HIGHLIGHT); // Point highlight
            }

            // Professional last point rendering with accent styling
            PricePoint lastPoint = priceHistory.get(priceHistory.size() - 1);
            int lastX = graphX + graphWidth;
            int lastY = graphY + graphHeight - ((lastPoint.getPrice() - minPrice) * graphHeight / priceRange);

            // Professional accent last point
            context.fill(lastX - 5, lastY - 5, lastX + 6, lastY + 6, SHADOW_MEDIUM); // Outer glow
            context.fill(lastX - 4, lastY - 4, lastX + 5, lastY + 5, TEXT_ACCENT); // Accent glow
            context.fill(lastX - 3, lastY - 3, lastX + 4, lastY + 4, GRAPH_POINT_HIGHLIGHT); // Inner glow
            context.fill(lastX - 2, lastY - 2, lastX + 3, lastY + 3, GRAPH_LINE_COLOR); // Background
            context.fill(lastX - 1, lastY - 1, lastX + 2, lastY + 2, TEXT_PRIMARY); // Center
            context.fill(lastX, lastY, lastX + 1, lastY + 1, HIGHLIGHT_MEDIUM); // Core highlight

            // Professional current price label with accent
            String currentPriceText = lastPoint.getPrice() + "💰";
            int labelWidth = this.textRenderer.getWidth(currentPriceText);
            int labelX = lastX - labelWidth / 2;
            int labelY = lastY - 15;

            // Accent price label background
            context.fill(labelX - 2, labelY - 1, labelX + labelWidth + 2, labelY + 9, CARD_BACKGROUND);
            context.drawBorder(labelX - 2, labelY - 1, labelWidth + 4, 10, TEXT_ACCENT);
            context.drawTextWithShadow(this.textRenderer, currentPriceText, labelX, labelY, TEXT_ACCENT);
        }

        // Enhanced hover tooltip for data points
        if (hoveredDataPoint >= 0 && hoveredDataPoint < priceHistory.size()) {
            PricePoint hoveredPoint = priceHistory.get(hoveredDataPoint);
            int hoveredX = graphX + (graphWidth * hoveredDataPoint / Math.max(1, priceHistory.size() - 1));
            int hoveredY = graphY + graphHeight - ((hoveredPoint.getPrice() - minPrice) * graphHeight / priceRange);

            // Enhanced tooltip content with improved day labels
            String dayLabel = getDayLabel(hoveredPoint.getDaysAgo());
            if (hoveredPoint.getDaysAgo() > 0) {
                dayLabel += " ago";
            }
            String priceLabel = hoveredPoint.getPrice() + "💰";

            // Calculate price change from previous day
            String changeLabel = "";
            if (hoveredDataPoint > 0) {
                PricePoint previousPoint = priceHistory.get(hoveredDataPoint - 1);
                int priceDiff = hoveredPoint.getPrice() - previousPoint.getPrice();
                double changePercent = ((double)priceDiff / previousPoint.getPrice()) * 100;
                String changeSymbol = priceDiff > 0 ? "↗" : priceDiff < 0 ? "↘" : "→";
                changeLabel = String.format("%s %.1f%%", changeSymbol, Math.abs(changePercent));
            }

            // Enhanced tooltip dimensions
            int tooltipWidth = Math.max(
                this.textRenderer.getWidth(dayLabel),
                Math.max(this.textRenderer.getWidth(priceLabel), this.textRenderer.getWidth(changeLabel))
            ) + 12;
            int tooltipHeight = changeLabel.isEmpty() ? 24 : 36;

            // Enhanced tooltip positioning
            int tooltipX = hoveredX - tooltipWidth / 2;
            int tooltipY = hoveredY - tooltipHeight - 8;

            // Keep tooltip within bounds
            tooltipX = Math.max(x + 4, Math.min(tooltipX, x + width - tooltipWidth - 4));
            tooltipY = Math.max(y + 4, tooltipY);

            // Professional balanced tooltip background
            context.fill(tooltipX + 1, tooltipY + 1, tooltipX + tooltipWidth + 1, tooltipY + tooltipHeight + 1, SHADOW_MEDIUM);
            context.fill(tooltipX, tooltipY, tooltipX + tooltipWidth, tooltipY + tooltipHeight, CARD_BACKGROUND);
            context.drawBorder(tooltipX, tooltipY, tooltipWidth, tooltipHeight, TEXT_ACCENT);
            context.fill(tooltipX, tooltipY, tooltipX + tooltipWidth, tooltipY + 2, TEXT_ACCENT);

            // Professional balanced tooltip text
            context.drawTextWithShadow(this.textRenderer, dayLabel, tooltipX + 6, tooltipY + 4, TEXT_PRIMARY);
            context.drawTextWithShadow(this.textRenderer, priceLabel, tooltipX + 6, tooltipY + 14, TEXT_ACCENT);
            if (!changeLabel.isEmpty()) {
                // Strategic trend indication with appropriate colors
                int changeColor = changeLabel.startsWith("↗") ? TEXT_SUCCESS :
                                 changeLabel.startsWith("↘") ? TEXT_ERROR : TEXT_MUTED;
                context.drawTextWithShadow(this.textRenderer, changeLabel, tooltipX + 6, tooltipY + 24, changeColor);
            }

            // Professional accent hover point highlight
            context.fill(hoveredX - 4, hoveredY - 4, hoveredX + 5, hoveredY + 5, SHADOW_MEDIUM);
            context.fill(hoveredX - 3, hoveredY - 3, hoveredX + 4, hoveredY + 4, TEXT_ACCENT);
            context.fill(hoveredX - 2, hoveredY - 2, hoveredX + 3, hoveredY + 3, TEXT_PRIMARY);
            context.fill(hoveredX - 1, hoveredY - 1, hoveredX + 2, hoveredY + 2, GRAPH_POINT_HIGHLIGHT);
        }
    }

    private void drawLine(DrawContext context, int x1, int y1, int x2, int y2, int color) {
        // Simple line drawing using filled rectangles
        int dx = Math.abs(x2 - x1);
        int dy = Math.abs(y2 - y1);
        int steps = Math.max(dx, dy);

        for (int i = 0; i <= steps; i++) {
            int x = x1 + (x2 - x1) * i / steps;
            int y = y1 + (y2 - y1) * i / steps;
            context.fill(x, y, x + 1, y + 1, color);
        }
    }

    private void drawModernTotalCalculation(DrawContext context, int x, int y, int width) {
        // Compact professional total calculation card
        int cardHeight = 20;
        int cardX = x + SPACING_XS;
        int cardY = y;
        int cardWidth = width - SPACING_XS * 2;

        // Compact professional card shadow
        context.fill(cardX + 1, cardY + 1, cardX + cardWidth + 1, cardY + cardHeight + 1, SHADOW_MEDIUM);

        // Professional total card background with success accent
        context.fill(cardX, cardY, cardX + cardWidth, cardY + cardHeight, SURFACE_ELEVATED);

        // Professional gradient system
        context.fill(cardX, cardY, cardX + cardWidth, cardY + cardHeight/3, HIGHLIGHT_MEDIUM);
        context.fill(cardX, cardY + cardHeight*2/3, cardX + cardWidth, cardY + cardHeight, SHADOW_LIGHT);

        // Professional border system with success color for financial total
        context.drawBorder(cardX, cardY, cardWidth, cardHeight, TEXT_SUCCESS);
        context.fill(cardX + 1, cardY + 1, cardX + cardWidth - 1, cardY + 2, HIGHLIGHT_MEDIUM);

        // Compact professional total calculation with enhanced typography
        int currentPrice = getCurrentPrice();
        int totalValue = selectedQuantity * currentPrice;
        String totalText = "Total: " + totalValue + "💰 (" + currentPrice + " each)";
        int totalWidth = this.textRenderer.getWidth(totalText);
        int textX = cardX + (cardWidth - totalWidth) / 2;
        int textY = cardY + (cardHeight - 8) / 2;

        // Compact professional text shadow
        context.drawText(this.textRenderer, totalText, textX + 1, textY + 1, SHADOW_MEDIUM, false);
        context.drawTextWithShadow(this.textRenderer, totalText, textX, textY, TEXT_PRIMARY);
    }

    private void drawModernActionBar(DrawContext context, int x, int y, int width, int height) {
        int actionBarY = y + height - BOTTOM_ACTION_BAR_HEIGHT;

        // Professional neutral separator above action bar
        context.fill(x + SPACING_SM, actionBarY - 1, x + width - SPACING_SM, actionBarY, BORDER_LIGHT);

        // Compact professional action bar background
        context.fill(x, actionBarY, x + width, y + height, GLASS_CONTENT_BG);

        // Compact professional gradient system
        context.fill(x, actionBarY, x + width, actionBarY + BOTTOM_ACTION_BAR_HEIGHT/2, HIGHLIGHT_MEDIUM);
        context.fill(x, actionBarY + BOTTOM_ACTION_BAR_HEIGHT/2, x + width, y + height, SHADOW_LIGHT);

        // Compact professional border system
        context.drawBorder(x, actionBarY, width, BOTTOM_ACTION_BAR_HEIGHT, BORDER_LIGHT);

        // Compact professional inner highlight
        context.fill(x + 1, actionBarY + 1, x + width - 1, actionBarY + 2, HIGHLIGHT_SUBTLE);
    }

    /**
     * Represents a price point in the price history graph
     */
    public static class PricePoint {
        private final int daysAgo;
        private final int price;
        private final long timestamp; // Add timestamp for better tracking

        public PricePoint(int daysAgo, int price) {
            this.daysAgo = daysAgo;
            this.price = price;
            this.timestamp = System.currentTimeMillis() - (daysAgo * 24L * 60L * 60L * 1000L);
        }

        public PricePoint(int daysAgo, int price, long timestamp) {
            this.daysAgo = daysAgo;
            this.price = price;
            this.timestamp = timestamp;
        }

        public int getDaysAgo() { return daysAgo; }
        public int getPrice() { return price; }
        public long getTimestamp() { return timestamp; }
    }

    /**
     * Sets up the price update listener to refresh the screen when prices change.
     */
    private void setupPriceUpdateListener() {
        try {
            var clientPriceManager = com.pokecobble.phone.food.client.ClientFoodPriceManager.getInstance();

            // Create listener that refreshes price data when prices update
            priceUpdateListener = (updatedPrices) -> {
                // Check if this product's price was updated
                if (updatedPrices.containsKey(product.getId())) {
                    // Reload price history with new data
                    loadRealPriceHistory();
                }
            };

            // Register the listener
            clientPriceManager.addPriceUpdateListener(priceUpdateListener);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Failed to setup price update listener: " + e.getMessage());
        }
    }

    /**
     * Requests fresh price data from server if the client doesn't have current data.
     */
    private void requestPriceDataIfNeeded() {
        try {
            var clientPriceManager = com.pokecobble.phone.food.client.ClientFoodPriceManager.getInstance();

            // Check if we have price data for this product
            if (!clientPriceManager.hasPriceData() || clientPriceManager.getCurrentPrice(product.getId()) <= 0) {
                // Request fresh data from server
                clientPriceManager.requestPriceUpdate();
                Pokecobbleclaim.LOGGER.debug("Requested fresh price data for {}", product.getId());
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Failed to request price data: " + e.getMessage());
        }
    }

    /**
     * Cleans up the price update listener when the screen is closed.
     */
    private void cleanupPriceUpdateListener() {
        if (priceUpdateListener != null) {
            try {
                var clientPriceManager = com.pokecobble.phone.food.client.ClientFoodPriceManager.getInstance();
                clientPriceManager.removePriceUpdateListener(priceUpdateListener);
                priceUpdateListener = null;
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Failed to cleanup price update listener: " + e.getMessage());
            }
        }
    }

    /**
     * Opens the product edit screen for modifying product details.
     */
    private void openProductEditScreen() {
        if (this.client == null) {
            return;
        }

        // Open the product edit screen
        ProductEditScreen editScreen = new ProductEditScreen(this.parent, product);
        this.client.setScreen(editScreen);
    }

    /**
     * Gets a user-friendly day label for the graph.
     */
    private String getDayLabel(int daysAgo) {
        switch (daysAgo) {
            case 0:
                return "Today";
            case 1:
                return "1d";
            case 2:
                return "2d";
            case 3:
                return "3d";
            case 4:
                return "4d";
            case 5:
                return "5d";
            case 6:
                return "6d";
            case 7:
                return "1w";
            case 8:
                return "8d";
            case 9:
                return "9d";
            default:
                return daysAgo + "d";
        }
    }

    @Override
    public void close() {
        cleanupPriceUpdateListener();
        this.client.setScreen(parent);
    }
}
