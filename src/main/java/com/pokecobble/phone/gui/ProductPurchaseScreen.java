package com.pokecobble.phone.gui;

import com.pokecobble.phone.FarmerEditModeManager;
import com.pokecobble.phone.network.ProductPurchasePacket;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;

/**
 * Modern purchase confirmation screen for farmer app products.
 * Features quantity selection, price calculation, and purchase confirmation.
 */
public class ProductPurchaseScreen extends Screen {
    private final Screen parent;
    private final FarmerAppScreen.Product product;
    
    // UI Components
    private TextFieldWidget quantityField;
    private ButtonWidget decreaseButton;
    private ButtonWidget increaseButton;
    private ButtonWidget maxButton;
    private ButtonWidget purchaseButton;
    private ButtonWidget cancelButton;
    private ButtonWidget editButton;
    
    // Panel dimensions - responsive and matching MyTownScreen style
    private int panelWidth = 400;
    private int panelHeight = 300;
    
    // Glass Effect Color Palette - matching MyTownScreen styling
    private static final int GLASS_PANEL_BG = 0xD0101010;      // Main panel background
    private static final int GLASS_HEADER_BG = 0x60404040;     // Header glass background
    private static final int GLASS_CONTENT_BG = 0x30000000;    // Content area background
    private static final int GLASS_CARD_BG = 0x40303030;       // Card backgrounds
    
    // Glass effect highlights and shadows
    private static final int GLASS_TOP_HIGHLIGHT = 0x20FFFFFF;    // Top glass highlight
    private static final int GLASS_BRIGHT_HIGHLIGHT = 0x40FFFFFF; // Brighter highlights
    private static final int GLASS_SHADOW = 0x40000000;          // Glass shadows
    
    // Text colors
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;     // Primary text
    private static final int TEXT_SECONDARY = 0xFFB0B0B0;   // Secondary text
    private static final int TEXT_MUTED = 0xFF808080;       // Muted text
    private static final int TEXT_SUCCESS = 0xFF4CAF50;     // Success color
    private static final int TEXT_WARNING = 0xFFFF9800;     // Warning color
    
    // Spacing
    private static final int SPACING_SM = 8;
    private static final int SPACING_MD = 12;
    private static final int HEADER_HEIGHT = 32;
    
    // Purchase state
    private int selectedQuantity = 1;
    private int maxQuantity = 64; // Default max, can be overridden
    private long playerBalance = 0; // Will be updated from economy system
    
    public ProductPurchaseScreen(Screen parent, FarmerAppScreen.Product product) {
        super(Text.literal("Purchase: " + product.getName()));
        this.parent = parent;
        this.product = product;
        
        // Set max quantity based on product type
        switch (product.getType()) {
            case UPGRADE:
            case UNLOCK:
                maxQuantity = 1; // These are typically one-time purchases
                break;
            case TOOL:
                maxQuantity = 5; // Limited tool purchases
                break;
            default:
                maxQuantity = 64; // Seeds and other stackable items
                break;
        }
        
        // Request current balance from economy system
        requestPlayerBalance();
    }
    
    @Override
    protected void init() {
        super.init();
        
        // Responsive panel sizing
        int minWidth = 350;
        int minHeight = 250;
        int maxWidth = 500;
        int maxHeight = 400;
        int margin = 20;
        
        panelWidth = Math.max(minWidth, Math.min(maxWidth, width - margin * 2));
        panelHeight = Math.max(minHeight, Math.min(maxHeight, height - margin * 2));
        
        // Center panel position
        int leftX = (width - panelWidth) / 2;
        int topY = Math.max(10, (height - panelHeight) / 2);
        
        // Content area
        int contentX = leftX + SPACING_MD;
        int contentY = topY + HEADER_HEIGHT + SPACING_SM;
        int contentWidth = panelWidth - SPACING_MD * 2;
        
        // Quantity selection area
        int quantityY = contentY + 80; // Space for product info
        setupQuantityControls(contentX, quantityY, contentWidth);
        
        // Purchase buttons
        int buttonY = topY + panelHeight - 50;
        setupPurchaseButtons(leftX, buttonY);
    }
    
    private void setupQuantityControls(int x, int y, int width) {
        int controlWidth = 200;
        int controlX = x + (width - controlWidth) / 2;
        
        // Decrease button
        decreaseButton = ButtonWidget.builder(Text.literal("-"), button -> {
            if (selectedQuantity > 1) {
                selectedQuantity--;
                updateQuantityField();
            }
        }).dimensions(controlX, y, 30, 20).build();
        this.addDrawableChild(decreaseButton);
        
        // Quantity field
        quantityField = new TextFieldWidget(this.textRenderer, controlX + 35, y, 60, 20, Text.literal("Quantity"));
        quantityField.setText(String.valueOf(selectedQuantity));
        quantityField.setMaxLength(3);
        quantityField.setChangedListener(this::onQuantityChanged);
        this.addSelectableChild(quantityField);
        this.addDrawableChild(quantityField);
        
        // Increase button
        increaseButton = ButtonWidget.builder(Text.literal("+"), button -> {
            if (selectedQuantity < maxQuantity) {
                selectedQuantity++;
                updateQuantityField();
            }
        }).dimensions(controlX + 100, y, 30, 20).build();
        this.addDrawableChild(increaseButton);
        
        // Max button
        maxButton = ButtonWidget.builder(Text.literal("Max"), button -> {
            selectedQuantity = Math.min(maxQuantity, getMaxAffordableQuantity());
            updateQuantityField();
        }).dimensions(controlX + 135, y, 35, 20).build();
        this.addDrawableChild(maxButton);
    }
    
    private void setupPurchaseButtons(int leftX, int buttonY) {
        boolean editModeEnabled = FarmerEditModeManager.getInstance().isEditModeEnabled();

        int buttonWidth = 100;
        int buttonSpacing = 10;

        // Calculate button layout based on whether edit mode is enabled
        int buttonCount = editModeEnabled ? 3 : 2;
        int totalWidth = buttonWidth * buttonCount + buttonSpacing * (buttonCount - 1);
        int buttonStartX = leftX + (panelWidth - totalWidth) / 2;

        int currentX = buttonStartX;

        // Edit button (only if edit mode is enabled)
        if (editModeEnabled) {
            editButton = ButtonWidget.builder(Text.literal("✏ Edit"), button -> {
                openProductEditScreen();
            }).dimensions(currentX, buttonY, buttonWidth, 24).build();
            this.addDrawableChild(editButton);
            currentX += buttonWidth + buttonSpacing;
        }

        // Purchase button
        purchaseButton = ButtonWidget.builder(Text.literal("💰 Purchase"), button -> {
            confirmPurchase();
        }).dimensions(currentX, buttonY, buttonWidth, 24).build();
        this.addDrawableChild(purchaseButton);
        currentX += buttonWidth + buttonSpacing;

        // Cancel button
        cancelButton = ButtonWidget.builder(Text.literal("❌ Cancel"), button -> {
            this.close();
        }).dimensions(currentX, buttonY, buttonWidth, 24).build();
        this.addDrawableChild(cancelButton);
    }
    
    private void onQuantityChanged(String text) {
        try {
            int newQuantity = Integer.parseInt(text);
            if (newQuantity >= 1 && newQuantity <= maxQuantity) {
                selectedQuantity = newQuantity;
            }
        } catch (NumberFormatException e) {
            // Invalid input, revert to current quantity
            updateQuantityField();
        }
    }
    
    private void updateQuantityField() {
        if (quantityField != null) {
            quantityField.setText(String.valueOf(selectedQuantity));
        }
    }
    
    private int getMaxAffordableQuantity() {
        long itemPrice = product.getDiscountedPrice();
        if (itemPrice <= 0) return maxQuantity;
        
        int affordable = (int) (playerBalance / itemPrice);
        return Math.min(affordable, maxQuantity);
    }
    
    private void confirmPurchase() {
        if (selectedQuantity <= 0) {
            return;
        }
        
        long totalPrice = (long) product.getDiscountedPrice() * selectedQuantity;
        
        // Check if player can afford it
        if (totalPrice > playerBalance) {
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(
                    Text.literal("§cInsufficient funds! Need " + totalPrice + " coins, have " + playerBalance),
                    true
                );
            }
            return;
        }
        
        // Send purchase request to server
        ProductPurchasePacket.sendPurchaseRequest(product.getId(), selectedQuantity, totalPrice);
        
        // Close the screen
        this.close();
    }
    
    private void requestPlayerBalance() {
        try {
            // Request balance from economy system
            net.minecraft.network.PacketByteBuf buf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                com.pokecobble.economy.network.EconomyNetworkManager.ECONOMY_BALANCE_REQUEST, buf
            );
        } catch (Exception e) {
            // Economy system not available, assume 0 balance
            playerBalance = 0;
        }
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render dark background
        this.renderBackground(context);
        
        // Calculate panel position
        int leftX = (width - panelWidth) / 2;
        int topY = Math.max(10, (height - panelHeight) / 2);
        
        // Draw glass effect panel
        drawGlassPanel(context, leftX, topY, panelWidth, panelHeight);
        
        // Draw header
        drawHeader(context, leftX, topY, panelWidth);
        
        // Draw content
        drawContent(context, leftX, topY);
        
        super.render(context, mouseX, mouseY, delta);
    }
    
    private void drawGlassPanel(DrawContext context, int x, int y, int width, int height) {
        // Main panel background
        context.fill(x, y, x + width, y + height, GLASS_PANEL_BG);
        
        // Glass effect borders
        context.fill(x, y, x + width, y + 1, GLASS_BRIGHT_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, GLASS_TOP_HIGHLIGHT); // Right highlight
        context.fill(x, y + height - 1, x + width, y + height, GLASS_SHADOW); // Bottom shadow
    }
    
    private void drawHeader(DrawContext context, int x, int y, int width) {
        // Header background
        context.fill(x, y, x + width, y + HEADER_HEIGHT, GLASS_HEADER_BG);
        
        // Header borders
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT);
        context.fill(x, y, x + 1, y + HEADER_HEIGHT, GLASS_TOP_HIGHLIGHT);
        
        // Title
        String title = "💰 Purchase: " + product.getName();
        int titleWidth = this.textRenderer.getWidth(title);
        int titleX = x + (width - titleWidth) / 2;
        int titleY = y + (HEADER_HEIGHT - 8) / 2;
        
        context.drawTextWithShadow(this.textRenderer, title, titleX, titleY, TEXT_PRIMARY);
    }
    
    private void drawContent(DrawContext context, int leftX, int topY) {
        int contentX = leftX + SPACING_MD;
        int contentY = topY + HEADER_HEIGHT + SPACING_SM;
        
        // Product info section
        drawProductInfo(context, contentX, contentY);
        
        // Price calculation section
        drawPriceCalculation(context, contentX, contentY + 120);
        
        // Balance info
        drawBalanceInfo(context, contentX, contentY + 160);
    }
    
    private void drawProductInfo(DrawContext context, int x, int y) {
        // Product name and type
        String typeIcon = getProductTypeIcon(product.getType());
        String productInfo = typeIcon + " " + product.getName();
        context.drawTextWithShadow(this.textRenderer, productInfo, x, y, TEXT_PRIMARY);
        
        // Description
        context.drawTextWithShadow(this.textRenderer, product.getDescription(), x, y + 12, TEXT_SECONDARY);
        
        // Base price
        String priceText = "Base Price: " + product.getPrice() + " coins";
        if (product.isOnSale()) {
            priceText += " §7(§c-" + product.getDiscountPercent() + "%§7)";
        }
        context.drawTextWithShadow(this.textRenderer, priceText, x, y + 24, TEXT_MUTED);
        
        // Final price per item
        String finalPriceText = "Price per item: " + product.getDiscountedPrice() + " coins";
        int priceColor = product.isOnSale() ? TEXT_SUCCESS : TEXT_PRIMARY;
        context.drawTextWithShadow(this.textRenderer, finalPriceText, x, y + 36, priceColor);
    }
    
    private void drawPriceCalculation(DrawContext context, int x, int y) {
        long totalPrice = (long) product.getDiscountedPrice() * selectedQuantity;
        
        String calculation = selectedQuantity + " × " + product.getDiscountedPrice() + " = " + totalPrice + " coins";
        context.drawTextWithShadow(this.textRenderer, "Total: " + calculation, x, y, TEXT_PRIMARY);
    }
    
    private void drawBalanceInfo(DrawContext context, int x, int y) {
        long totalPrice = (long) product.getDiscountedPrice() * selectedQuantity;
        boolean canAfford = totalPrice <= playerBalance;
        
        String balanceText = "Your Balance: " + playerBalance + " coins";
        int balanceColor = canAfford ? TEXT_SUCCESS : TEXT_WARNING;
        context.drawTextWithShadow(this.textRenderer, balanceText, x, y, balanceColor);
        
        if (!canAfford) {
            long needed = totalPrice - playerBalance;
            String neededText = "Need " + needed + " more coins";
            context.drawTextWithShadow(this.textRenderer, neededText, x, y + 12, TEXT_WARNING);
        }
    }
    
    private String getProductTypeIcon(FarmerAppScreen.ProductType type) {
        switch (type) {
            case SEED: return "🌱";
            case FOOD: return "🥕";
            case TOOL: return "🔧";
            case UPGRADE: return "⬆️";
            case UNLOCK: return "🔓";
            default: return "📦";
        }
    }

    /**
     * Opens the product edit screen for modifying product details.
     */
    private void openProductEditScreen() {
        if (this.client == null) {
            return;
        }

        // Open the product edit screen
        ProductEditScreen editScreen = new ProductEditScreen(this.parent, product);
        this.client.setScreen(editScreen);
    }

    @Override
    public void close() {
        this.client.setScreen(parent);
    }
    
    @Override
    public boolean shouldPause() {
        return false;
    }
}
