package com.pokecobble.phone.app;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.gui.FarmerAppScreen;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.Identifier;

/**
 * The Farmer app that opens the FarmerAppScreen.
 */
public class FarmerApp extends App {
    private static final Identifier ICON_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/farmerapp/farmer.png");

    /**
     * Creates a new Farmer app.
     */
    public FarmerApp() {
        super("farmer", "Farmer", ICON_TEXTURE);
    }

    @Override
    public void open(MinecraftClient client) {
        Pokecobbleclaim.LOGGER.debug("Opening Farmer app");
        
        // Open the FarmerAppScreen
        client.execute(() -> client.setScreen(new FarmerAppScreen(client.currentScreen)));
    }
}
