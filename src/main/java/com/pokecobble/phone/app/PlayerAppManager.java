package com.pokecobble.phone.app;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.pokecobble.Pokecobbleclaim;
import net.minecraft.client.MinecraftClient;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.*;

/**
 * Manages which apps are available to each player.
 */
public class PlayerAppManager {
    private static final PlayerAppManager INSTANCE = new PlayerAppManager();
    private static final Gson GSON = new Gson();
    
    // Apps that are available to all players by default
    private static final Set<String> DEFAULT_APPS = Set.of(
        "town", "bank", "settings", "shop", "trading"
    );
    
    // Player-specific app availability
    private final Map<UUID, Set<String>> playerApps = new HashMap<>();
    private boolean dataLoaded = false;

    /**
     * Gets the player app manager instance.
     */
    public static PlayerAppManager getInstance() {
        return INSTANCE;
    }

    /**
     * Gets all apps available to a specific player.
     */
    public List<App> getAvailableApps(UUID playerId) {
        ensureDataLoaded();
        
        Set<String> availableAppIds = new HashSet<>(DEFAULT_APPS);
        availableAppIds.addAll(playerApps.getOrDefault(playerId, new HashSet<>()));
        
        List<App> availableApps = new ArrayList<>();
        for (App app : AppRegistry.getInstance().getAllApps()) {
            if (availableAppIds.contains(app.getId())) {
                availableApps.add(app);
            }
        }
        
        return availableApps;
    }

    /**
     * Checks if a player has access to a specific app.
     */
    public boolean hasApp(UUID playerId, String appId) {
        ensureDataLoaded();
        
        // Default apps are always available
        if (DEFAULT_APPS.contains(appId)) {
            return true;
        }
        
        Set<String> playerSpecificApps = playerApps.get(playerId);
        return playerSpecificApps != null && playerSpecificApps.contains(appId);
    }

    /**
     * Adds an app to a player's available apps.
     */
    public void addAppToPlayer(UUID playerId, String appId) {
        ensureDataLoaded();
        
        // Don't add default apps (they're always available)
        if (DEFAULT_APPS.contains(appId)) {
            return;
        }
        
        playerApps.computeIfAbsent(playerId, k -> new HashSet<>()).add(appId);
        saveData();
        
        Pokecobbleclaim.LOGGER.info("Added app '{}' to player {}", appId, playerId);
    }

    /**
     * Removes an app from a player's available apps.
     */
    public void removeAppFromPlayer(UUID playerId, String appId) {
        ensureDataLoaded();
        
        Set<String> playerSpecificApps = playerApps.get(playerId);
        if (playerSpecificApps != null) {
            playerSpecificApps.remove(appId);
            if (playerSpecificApps.isEmpty()) {
                playerApps.remove(playerId);
            }
            saveData();
            
            Pokecobbleclaim.LOGGER.info("Removed app '{}' from player {}", appId, playerId);
        }
    }

    /**
     * Ensures data is loaded from disk.
     */
    private void ensureDataLoaded() {
        if (!dataLoaded) {
            loadData();
            dataLoaded = true;
        }
    }

    /**
     * Loads player app data from disk.
     */
    private void loadData() {
        try {
            File dataFile = getDataFile();
            if (!dataFile.exists()) {
                Pokecobbleclaim.LOGGER.info("Player app data file doesn't exist, using defaults");
                return;
            }

            try (FileReader reader = new FileReader(dataFile)) {
                Type type = new TypeToken<Map<String, Set<String>>>() {}.getType();
                Map<String, Set<String>> loadedData = GSON.fromJson(reader, type);
                
                if (loadedData != null) {
                    playerApps.clear();
                    for (Map.Entry<String, Set<String>> entry : loadedData.entrySet()) {
                        try {
                            UUID playerId = UUID.fromString(entry.getKey());
                            playerApps.put(playerId, new HashSet<>(entry.getValue()));
                        } catch (IllegalArgumentException e) {
                            Pokecobbleclaim.LOGGER.warn("Invalid UUID in player app data: {}", entry.getKey());
                        }
                    }
                    Pokecobbleclaim.LOGGER.info("Loaded player app data for {} players", playerApps.size());
                }
            }
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Error loading player app data: {}", e.getMessage());
        }
    }

    /**
     * Saves player app data to disk.
     */
    private void saveData() {
        try {
            File dataFile = getDataFile();
            dataFile.getParentFile().mkdirs();

            // Convert UUID keys to strings for JSON serialization
            Map<String, Set<String>> saveData = new HashMap<>();
            for (Map.Entry<UUID, Set<String>> entry : playerApps.entrySet()) {
                saveData.put(entry.getKey().toString(), entry.getValue());
            }

            try (FileWriter writer = new FileWriter(dataFile)) {
                GSON.toJson(saveData, writer);
            }
            
            Pokecobbleclaim.LOGGER.debug("Saved player app data for {} players", playerApps.size());
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Error saving player app data: {}", e.getMessage());
        }
    }

    /**
     * Gets the data file for storing player app information.
     */
    private File getDataFile() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.runDirectory != null) {
            return new File(client.runDirectory, "config/pokecobbleclaim/player_apps.json");
        }
        return new File("config/pokecobbleclaim/player_apps.json");
    }

    /**
     * Clears all data (for testing purposes).
     */
    public void clearData() {
        playerApps.clear();
        dataLoaded = false;
        saveData();
    }
}
