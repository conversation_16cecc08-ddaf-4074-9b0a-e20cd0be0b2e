package com.pokecobble.phone.network;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.client.ClientProductManager;
import com.pokecobble.phone.data.ServerProductData;
import com.pokecobble.phone.manager.ServerProductManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.Map;

/**
 * Manages all product-related network communication between client and server.
 * Handles product updates, synchronization, and full sync requests.
 */
public class ProductNetworkManager {
    public static final Identifier PRODUCT_SYNC_REQUEST = new Identifier("pokecobbleclaim", "product_sync_request");
    public static final Identifier PRODUCT_FULL_SYNC = new Identifier("pokecobbleclaim", "product_full_sync");
    
    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register product update handler
        ProductUpdatePacket.registerServerHandler();

        // Register product create handler
        ProductCreatePacket.registerServerHandler();

        // Register product sync request handler
        ServerPlayNetworking.registerGlobalReceiver(PRODUCT_SYNC_REQUEST, (server, player, handler, buf, responseSender) -> {
            long clientVersion = buf.readLong();

            server.execute(() -> {
                handleProductSyncRequest(player, clientVersion);
            });
        });

        Pokecobbleclaim.LOGGER.info("Registered server-side product network handlers");
    }
    
    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register product sync handler
        ProductUpdatePacket.registerClientSyncHandler();

        // Register product create response handler
        ProductCreatePacket.registerClientHandler();

        // Register full sync handler
        ClientPlayNetworking.registerGlobalReceiver(PRODUCT_FULL_SYNC, (client, handler, buf, responseSender) -> {
            try {
                int productCount = buf.readInt();

                // Read all product data in the network thread to avoid buffer release issues
                java.util.List<ProductSyncData> products = new java.util.ArrayList<>();

                for (int i = 0; i < productCount; i++) {
                    String productId = buf.readString();
                    String name = buf.readString();
                    int price = buf.readInt();
                    String description = buf.readString();
                    int requiredLevel = buf.readInt();
                    String typeName = buf.readString();
                    String iconPath = buf.readString();
                    String itemId = buf.readString();
                    boolean onSale = buf.readBoolean();
                    int discountPercent = buf.readInt();
                    String categoryDataJson = buf.readString();
                    long dataVersion = buf.readLong();

                    products.add(new ProductSyncData(productId, name, price, description, requiredLevel,
                        typeName, iconPath, itemId, onSale, discountPercent, categoryDataJson, dataVersion));
                }

                client.execute(() -> {
                    handleFullProductSync(products);
                });

            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error reading product sync data", e);
            }
        });

        Pokecobbleclaim.LOGGER.info("Registered client-side product network handlers");
    }
    
    /**
     * Handles product sync requests from clients.
     */
    private static void handleProductSyncRequest(ServerPlayerEntity player, long clientVersion) {
        try {
            ServerProductManager productManager = ServerProductManager.getInstance();
            Map<String, ServerProductData> allProducts = productManager.getAllProducts();
            
            // Check if client needs a full sync
            long serverVersion = productManager.getDataVersion();
            boolean needsFullSync = clientVersion < serverVersion || allProducts.isEmpty();
            
            if (needsFullSync) {
                sendFullProductSync(player, allProducts);
            } else {
                // Client is up to date
                Pokecobbleclaim.LOGGER.debug("Client {} is up to date (version: {})", 
                    player.getName().getString(), clientVersion);
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to handle product sync request from " + 
                player.getName().getString(), e);
        }
    }
    
    /**
     * Sends full product synchronization to a player.
     */
    public static void sendFullProductSync(ServerPlayerEntity player, Map<String, ServerProductData> products) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeInt(products.size());
            
            for (ServerProductData product : products.values()) {
                // Write product data
                buf.writeString(product.getId());
                buf.writeString(product.getName());
                buf.writeInt(product.getPrice());
                buf.writeString(product.getDescription());
                buf.writeInt(product.getRequiredLevel());
                buf.writeString(product.getType().name());
                buf.writeString(product.getIconPath() != null ? product.getIconPath() : "");
                buf.writeString(product.getItemId() != null ? product.getItemId() : "");
                buf.writeBoolean(product.isOnSale());
                buf.writeInt(product.getDiscountPercent());
                
                // Serialize category data
                String categoryDataJson = new com.google.gson.Gson().toJson(product.getCategoryData());
                buf.writeString(categoryDataJson);
                
                // Data version
                buf.writeLong(product.getDataVersion());
            }
            
            ServerPlayNetworking.send(player, PRODUCT_FULL_SYNC, buf);

            Pokecobbleclaim.LOGGER.info("Sent full product sync to {} ({} products)",
                player.getName().getString(), products.size());

            // Debug: Log first few product names
            if (!products.isEmpty()) {
                java.util.List<String> productNames = products.values().stream()
                    .limit(3)
                    .map(p -> p.getName())
                    .collect(java.util.stream.Collectors.toList());
                Pokecobbleclaim.LOGGER.debug("Sample products sent: {}", productNames);
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to send full product sync to " + 
                player.getName().getString(), e);
        }
    }
    
    /**
     * Sends full product synchronization to all connected players.
     */
    public static void sendFullProductSyncToAll(net.minecraft.server.MinecraftServer server) {
        try {
            ServerProductManager productManager = ServerProductManager.getInstance();
            Map<String, ServerProductData> allProducts = productManager.getAllProducts();
            
            int playerCount = 0;
            for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
                sendFullProductSync(player, allProducts);
                playerCount++;
            }
            
            Pokecobbleclaim.LOGGER.info("Sent full product sync to {} players", playerCount);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to send full product sync to all players", e);
        }
    }
    
    /**
     * Handles full product synchronization on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleFullProductSync(java.util.List<ProductSyncData> products) {
        try {
            ClientProductManager clientManager = ClientProductManager.getInstance();

            // Clear existing cache
            clientManager.clearCache();

            // Process all products
            for (ProductSyncData productData : products) {
                // Parse category data
                java.util.Map<String, Object> categoryData = new java.util.HashMap<>();
                try {
                    if (!productData.categoryDataJson.isEmpty()) {
                        com.google.gson.reflect.TypeToken<java.util.Map<String, Object>> typeToken =
                            new com.google.gson.reflect.TypeToken<java.util.Map<String, Object>>(){};
                        categoryData = new com.google.gson.Gson().fromJson(productData.categoryDataJson, typeToken.getType());
                        if (categoryData == null) {
                            categoryData = new java.util.HashMap<>();
                        }
                    }
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.warn("Failed to parse category data for product {}: {}",
                        productData.productId, e.getMessage());
                    categoryData = new java.util.HashMap<>();
                }

                // Update client cache
                clientManager.updateProduct(productData.productId, productData.name, productData.price,
                    productData.description, productData.requiredLevel, productData.typeName,
                    productData.iconPath, productData.itemId, productData.onSale, productData.discountPercent,
                    categoryData, productData.dataVersion);
            }

            Pokecobbleclaim.LOGGER.info("Received full product sync: {} products", products.size());

            // Debug: Log first few product names
            if (!products.isEmpty()) {
                java.util.List<String> productNames = products.stream()
                    .limit(3)
                    .map(p -> p.name)
                    .collect(java.util.stream.Collectors.toList());
                Pokecobbleclaim.LOGGER.debug("Sample products received: {}", productNames);
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to handle full product sync", e);
        }
    }
    
    /**
     * Requests a product sync from the server (client-side).
     */
    @Environment(EnvType.CLIENT)
    public static void requestProductSync() {
        try {
            ClientProductManager clientManager = ClientProductManager.getInstance();
            long currentVersion = clientManager.getLastSyncVersion();
            
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeLong(currentVersion);
            
            ClientPlayNetworking.send(PRODUCT_SYNC_REQUEST, buf);
            
            Pokecobbleclaim.LOGGER.info("Requested product sync from server (current version: {})", currentVersion);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to request product sync", e);
        }
    }
    
    /**
     * Initializes the product network system.
     */
    public static void initialize() {
        // This method can be called from both client and server
        Pokecobbleclaim.LOGGER.info("Initializing product network system");
    }

    /**
     * Data class for holding product sync information.
     */
    private static class ProductSyncData {
        final String productId;
        final String name;
        final int price;
        final String description;
        final int requiredLevel;
        final String typeName;
        final String iconPath;
        final String itemId;
        final boolean onSale;
        final int discountPercent;
        final String categoryDataJson;
        final long dataVersion;

        ProductSyncData(String productId, String name, int price, String description, int requiredLevel,
                       String typeName, String iconPath, String itemId, boolean onSale, int discountPercent,
                       String categoryDataJson, long dataVersion) {
            this.productId = productId;
            this.name = name;
            this.price = price;
            this.description = description;
            this.requiredLevel = requiredLevel;
            this.typeName = typeName;
            this.iconPath = iconPath;
            this.itemId = itemId;
            this.onSale = onSale;
            this.discountPercent = discountPercent;
            this.categoryDataJson = categoryDataJson;
            this.dataVersion = dataVersion;
        }
    }
}
