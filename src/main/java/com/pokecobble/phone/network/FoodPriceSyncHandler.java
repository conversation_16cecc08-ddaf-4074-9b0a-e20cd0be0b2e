package com.pokecobble.phone.network;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.food.data.FoodPriceData;
import com.pokecobble.town.network.NetworkConstants;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.List;
import java.util.Map;

/**
 * Handles client-server synchronization of food price data.
 * Manages sending price updates and price history to clients.
 */
public class FoodPriceSyncHandler {
    
    // Network packet identifiers
    public static final Identifier FOOD_PRICE_SYNC = new Identifier("pokecobbleclaim", "food_price_sync");
    public static final Identifier FOOD_PRICE_UPDATE = new Identifier("pokecobbleclaim", "food_price_update");
    public static final Identifier FOOD_PRICE_REQUEST = new Identifier("pokecobbleclaim", "food_price_request");
    
    /**
     * Registers server-side network handlers.
     */
    public static void registerServerHandlers() {
        // Handle client requests for price data
        ServerPlayNetworking.registerGlobalReceiver(FOOD_PRICE_REQUEST, FoodPriceSyncHandler::handlePriceRequest);
        
        Pokecobbleclaim.LOGGER.debug("Registered server-side food price network handlers");
    }
    
    /**
     * Registers client-side network handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Handle price sync from server
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
            FOOD_PRICE_SYNC, FoodPriceSyncHandler::handlePriceSync);
        
        // Handle price updates from server
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
            FOOD_PRICE_UPDATE, FoodPriceSyncHandler::handlePriceUpdate);
        
        Pokecobbleclaim.LOGGER.debug("Registered client-side food price network handlers");
    }
    
    /**
     * Synchronizes current prices and price history to a specific player.
     */
    public static void syncPricesToPlayer(ServerPlayerEntity player, 
                                        Map<String, Integer> currentPrices,
                                        Map<String, List<FoodPriceData.PriceHistoryEntry>> priceHistory) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Write sync type (full sync)
            buf.writeBoolean(true);
            
            // Write current prices
            buf.writeInt(currentPrices.size());
            for (Map.Entry<String, Integer> entry : currentPrices.entrySet()) {
                buf.writeString(entry.getKey(), NetworkConstants.MAX_STRING_LENGTH);
                buf.writeInt(entry.getValue());
            }
            
            // Write price history
            buf.writeInt(priceHistory.size());
            for (Map.Entry<String, List<FoodPriceData.PriceHistoryEntry>> entry : priceHistory.entrySet()) {
                buf.writeString(entry.getKey(), NetworkConstants.MAX_STRING_LENGTH);
                
                List<FoodPriceData.PriceHistoryEntry> history = entry.getValue();
                buf.writeInt(history.size());
                
                for (FoodPriceData.PriceHistoryEntry historyEntry : history) {
                    buf.writeLong(historyEntry.getTimestamp());
                    buf.writeInt(historyEntry.getPrice());
                }
            }
            
            // Write timestamp
            buf.writeLong(System.currentTimeMillis());
            
            ServerPlayNetworking.send(player, FOOD_PRICE_SYNC, buf);
            
            Pokecobbleclaim.LOGGER.debug("Sent food price sync to player {}", player.getName().getString());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to sync prices to player {}: {}", 
                player.getName().getString(), e.getMessage());
        }
    }
    
    /**
     * Synchronizes prices to all connected players.
     */
    public static void syncPricesToAllPlayers(MinecraftServer server,
                                            Map<String, Integer> currentPrices,
                                            Map<String, List<FoodPriceData.PriceHistoryEntry>> priceHistory) {
        for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
            syncPricesToPlayer(player, currentPrices, priceHistory);
        }
        
        Pokecobbleclaim.LOGGER.info("Synchronized food prices to {} players", 
            server.getPlayerManager().getPlayerList().size());
    }
    
    /**
     * Sends a price update notification to all players.
     */
    public static void notifyPriceUpdate(MinecraftServer server,
                                       Map<String, Integer> newPrices) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Write update type (price update only)
            buf.writeBoolean(false);
            
            // Write new prices
            buf.writeInt(newPrices.size());
            for (Map.Entry<String, Integer> entry : newPrices.entrySet()) {
                buf.writeString(entry.getKey(), NetworkConstants.MAX_STRING_LENGTH);
                buf.writeInt(entry.getValue());
            }
            
            // Write timestamp
            buf.writeLong(System.currentTimeMillis());
            
            // Send to all players
            for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
                ServerPlayNetworking.send(player, FOOD_PRICE_UPDATE, buf);
            }
            
            Pokecobbleclaim.LOGGER.info("Sent price update notification to {} players", 
                server.getPlayerManager().getPlayerList().size());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to send price update notification: " + e.getMessage());
        }
    }
    
    /**
     * Handles client requests for price data.
     */
    private static void handlePriceRequest(MinecraftServer server, ServerPlayerEntity player,
                                         net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                         PacketByteBuf buf, PacketSender responseSender) {
        try {
            // Get current price data from the price manager
            var priceManager = com.pokecobble.phone.food.FoodPriceManager.getInstance();
            priceManager.syncPricesToPlayer(player);
            
            Pokecobbleclaim.LOGGER.debug("Handled price request from player {}", player.getName().getString());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling price request from {}: {}", 
                player.getName().getString(), e.getMessage());
        }
    }
    
    /**
     * Client-side handler for price sync packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handlePriceSync(net.minecraft.client.MinecraftClient client,
                                      net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                      PacketByteBuf buf,
                                      PacketSender responseSender) {
        try {
            // Read sync type
            boolean isFullSync = buf.readBoolean();
            
            // Read current prices
            int priceCount = buf.readInt();
            Map<String, Integer> currentPrices = new java.util.HashMap<>();
            
            for (int i = 0; i < priceCount; i++) {
                String foodItem = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                int price = buf.readInt();
                currentPrices.put(foodItem, price);
            }
            
            // Read price history
            int historyCount = buf.readInt();
            Map<String, List<FoodPriceData.PriceHistoryEntry>> priceHistory = new java.util.HashMap<>();
            
            for (int i = 0; i < historyCount; i++) {
                String foodItem = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                int entryCount = buf.readInt();
                
                List<FoodPriceData.PriceHistoryEntry> history = new java.util.ArrayList<>();
                for (int j = 0; j < entryCount; j++) {
                    long timestamp = buf.readLong();
                    int price = buf.readInt();
                    history.add(new FoodPriceData.PriceHistoryEntry(timestamp, price));
                }
                
                priceHistory.put(foodItem, history);
            }
            
            // Read timestamp
            long timestamp = buf.readLong();
            
            // Update client-side price manager
            client.execute(() -> {
                com.pokecobble.phone.food.client.ClientFoodPriceManager.getInstance()
                    .updatePrices(currentPrices, priceHistory, timestamp);
                
                // Fire sync event for UI updates
                com.pokecobble.town.client.ClientSyncManager.getInstance()
                    .fireEvent("food_prices_updated", currentPrices);
            });
            
            Pokecobbleclaim.LOGGER.debug("Received food price sync from server ({} items)", priceCount);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling price sync: " + e.getMessage());
        }
    }
    
    /**
     * Client-side handler for price update packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handlePriceUpdate(net.minecraft.client.MinecraftClient client,
                                        net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                        PacketByteBuf buf,
                                        PacketSender responseSender) {
        try {
            // Read update type
            boolean isFullSync = buf.readBoolean();
            
            // Read new prices
            int priceCount = buf.readInt();
            Map<String, Integer> newPrices = new java.util.HashMap<>();
            
            for (int i = 0; i < priceCount; i++) {
                String foodItem = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                int price = buf.readInt();
                newPrices.put(foodItem, price);
            }
            
            // Read timestamp
            long timestamp = buf.readLong();
            
            // Update client-side price manager
            client.execute(() -> {
                com.pokecobble.phone.food.client.ClientFoodPriceManager.getInstance()
                    .updateCurrentPrices(newPrices, timestamp);
                
                // Fire update event for UI updates
                com.pokecobble.town.client.ClientSyncManager.getInstance()
                    .fireEvent("food_prices_updated", newPrices);
                
                // Show notification to player about price changes
                if (client.player != null) {
                    client.player.sendMessage(
                        net.minecraft.text.Text.literal("§6Food prices have been updated!"), 
                        true // Show as action bar message
                    );
                }
            });
            
            Pokecobbleclaim.LOGGER.debug("Received food price update from server ({} items)", priceCount);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling price update: " + e.getMessage());
        }
    }
    
    /**
     * Requests current price data from the server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestPriceData() {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeLong(System.currentTimeMillis()); // Request timestamp
            
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(FOOD_PRICE_REQUEST, buf);
            
            Pokecobbleclaim.LOGGER.debug("Requested price data from server");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to request price data: " + e.getMessage());
        }
    }
}
