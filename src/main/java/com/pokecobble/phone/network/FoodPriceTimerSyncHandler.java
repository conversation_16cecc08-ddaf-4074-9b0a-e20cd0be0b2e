package com.pokecobble.phone.network;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.food.data.FoodPriceTimerData;
import com.pokecobble.town.network.NetworkConstants;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

/**
 * Handles client-server synchronization of food price timer data.
 */
public class FoodPriceTimerSyncHandler {
    
    // Network packet identifiers
    public static final Identifier TIMER_SYNC = new Identifier("pokecobbleclaim", "food_timer_sync");
    public static final Identifier TIMER_REQUEST = new Identifier("pokecobbleclaim", "food_timer_request");
    
    /**
     * Registers server-side network handlers.
     */
    public static void registerServerHandlers() {
        // Handle client requests for timer data
        ServerPlayNetworking.registerGlobalReceiver(TIMER_REQUEST, FoodPriceTimerSyncHandler::handleTimerRequest);
        
        Pokecobbleclaim.LOGGER.debug("Registered server-side food price timer network handlers");
    }
    
    /**
     * Registers client-side network handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Handle timer sync from server
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
            TIMER_SYNC, FoodPriceTimerSyncHandler::handleTimerSync);
        
        Pokecobbleclaim.LOGGER.debug("Registered client-side food price timer network handlers");
    }
    
    /**
     * Synchronizes timer data to a specific player.
     */
    public static void syncTimerToPlayer(ServerPlayerEntity player, FoodPriceTimerData timerData) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Write timer data
            buf.writeLong(timerData.getNextPriceUpdateTime());
            buf.writeLong(timerData.getLastPriceUpdateTime());
            buf.writeLong(timerData.getDataVersion());
            buf.writeLong(System.currentTimeMillis()); // Current server time for sync
            
            ServerPlayNetworking.send(player, TIMER_SYNC, buf);
            
            Pokecobbleclaim.LOGGER.debug("Sent timer sync to player {} (next update: {})", 
                player.getName().getString(), timerData.getFormattedTimeUntilNextUpdate());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to sync timer to player {}: {}", 
                player.getName().getString(), e.getMessage());
        }
    }
    
    /**
     * Synchronizes timer data to all connected players.
     */
    public static void syncTimerToAllPlayers(MinecraftServer server, FoodPriceTimerData timerData) {
        for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
            syncTimerToPlayer(player, timerData);
        }
        
        Pokecobbleclaim.LOGGER.debug("Synchronized timer data to {} players", 
            server.getPlayerManager().getPlayerList().size());
    }
    
    /**
     * Handles client requests for timer data.
     */
    private static void handleTimerRequest(MinecraftServer server, ServerPlayerEntity player,
                                         net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                         PacketByteBuf buf, PacketSender responseSender) {
        try {
            // Get current timer data from the timer manager
            var timerManager = com.pokecobble.phone.food.FoodPriceTimer.getInstance();
            var timerData = timerManager.getTimerData();
            
            if (timerData != null) {
                syncTimerToPlayer(player, timerData);
            } else {
                Pokecobbleclaim.LOGGER.warn("No timer data available for player {}", player.getName().getString());
            }
            
            Pokecobbleclaim.LOGGER.debug("Handled timer request from player {}", player.getName().getString());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling timer request from {}: {}", 
                player.getName().getString(), e.getMessage());
        }
    }
    
    /**
     * Client-side handler for timer sync packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTimerSync(net.minecraft.client.MinecraftClient client,
                                      net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                      PacketByteBuf buf,
                                      PacketSender responseSender) {
        try {
            // Read timer data
            long nextPriceUpdateTime = buf.readLong();
            long lastPriceUpdateTime = buf.readLong();
            long dataVersion = buf.readLong();
            long serverTime = buf.readLong();
            
            // Calculate time offset between client and server
            long clientTime = System.currentTimeMillis();
            long timeOffset = serverTime - clientTime;
            
            // Adjust times for client-server time difference
            long adjustedNextUpdate = nextPriceUpdateTime - timeOffset;
            long adjustedLastUpdate = lastPriceUpdateTime - timeOffset;
            
            // Update client-side timer manager
            client.execute(() -> {
                var clientTimerData = new FoodPriceTimerData(adjustedNextUpdate, adjustedLastUpdate);
                clientTimerData.setDataVersion(dataVersion);
                
                com.pokecobble.phone.food.client.ClientFoodPriceTimer.getInstance()
                    .updateTimerData(clientTimerData);
                
                // Fire sync event for UI updates
                com.pokecobble.town.client.ClientSyncManager.getInstance()
                    .fireEvent("food_timer_updated", clientTimerData);
            });
            
            Pokecobbleclaim.LOGGER.debug("Received timer sync from server (next update in: {}ms)", 
                adjustedNextUpdate - clientTime);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling timer sync: " + e.getMessage());
        }
    }
    
    /**
     * Requests current timer data from the server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestTimerData() {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeLong(System.currentTimeMillis()); // Request timestamp
            
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(TIMER_REQUEST, buf);
            
            Pokecobbleclaim.LOGGER.debug("Requested timer data from server");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to request timer data: " + e.getMessage());
        }
    }
}
