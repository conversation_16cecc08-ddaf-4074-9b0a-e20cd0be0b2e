package com.pokecobble.phone.network;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.app.PlayerAppManager;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

/**
 * Handles network communication for phone app management.
 */
public class PhoneAppNetworkHandler {
    
    // Network identifiers
    public static final Identifier PHONE_APP_ADD = new Identifier("pokecobbleclaim", "phone_app_add");
    public static final Identifier PHONE_APP_REMOVE = new Identifier("pokecobbleclaim", "phone_app_remove");

    /**
     * Registers network handlers for phone app management.
     */
    public static void registerHandlers() {
        // Register client-side handlers
        // Note: Client-side handlers would be registered in a client initializer
        Pokecobbleclaim.LOGGER.info("Registered phone app network handlers");
    }

    /**
     * Sends a packet to add an app to a player's phone.
     */
    public static void sendAddAppToPlayer(ServerPlayerEntity player, String appId) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeString(appId);
            
            ServerPlayNetworking.send(player, PHONE_APP_ADD, buf);
            Pokecobbleclaim.LOGGER.debug("Sent add app packet for app '{}' to player {}", appId, player.getName().getString());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending add app packet to player {}: {}", player.getName().getString(), e.getMessage());
        }
    }

    /**
     * Sends a packet to remove an app from a player's phone.
     */
    public static void sendRemoveAppFromPlayer(ServerPlayerEntity player, String appId) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeString(appId);
            
            ServerPlayNetworking.send(player, PHONE_APP_REMOVE, buf);
            Pokecobbleclaim.LOGGER.debug("Sent remove app packet for app '{}' to player {}", appId, player.getName().getString());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending remove app packet to player {}: {}", player.getName().getString(), e.getMessage());
        }
    }
}
