package com.pokecobble.phone.network;

import com.google.gson.Gson;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.data.PlayerUpgradeData;
import com.pokecobble.phone.manager.PlayerUpgradeManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

/**
 * Handles network communication for player upgrade data synchronization.
 * Manages client-server sync of farming upgrades and unlocks.
 */
public class PlayerUpgradeNetworkHandler {
    private static final Gson GSON = new Gson();
    
    // Network identifiers
    public static final Identifier UPGRADE_SYNC_ID = new Identifier("pokecobbleclaim", "player_upgrade_sync");
    public static final Identifier UPGRADE_REQUEST_ID = new Identifier("pokecobbleclaim", "player_upgrade_request");
    
    /**
     * Registers server-side network handlers.
     */
    public static void registerServerHandlers() {
        // Handle upgrade data requests from clients
        ServerPlayNetworking.registerGlobalReceiver(UPGRADE_REQUEST_ID, (server, player, handler, buf, responseSender) -> {
            server.execute(() -> {
                handleUpgradeDataRequest(player);
            });
        });
        
        Pokecobbleclaim.LOGGER.info("Registered server-side player upgrade network handlers");
    }
    
    /**
     * Registers client-side network handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Handle upgrade data sync from server
        ClientPlayNetworking.registerGlobalReceiver(UPGRADE_SYNC_ID, (client, handler, buf, responseSender) -> {
            // Read upgrade data from server
            String upgradeDataJson = buf.readString();
            
            client.execute(() -> {
                handleUpgradeDataSync(upgradeDataJson);
            });
        });
        
        Pokecobbleclaim.LOGGER.info("Registered client-side player upgrade network handlers");
    }
    
    /**
     * Handles upgrade data requests from clients.
     */
    private static void handleUpgradeDataRequest(ServerPlayerEntity player) {
        try {
            PlayerUpgradeManager upgradeManager = PlayerUpgradeManager.getInstance();
            PlayerUpgradeData upgradeData = upgradeManager.getPlayerData(player);
            
            if (upgradeData != null) {
                sendUpgradeDataToPlayer(player, upgradeData);
                Pokecobbleclaim.LOGGER.debug("Sent upgrade data to player {}", player.getName().getString());
            } else {
                Pokecobbleclaim.LOGGER.warn("No upgrade data found for player {}", player.getName().getString());
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling upgrade data request from " + player.getName().getString(), e);
        }
    }
    
    /**
     * Handles upgrade data sync from server on client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleUpgradeDataSync(String upgradeDataJson) {
        try {
            PlayerUpgradeData upgradeData = GSON.fromJson(upgradeDataJson, PlayerUpgradeData.class);
            
            if (upgradeData != null) {
                // Update client-side upgrade manager
                com.pokecobble.phone.client.ClientPlayerUpgradeManager.getInstance().updateUpgradeData(upgradeData);
                
                Pokecobbleclaim.LOGGER.debug("Received upgrade data sync: {} upgrades, {} unlocks", 
                    upgradeData.getUpgradeLevels().size(), upgradeData.getUnlockedFeatures().size());
            } else {
                Pokecobbleclaim.LOGGER.warn("Received null upgrade data from server");
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling upgrade data sync from server", e);
        }
    }
    
    /**
     * Sends upgrade data to a specific player.
     */
    public static void sendUpgradeDataToPlayer(ServerPlayerEntity player, PlayerUpgradeData upgradeData) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Serialize upgrade data to JSON
            String upgradeDataJson = GSON.toJson(upgradeData);
            buf.writeString(upgradeDataJson);
            
            // Send to player
            ServerPlayNetworking.send(player, UPGRADE_SYNC_ID, buf);
            
            Pokecobbleclaim.LOGGER.debug("Sent upgrade data to player {}: {} upgrades, {} unlocks", 
                player.getName().getString(), upgradeData.getUpgradeLevels().size(), upgradeData.getUnlockedFeatures().size());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to send upgrade data to player " + player.getName().getString(), e);
        }
    }
    
    /**
     * Requests upgrade data from server (client-side).
     */
    @Environment(EnvType.CLIENT)
    public static void requestUpgradeData() {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            // No additional data needed for request
            
            ClientPlayNetworking.send(UPGRADE_REQUEST_ID, buf);
            
            Pokecobbleclaim.LOGGER.debug("Requested upgrade data from server");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to request upgrade data from server", e);
        }
    }
    
    /**
     * Syncs upgrade data to a player after a purchase or unlock.
     */
    public static void syncUpgradeDataAfterPurchase(ServerPlayerEntity player) {
        try {
            PlayerUpgradeManager upgradeManager = PlayerUpgradeManager.getInstance();
            PlayerUpgradeData upgradeData = upgradeManager.getPlayerData(player);
            
            if (upgradeData != null) {
                sendUpgradeDataToPlayer(player, upgradeData);
                
                // Also refresh any open farmer app screens
                refreshPlayerFarmerScreens(player);
                
                Pokecobbleclaim.LOGGER.debug("Synced upgrade data after purchase for player {}", player.getName().getString());
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing upgrade data after purchase for " + player.getName().getString(), e);
        }
    }
    
    /**
     * Refreshes farmer app screens for a player after upgrade changes.
     */
    private static void refreshPlayerFarmerScreens(ServerPlayerEntity player) {
        try {
            // Send a refresh signal to the client
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeString("refresh_farmer_screens");
            
            ServerPlayNetworking.send(player, new Identifier("pokecobbleclaim", "farmer_screen_refresh"), buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error refreshing farmer screens for " + player.getName().getString(), e);
        }
    }
    
    /**
     * Syncs upgrade data to all online players (for admin commands or server events).
     */
    public static void syncUpgradeDataToAllPlayers(net.minecraft.server.MinecraftServer server) {
        try {
            PlayerUpgradeManager upgradeManager = PlayerUpgradeManager.getInstance();
            int playerCount = 0;
            
            for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
                PlayerUpgradeData upgradeData = upgradeManager.getPlayerData(player);
                if (upgradeData != null) {
                    sendUpgradeDataToPlayer(player, upgradeData);
                    playerCount++;
                }
            }
            
            Pokecobbleclaim.LOGGER.info("Synced upgrade data to {} players", playerCount);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing upgrade data to all players", e);
        }
    }
    
    /**
     * Handles player join - automatically syncs their upgrade data.
     */
    public static void handlePlayerJoin(ServerPlayerEntity player) {
        try {
            // Small delay to ensure player is fully loaded
            player.getServer().execute(() -> {
                try {
                    Thread.sleep(500); // 0.5 second delay
                    
                    PlayerUpgradeManager upgradeManager = PlayerUpgradeManager.getInstance();
                    PlayerUpgradeData upgradeData = upgradeManager.getPlayerData(player);
                    
                    if (upgradeData != null) {
                        sendUpgradeDataToPlayer(player, upgradeData);
                        Pokecobbleclaim.LOGGER.info("Synced upgrade data to joining player {}: {} upgrades, {} unlocks", 
                            player.getName().getString(), upgradeData.getUpgradeLevels().size(), upgradeData.getUnlockedFeatures().size());
                    }
                    
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error syncing upgrade data to joining player " + player.getName().getString(), e);
                }
            });
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling player join for upgrade sync: " + player.getName().getString(), e);
        }
    }
}
