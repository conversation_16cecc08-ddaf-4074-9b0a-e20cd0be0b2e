package com.pokecobble.phone.network;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.data.ServerProductData;
import com.pokecobble.phone.manager.ServerProductManager;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;

/**
 * Handles product purchase transactions between client and server.
 * Integrates with the economy system for payment processing.
 */
public class ProductPurchasePacket {
    public static final Identifier PRODUCT_PURCHASE_ID = new Identifier("pokecobbleclaim", "product_purchase");
    public static final Identifier PURCHASE_RESULT_ID = new Identifier("pokecobbleclaim", "purchase_result");
    
    /**
     * Sends a product purchase request from client to server.
     * 
     * @param productId The ID of the product to purchase
     * @param quantity The quantity to purchase
     * @param totalPrice The total price for the purchase
     */
    public static void sendPurchaseRequest(String productId, int quantity, long totalPrice) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeString(productId);
        buf.writeInt(quantity);
        buf.writeLong(totalPrice);
        
        ClientPlayNetworking.send(PRODUCT_PURCHASE_ID, buf);
        Pokecobbleclaim.LOGGER.info("Sent purchase request for {} x{} (total: {} coins)", productId, quantity, totalPrice);
    }
    
    /**
     * Registers the server-side purchase handler.
     */
    public static void registerServerHandler() {
        ServerPlayNetworking.registerGlobalReceiver(PRODUCT_PURCHASE_ID, (server, player, handler, buf, responseSender) -> {
            String productId = buf.readString();
            int quantity = buf.readInt();
            long totalPrice = buf.readLong();
            
            server.execute(() -> {
                handleProductPurchase(player, productId, quantity, totalPrice);
            });
        });
    }
    
    /**
     * Registers the client-side result handler.
     */
    public static void registerClientHandler() {
        ClientPlayNetworking.registerGlobalReceiver(PURCHASE_RESULT_ID, (client, handler, buf, responseSender) -> {
            boolean success = buf.readBoolean();
            String message = buf.readString();
            String productName = buf.readString();
            int quantity = buf.readInt();
            
            client.execute(() -> {
                handlePurchaseResult(success, message, productName, quantity);
            });
        });
    }
    
    /**
     * Handles product purchase on the server side.
     */
    private static void handleProductPurchase(ServerPlayerEntity player, String productId, int quantity, long totalPrice) {
        try {
            // Get product data
            ServerProductManager productManager = ServerProductManager.getInstance();
            ServerProductData product = productManager.getProduct(productId);
            
            if (product == null) {
                sendPurchaseResult(player, false, "Product not found!", "", 0);
                return;
            }
            
            // Validate purchase
            if (quantity <= 0) {
                sendPurchaseResult(player, false, "Invalid quantity!", product.getName(), quantity);
                return;
            }
            
            // Calculate expected price
            long expectedPrice = (long) product.getDiscountedPrice() * quantity;
            if (totalPrice != expectedPrice) {
                sendPurchaseResult(player, false, "Price mismatch! Expected: " + expectedPrice, product.getName(), quantity);
                return;
            }
            
            // Check player level requirement
            // TODO: Implement player level checking system
            // For now, assume all players meet requirements
            
            // Process payment through economy system
            boolean paymentSuccess = processPayment(player, totalPrice, 
                "Purchased " + quantity + " " + product.getName());
            
            if (!paymentSuccess) {
                sendPurchaseResult(player, false, "Insufficient funds!", product.getName(), quantity);
                return;
            }
            
            // Give items to player
            boolean itemsGiven = giveItemsToPlayer(player, product, quantity);
            
            if (!itemsGiven) {
                // Refund the payment if items couldn't be given
                refundPayment(player, totalPrice, "Refund for failed purchase: " + product.getName());
                sendPurchaseResult(player, false, "Inventory full! Payment refunded.", product.getName(), quantity);
                return;
            }
            
            // Success!
            sendPurchaseResult(player, true, "Purchase successful!", product.getName(), quantity);
            
            Pokecobbleclaim.LOGGER.info("Player {} purchased {} x{} for {} coins", 
                player.getName().getString(), product.getName(), quantity, totalPrice);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error processing purchase for player " + player.getName().getString(), e);
            sendPurchaseResult(player, false, "Purchase failed due to server error!", "", 0);
        }
    }
    
    /**
     * Processes payment through the economy system.
     */
    private static boolean processPayment(ServerPlayerEntity player, long amount, String description) {
        try {
            // Use the economy system to subtract money from player
            com.pokecobble.economy.api.EconomyAPI economyAPI = com.pokecobble.economy.api.EconomyAPI.getInstance();

            if (!economyAPI.isAvailable()) {
                Pokecobbleclaim.LOGGER.warn("Economy system not available for purchase");
                return false;
            }

            // Check if player has enough money first
            if (!economyAPI.hasEnoughMoney(player.getUuid(), amount)) {
                return false;
            }

            // Subtract money from player's balance
            return economyAPI.subtractMoney(player.getUuid(), amount, description);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to process payment for player " + player.getName().getString(), e);
            return false;
        }
    }

    /**
     * Refunds payment to the player.
     */
    private static void refundPayment(ServerPlayerEntity player, long amount, String description) {
        try {
            com.pokecobble.economy.api.EconomyAPI economyAPI = com.pokecobble.economy.api.EconomyAPI.getInstance();

            if (economyAPI.isAvailable()) {
                economyAPI.addMoney(player.getUuid(), amount, description);
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to refund payment to player " + player.getName().getString(), e);
        }
    }
    
    /**
     * Gives purchased items to the player.
     */
    private static boolean giveItemsToPlayer(ServerPlayerEntity player, ServerProductData product, int quantity) {
        try {
            switch (product.getType()) {
                case SEED:
                case FOOD:
                    // Give ItemStack-based items
                    if (product.hasItemStack()) {
                        ItemStack itemStack = product.createItemStack();
                        if (!itemStack.isEmpty()) {
                            itemStack.setCount(quantity);
                            return player.getInventory().insertStack(itemStack);
                        }
                    }
                    break;
                    
                case TOOL:
                    // Give tools (for now, give as items - later could be special tool system)
                    ItemStack toolItem = createToolItem(product);
                    if (!toolItem.isEmpty()) {
                        for (int i = 0; i < quantity; i++) {
                            if (!player.getInventory().insertStack(toolItem.copy())) {
                                return false; // Inventory full
                            }
                        }
                        return true;
                    }
                    break;
                    
                case UPGRADE:
                    // Apply upgrades to player (no physical items)
                    return applyUpgradeToPlayer(player, product, quantity);

                case UNLOCK:
                    // Unlock features for player (no physical items)
                    return unlockFeatureForPlayer(player, product);
            }
            
            return false;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to give items to player " + player.getName().getString(), e);
            return false;
        }
    }
    
    /**
     * Creates a tool item for the player.
     */
    private static ItemStack createToolItem(ServerProductData product) {
        // For now, give basic tools - later this could be enhanced with custom tools
        switch (product.getId()) {
            case "iron_hoe":
                return new ItemStack(Items.IRON_HOE);
            case "diamond_hoe":
                return new ItemStack(Items.DIAMOND_HOE);
            case "watering_can":
                // Custom item - for now give a water bucket as placeholder
                return new ItemStack(Items.WATER_BUCKET);
            default:
                return ItemStack.EMPTY;
        }
    }
    
    /**
     * Applies an upgrade to the player.
     */
    private static boolean applyUpgradeToPlayer(ServerPlayerEntity player, ServerProductData product, int quantity) {
        try {
            com.pokecobble.phone.manager.PlayerUpgradeManager upgradeManager =
                com.pokecobble.phone.manager.PlayerUpgradeManager.getInstance();

            // Apply the upgrade (quantity times for stackable upgrades)
            boolean success = false;
            for (int i = 0; i < quantity; i++) {
                if (upgradeManager.purchaseUpgrade(player, product.getId())) {
                    success = true;
                } else {
                    // If we can't apply more levels, break
                    break;
                }
            }

            if (success) {
                // Get current upgrade level and effect
                int currentLevel = upgradeManager.getUpgradeLevel(player, product.getId());
                com.pokecobble.phone.data.PlayerUpgradeData upgradeData = upgradeManager.getPlayerData(player);
                int effect = upgradeData != null ? upgradeData.getUpgradeEffect(product.getId()) : 0;

                // Send notification to player
                player.sendMessage(Text.literal(String.format("§a✓ %s upgraded to level %d (%d%% effect)!",
                    product.getName(), currentLevel, effect)), false);

                // Sync upgrade data to client
                com.pokecobble.phone.network.PlayerUpgradeNetworkHandler.syncUpgradeDataAfterPurchase(player);

                Pokecobbleclaim.LOGGER.info("Applied upgrade {} x{} to player {} (level: {}, effect: {}%)",
                    product.getName(), quantity, player.getName().getString(), currentLevel, effect);

                return true;
            } else {
                player.sendMessage(Text.literal("§c✗ Upgrade is already at maximum level!"), false);
                return false;
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error applying upgrade " + product.getName() + " to player " + player.getName().getString(), e);
            player.sendMessage(Text.literal("§c✗ Failed to apply upgrade!"), false);
            return false;
        }
    }
    
    /**
     * Unlocks a feature for the player.
     */
    private static boolean unlockFeatureForPlayer(ServerPlayerEntity player, ServerProductData product) {
        try {
            com.pokecobble.phone.manager.PlayerUpgradeManager upgradeManager =
                com.pokecobble.phone.manager.PlayerUpgradeManager.getInstance();

            // Check if already unlocked
            if (upgradeManager.hasFeatureUnlocked(player, product.getId())) {
                player.sendMessage(Text.literal("§c✗ " + product.getName() + " is already unlocked!"), false);
                return false;
            }

            // Unlock the feature
            boolean success = upgradeManager.unlockFeature(player, product.getId());

            if (success) {
                // Send notification to player
                player.sendMessage(Text.literal("§a🔓 " + product.getName() + " unlocked!"), false);

                // Give the unlocked tool to the player
                giveUnlockedToolToPlayer(player, product);

                // Sync upgrade data to client
                com.pokecobble.phone.network.PlayerUpgradeNetworkHandler.syncUpgradeDataAfterPurchase(player);

                Pokecobbleclaim.LOGGER.info("Unlocked feature {} for player {}",
                    product.getName(), player.getName().getString());

                return true;
            } else {
                player.sendMessage(Text.literal("§c✗ Failed to unlock " + product.getName() + "!"), false);
                return false;
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error unlocking feature " + product.getName() + " for player " + player.getName().getString(), e);
            player.sendMessage(Text.literal("§c✗ Failed to unlock feature!"), false);
            return false;
        }
    }

    /**
     * Gives the unlocked tool item to the player.
     */
    private static void giveUnlockedToolToPlayer(ServerPlayerEntity player, ServerProductData product) {
        try {
            ItemStack toolItem = createUnlockedToolItem(product);
            if (!toolItem.isEmpty()) {
                boolean success = player.getInventory().insertStack(toolItem);
                if (success) {
                    player.sendMessage(Text.literal("§a+ Received " + toolItem.getName().getString()), true);
                } else {
                    // Drop the item if inventory is full
                    player.dropItem(toolItem, false);
                    player.sendMessage(Text.literal("§e⚠ Inventory full! Tool dropped on ground."), true);
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error giving unlocked tool to player " + player.getName().getString(), e);
        }
    }

    /**
     * Creates a tool item for unlocked features.
     */
    private static ItemStack createUnlockedToolItem(ServerProductData product) {
        try {
            // Create special tool items based on the unlock type
            switch (product.getId()) {
                case "unlock_sprinkler":
                    return createCustomTool("Farming Sprinkler", "Auto-waters crops in 3x3 area",
                        net.minecraft.item.Items.WATER_BUCKET, product.getId());

                case "unlock_harvester":
                    return createCustomTool("Farming Harvester", "Auto-harvests crops in 5x5 area",
                        net.minecraft.item.Items.IRON_HOE, product.getId());

                case "unlock_fertilizer":
                    return createCustomTool("Crop Fertilizer", "Doubles crop growth speed",
                        net.minecraft.item.Items.BONE_MEAL, product.getId());

                default:
                    // Fallback to a generic tool
                    return createCustomTool(product.getName(), product.getDescription(),
                        net.minecraft.item.Items.STICK, product.getId());
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error creating unlocked tool item for " + product.getId(), e);
            return ItemStack.EMPTY;
        }
    }

    /**
     * Creates a custom tool with NBT data.
     */
    private static ItemStack createCustomTool(String name, String description, net.minecraft.item.Item baseItem, String toolId) {
        ItemStack tool = new ItemStack(baseItem);

        // Set custom name
        tool.setCustomName(Text.literal("§6" + name));

        // Add lore/description
        net.minecraft.nbt.NbtCompound nbt = tool.getOrCreateNbt();
        net.minecraft.nbt.NbtList lore = new net.minecraft.nbt.NbtList();
        lore.add(net.minecraft.nbt.NbtString.of(Text.Serializer.toJson(Text.literal("§7" + description))));
        lore.add(net.minecraft.nbt.NbtString.of(Text.Serializer.toJson(Text.literal("§8Farming Tool"))));

        net.minecraft.nbt.NbtCompound display = new net.minecraft.nbt.NbtCompound();
        display.put("Lore", lore);
        nbt.put("display", display);

        // Add custom data to identify this as a farming tool
        nbt.putString("farming_tool_id", toolId);
        nbt.putBoolean("is_farming_tool", true);

        return tool;
    }

    /**
     * Sends purchase result to the client.
     */
    private static void sendPurchaseResult(ServerPlayerEntity player, boolean success, String message, 
                                         String productName, int quantity) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeBoolean(success);
            buf.writeString(message);
            buf.writeString(productName);
            buf.writeInt(quantity);
            
            ServerPlayNetworking.send(player, PURCHASE_RESULT_ID, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to send purchase result to player " + player.getName().getString(), e);
        }
    }
    
    /**
     * Handles purchase result on the client side.
     */
    private static void handlePurchaseResult(boolean success, String message, String productName, int quantity) {
        try {
            // Show result to player
            net.minecraft.client.MinecraftClient client = net.minecraft.client.MinecraftClient.getInstance();
            if (client.player != null) {
                String prefix = success ? "§a✓ " : "§c✗ ";
                String quantityText = quantity > 1 ? " x" + quantity : "";
                String fullMessage = prefix + message + 
                    (!productName.isEmpty() ? " (" + productName + quantityText + ")" : "");
                
                client.player.sendMessage(Text.literal(fullMessage), true);
            }
            
            Pokecobbleclaim.LOGGER.info("Purchase result: {} - {}", success ? "SUCCESS" : "FAILED", message);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to handle purchase result", e);
        }
    }
}
