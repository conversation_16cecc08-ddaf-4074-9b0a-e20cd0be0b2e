package com.pokecobble.phone.network;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.app.PlayerAppManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.PacketByteBuf;

/**
 * Client-side network handler for phone app management.
 */
@Environment(EnvType.CLIENT)
public class ClientPhoneAppNetworkHandler {

    /**
     * Registers client-side network handlers for phone app management.
     */
    public static void registerHandlers() {
        // Register handler for adding apps
        ClientPlayNetworking.registerGlobalReceiver(PhoneAppNetworkHandler.PHONE_APP_ADD, ClientPhoneAppNetworkHandler::handleAddApp);
        
        // Register handler for removing apps
        ClientPlayNetworking.registerGlobalReceiver(PhoneAppNetworkHandler.PHONE_APP_REMOVE, ClientPhoneAppNetworkHandler::handleRemoveApp);
        
        Pokecobbleclaim.LOGGER.info("Registered client-side phone app network handlers");
    }

    /**
     * Handles the add app packet from the server.
     */
    private static void handleAddApp(MinecraftClient client, ClientPlayNetworkHandler handler, PacketByteBuf buf, PacketSender sender) {
        try {
            String appId = buf.readString();

            // Execute on the main thread
            client.execute(() -> {
                if (client.player != null) {
                    PlayerAppManager.getInstance().addAppToPlayer(client.player.getUuid(), appId);

                    // Show visual notification on top of screen
                    String message = getAppUnlockedMessage(appId);
                    com.pokecobble.town.client.NotificationRenderer.addSuccessNotification(message);

                    Pokecobbleclaim.LOGGER.info("Added app '{}' to player's phone and showed notification", appId);
                }
            });

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling add app packet: {}", e.getMessage());
        }
    }

    /**
     * Gets the appropriate message for when an app is unlocked.
     */
    private static String getAppUnlockedMessage(String appId) {
        return switch (appId) {
            case "farmer" -> "The Farmer app is now available on your phone!";
            default -> "A new app is now available on your phone!";
        };
    }

    /**
     * Handles the remove app packet from the server.
     */
    private static void handleRemoveApp(MinecraftClient client, ClientPlayNetworkHandler handler, PacketByteBuf buf, PacketSender sender) {
        try {
            String appId = buf.readString();
            
            // Execute on the main thread
            client.execute(() -> {
                if (client.player != null) {
                    PlayerAppManager.getInstance().removeAppFromPlayer(client.player.getUuid(), appId);
                    Pokecobbleclaim.LOGGER.info("Removed app '{}' from player's phone", appId);
                }
            });
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling remove app packet: {}", e.getMessage());
        }
    }
}
