package com.pokecobble.phone.manager;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.data.PlayerUpgradeData;
import com.pokecobble.phone.storage.PlayerUpgradeStorage;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Server-side manager for player farming upgrades and unlocks.
 * Handles upgrade purchases, effect calculations, and data persistence.
 */
public class PlayerUpgradeManager {
    private static final PlayerUpgradeManager INSTANCE = new PlayerUpgradeManager();
    
    private final PlayerUpgradeStorage storage;
    private final Map<UUID, PlayerUpgradeData> activePlayerData = new ConcurrentHashMap<>();
    
    private PlayerUpgradeManager() {
        this.storage = new PlayerUpgradeStorage();
    }
    
    public static PlayerUpgradeManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * Gets player upgrade data, loading from storage if needed.
     */
    public PlayerUpgradeData getPlayerData(UUID playerId) {
        // Check active data first
        PlayerUpgradeData activeData = activePlayerData.get(playerId);
        if (activeData != null) {
            return activeData;
        }
        
        // Load from storage
        PlayerUpgradeData data = storage.loadPlayerData(playerId);
        if (data != null) {
            activePlayerData.put(playerId, data);
        }
        
        return data;
    }
    
    /**
     * Gets player upgrade data for a connected player.
     */
    public PlayerUpgradeData getPlayerData(ServerPlayerEntity player) {
        return getPlayerData(player.getUuid());
    }
    
    /**
     * Saves player upgrade data to storage.
     */
    public void savePlayerData(PlayerUpgradeData data) {
        if (data == null) return;
        
        UUID playerId = data.getPlayerId();
        
        // Update active data
        activePlayerData.put(playerId, data);
        
        // Save to storage
        storage.savePlayerData(data);
    }
    
    /**
     * Handles player joining - loads their upgrade data.
     */
    public void onPlayerJoin(ServerPlayerEntity player) {
        UUID playerId = player.getUuid();
        
        try {
            PlayerUpgradeData data = getPlayerData(playerId);
            if (data != null) {
                Pokecobbleclaim.LOGGER.debug("Loaded upgrade data for player {}: {} upgrades, {} unlocks", 
                    player.getName().getString(), data.getUpgradeLevels().size(), data.getUnlockedFeatures().size());
            } else {
                Pokecobbleclaim.LOGGER.warn("Failed to load upgrade data for player {}", player.getName().getString());
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error loading upgrade data for player " + player.getName().getString(), e);
        }
    }
    
    /**
     * Handles player leaving - saves their upgrade data.
     */
    public void onPlayerLeave(ServerPlayerEntity player) {
        UUID playerId = player.getUuid();
        
        try {
            PlayerUpgradeData data = activePlayerData.get(playerId);
            if (data != null) {
                storage.savePlayerData(data);
                Pokecobbleclaim.LOGGER.debug("Saved upgrade data for leaving player {}", player.getName().getString());
            }
            
            // Remove from active data to free memory
            activePlayerData.remove(playerId);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error saving upgrade data for leaving player " + player.getName().getString(), e);
        }
    }
    
    /**
     * Purchases an upgrade for a player.
     */
    public boolean purchaseUpgrade(ServerPlayerEntity player, String upgradeId) {
        try {
            PlayerUpgradeData data = getPlayerData(player);
            if (data == null) {
                Pokecobbleclaim.LOGGER.error("No upgrade data found for player {}", player.getName().getString());
                return false;
            }
            
            // Check if upgrade can be leveled up
            int currentLevel = data.getUpgradeLevel(upgradeId);
            int maxLevel = PlayerUpgradeData.getMaxUpgradeLevel(upgradeId);
            
            if (currentLevel >= maxLevel) {
                Pokecobbleclaim.LOGGER.info("Player {} tried to upgrade {} but it's already at max level", 
                    player.getName().getString(), upgradeId);
                return false;
            }
            
            // Apply the upgrade
            boolean success = data.upgradeLevel(upgradeId);
            if (success) {
                savePlayerData(data);
                
                int newLevel = data.getUpgradeLevel(upgradeId);
                int effect = data.getUpgradeEffect(upgradeId);
                
                Pokecobbleclaim.LOGGER.info("Player {} upgraded {} to level {} ({}% effect)", 
                    player.getName().getString(), upgradeId, newLevel, effect);
                
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error purchasing upgrade " + upgradeId + " for player " + player.getName().getString(), e);
            return false;
        }
    }
    
    /**
     * Unlocks a feature for a player.
     */
    public boolean unlockFeature(ServerPlayerEntity player, String featureId) {
        try {
            PlayerUpgradeData data = getPlayerData(player);
            if (data == null) {
                Pokecobbleclaim.LOGGER.error("No upgrade data found for player {}", player.getName().getString());
                return false;
            }
            
            // Check if already unlocked
            if (data.isFeatureUnlocked(featureId)) {
                Pokecobbleclaim.LOGGER.info("Player {} tried to unlock {} but it's already unlocked", 
                    player.getName().getString(), featureId);
                return false;
            }
            
            // Unlock the feature
            data.unlockFeature(featureId);
            savePlayerData(data);
            
            Pokecobbleclaim.LOGGER.info("Player {} unlocked feature {}", 
                player.getName().getString(), featureId);
            
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error unlocking feature " + featureId + " for player " + player.getName().getString(), e);
            return false;
        }
    }
    
    /**
     * Calculates discounted price for a player based on their upgrades.
     */
    public int calculateDiscountedPrice(ServerPlayerEntity player, int originalPrice, String discountType) {
        try {
            PlayerUpgradeData data = getPlayerData(player);
            if (data == null) {
                return originalPrice;
            }
            
            return data.calculateDiscountedPrice(originalPrice, discountType);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error calculating discounted price for player " + player.getName().getString(), e);
            return originalPrice;
        }
    }
    
    /**
     * Calculates bonus price for a player based on their upgrades.
     */
    public int calculateBonusPrice(ServerPlayerEntity player, int originalPrice, String bonusType) {
        try {
            PlayerUpgradeData data = getPlayerData(player);
            if (data == null) {
                return originalPrice;
            }
            
            return data.calculateBonusPrice(originalPrice, bonusType);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error calculating bonus price for player " + player.getName().getString(), e);
            return originalPrice;
        }
    }
    
    /**
     * Checks if a player has unlocked a specific feature.
     */
    public boolean hasFeatureUnlocked(ServerPlayerEntity player, String featureId) {
        try {
            PlayerUpgradeData data = getPlayerData(player);
            return data != null && data.isFeatureUnlocked(featureId);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error checking feature unlock for player " + player.getName().getString(), e);
            return false;
        }
    }
    
    /**
     * Gets the current level of an upgrade for a player.
     */
    public int getUpgradeLevel(ServerPlayerEntity player, String upgradeId) {
        try {
            PlayerUpgradeData data = getPlayerData(player);
            return data != null ? data.getUpgradeLevel(upgradeId) : 0;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error getting upgrade level for player " + player.getName().getString(), e);
            return 0;
        }
    }
    
    /**
     * Gets all unlocked tools for a player.
     */
    public Set<String> getUnlockedTools(ServerPlayerEntity player) {
        try {
            PlayerUpgradeData data = getPlayerData(player);
            if (data == null) {
                return new HashSet<>();
            }
            
            Set<String> unlockedTools = new HashSet<>();
            for (String feature : data.getUnlockedFeatures()) {
                if (feature.startsWith("unlock_")) {
                    unlockedTools.add(feature);
                }
            }
            
            return unlockedTools;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error getting unlocked tools for player " + player.getName().getString(), e);
            return new HashSet<>();
        }
    }
    
    /**
     * Saves all active player data to storage.
     */
    public void saveAllPlayerData() {
        try {
            for (PlayerUpgradeData data : activePlayerData.values()) {
                storage.savePlayerData(data);
            }
            Pokecobbleclaim.LOGGER.info("Saved upgrade data for {} active players", activePlayerData.size());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error saving all player upgrade data", e);
        }
    }
    
    /**
     * Clears cached data for a player.
     */
    public void clearPlayerCache(UUID playerId) {
        activePlayerData.remove(playerId);
        storage.clearCache(playerId);
    }
}
