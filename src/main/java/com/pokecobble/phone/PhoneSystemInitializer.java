package com.pokecobble.phone;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.client.ClientProductManager;
import com.pokecobble.phone.manager.ServerProductManager;
import com.pokecobble.phone.network.ProductNetworkManager;
import com.pokecobble.phone.network.ProductPurchasePacket;
import com.pokecobble.phone.network.ProductUpdatePacket;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.event.lifecycle.v1.ServerLifecycleEvents;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

/**
 * Initializes the phone system components including product management,
 * network handlers, and synchronization systems.
 */
public class PhoneSystemInitializer {
    private static boolean serverInitialized = false;
    private static boolean clientInitialized = false;
    
    /**
     * Initializes server-side phone systems.
     */
    public static void initializeServer() {
        if (serverInitialized) {
            return;
        }
        
        try {
            Pokecobbleclaim.LOGGER.info("Initializing server-side phone systems...");
            
            // Initialize server product manager
            ServerProductManager.getInstance();
            
            // Register network handlers
            ProductNetworkManager.registerServerHandlers();
            ProductPurchasePacket.registerServerHandler();
            com.pokecobble.phone.network.PlayerUpgradeNetworkHandler.registerServerHandlers();

            // Register server lifecycle events
            registerServerLifecycleEvents();
            
            serverInitialized = true;
            Pokecobbleclaim.LOGGER.info("Server-side phone systems initialized successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize server-side phone systems", e);
        }
    }
    
    /**
     * Initializes client-side phone systems.
     */
    @Environment(EnvType.CLIENT)
    public static void initializeClient() {
        if (clientInitialized) {
            return;
        }
        
        try {
            Pokecobbleclaim.LOGGER.info("Initializing client-side phone systems...");
            
            // Initialize client product manager (server data only)
            ClientProductManager.getInstance();

            // Initialize client upgrade manager
            com.pokecobble.phone.client.ClientPlayerUpgradeManager.getInstance();

            // Register network handlers
            ProductNetworkManager.registerClientHandlers();
            ProductPurchasePacket.registerClientHandler();
            com.pokecobble.phone.network.PlayerUpgradeNetworkHandler.registerClientHandlers();
            
            // Register client lifecycle events
            registerClientLifecycleEvents();
            
            clientInitialized = true;
            Pokecobbleclaim.LOGGER.info("Client-side phone systems initialized successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize client-side phone systems", e);
        }
    }
    
    /**
     * Registers server lifecycle events.
     */
    private static void registerServerLifecycleEvents() {
        // Server starting event
        ServerLifecycleEvents.SERVER_STARTING.register(server -> {
            Pokecobbleclaim.LOGGER.info("Phone system: Server starting, initializing product and upgrade data");

            // Initialize server product manager if not already done
            ServerProductManager.getInstance();

            // Initialize player upgrade manager
            com.pokecobble.phone.manager.PlayerUpgradeManager.getInstance();
        });
        
        // Server started event
        ServerLifecycleEvents.SERVER_STARTED.register(server -> {
            Pokecobbleclaim.LOGGER.info("Phone system: Server started, product system ready");
            
            // Optionally sync products to any connected players
            // (though typically no players are connected at server start)
        });
        
        // Server stopping event
        ServerLifecycleEvents.SERVER_STOPPING.register(server -> {
            Pokecobbleclaim.LOGGER.info("Phone system: Server stopping, saving product and upgrade data");

            try {
                // Save all player upgrade data
                com.pokecobble.phone.manager.PlayerUpgradeManager.getInstance().saveAllPlayerData();

                // Shutdown product manager
                ServerProductManager.getInstance().shutdown();
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error during phone system shutdown", e);
            }
        });
        
        // Player join event - sync products to new players
        net.fabricmc.fabric.api.networking.v1.ServerPlayConnectionEvents.JOIN.register((handler, sender, server) -> {
            ServerPlayerEntity player = handler.getPlayer();

            // Schedule sync with proper delay to ensure player is fully loaded
            server.execute(() -> {
                // Use a scheduled task instead of Thread.sleep to avoid blocking
                java.util.concurrent.ScheduledExecutorService scheduler =
                    java.util.concurrent.Executors.newSingleThreadScheduledExecutor();

                scheduler.schedule(() -> {
                    try {
                        // Double-check that player is still connected
                        if (player.isDisconnected()) {
                            Pokecobbleclaim.LOGGER.debug("Player {} disconnected before product sync",
                                player.getName().getString());
                            return;
                        }

                        java.util.Map<String, com.pokecobble.phone.data.ServerProductData> products =
                            ServerProductManager.getInstance().getAllProducts();

                        if (products.isEmpty()) {
                            Pokecobbleclaim.LOGGER.warn("No products available to sync to player {}",
                                player.getName().getString());
                            return;
                        }

                        ProductNetworkManager.sendFullProductSync(player, products);

                        // Also sync player upgrade data
                        com.pokecobble.phone.manager.PlayerUpgradeManager upgradeManager =
                            com.pokecobble.phone.manager.PlayerUpgradeManager.getInstance();
                        upgradeManager.onPlayerJoin(player);
                        com.pokecobble.phone.network.PlayerUpgradeNetworkHandler.handlePlayerJoin(player);

                        Pokecobbleclaim.LOGGER.info("Successfully synced {} products and upgrade data to player {}",
                            products.size(), player.getName().getString());

                    } catch (Exception e) {
                        Pokecobbleclaim.LOGGER.error("Failed to sync products to player {}: {}",
                            player.getName().getString(), e.getMessage());

                        // Retry once after a short delay
                        scheduler.schedule(() -> {
                            try {
                                if (!player.isDisconnected()) {
                                    java.util.Map<String, com.pokecobble.phone.data.ServerProductData> retryProducts =
                                        ServerProductManager.getInstance().getAllProducts();
                                    ProductNetworkManager.sendFullProductSync(player, retryProducts);
                                    Pokecobbleclaim.LOGGER.info("Retry sync successful for player {}",
                                        player.getName().getString());
                                }
                            } catch (Exception retryE) {
                                Pokecobbleclaim.LOGGER.error("Retry sync also failed for player {}: {}",
                                    player.getName().getString(), retryE.getMessage());
                            }
                        }, 2, java.util.concurrent.TimeUnit.SECONDS);
                    } finally {
                        scheduler.shutdown();
                    }
                }, 1500, java.util.concurrent.TimeUnit.MILLISECONDS); // 1.5 second delay
            });
        });
    }
    
    /**
     * Registers client lifecycle events.
     */
    @Environment(EnvType.CLIENT)
    private static void registerClientLifecycleEvents() {
        // Client connected to server event
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayConnectionEvents.JOIN.register((handler, sender, client) -> {
            Pokecobbleclaim.LOGGER.info("Phone system: Connected to server, requesting product sync");
            
            // Request product sync from server
            try {
                // Delay the request slightly to ensure connection is stable
                new Thread(() -> {
                    try {
                        Thread.sleep(2000); // 2 second delay
                        ProductNetworkManager.requestProductSync();

                        // Also request upgrade data
                        Thread.sleep(500); // Small additional delay
                        com.pokecobble.phone.network.PlayerUpgradeNetworkHandler.requestUpgradeData();
                    } catch (Exception e) {
                        Pokecobbleclaim.LOGGER.error("Failed to request product/upgrade sync", e);
                    }
                }, "ProductUpgradeSyncRequest").start();
                
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to request initial product sync", e);
            }
        });
        
        // Client disconnected from server event
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayConnectionEvents.DISCONNECT.register((handler, client) -> {
            Pokecobbleclaim.LOGGER.info("Phone system: Disconnected from server, clearing caches");

            // Clear client product cache
            ClientProductManager.getInstance().clearCache();

            // Clear client upgrade data
            com.pokecobble.phone.client.ClientPlayerUpgradeManager.getInstance().clearUpgradeData();
        });
    }
    
    /**
     * Performs a system health check.
     */
    public static boolean performHealthCheck() {
        try {
            // Check server systems
            if (serverInitialized) {
                ServerProductManager serverManager = ServerProductManager.getInstance();
                java.util.Map<String, com.pokecobble.phone.data.ServerProductData> products = serverManager.getAllProducts();
                
                if (products.isEmpty()) {
                    Pokecobbleclaim.LOGGER.warn("Phone system health check: No products found on server");
                    return false;
                }
                
                Pokecobbleclaim.LOGGER.info("Phone system health check: Server has {} products", products.size());
            }
            
            // Check client systems (if on client)
            try {
                ClientProductManager clientManager = ClientProductManager.getInstance();
                java.util.Map<String, com.pokecobble.phone.gui.FarmerAppScreen.Product> clientProducts = clientManager.getAllProducts();
                
                Pokecobbleclaim.LOGGER.info("Phone system health check: Client has {} products", clientProducts.size());
            } catch (Exception e) {
                // Client-side check failed, but this might be normal on server
                Pokecobbleclaim.LOGGER.debug("Client-side health check failed (normal on server): {}", e.getMessage());
            }
            
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Phone system health check failed", e);
            return false;
        }
    }
    
    /**
     * Forces a full product synchronization to all connected players.
     */
    public static void forceSyncToAllPlayers(MinecraftServer server) {
        if (!serverInitialized) {
            Pokecobbleclaim.LOGGER.warn("Cannot force sync: Server systems not initialized");
            return;
        }
        
        try {
            ProductNetworkManager.sendFullProductSyncToAll(server);
            Pokecobbleclaim.LOGGER.info("Forced product sync to all connected players");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to force sync to all players", e);
        }
    }
    
    /**
     * Gets initialization status.
     */
    public static String getInitializationStatus() {
        StringBuilder status = new StringBuilder();
        status.append("Phone System Status:\n");
        status.append("  Server Initialized: ").append(serverInitialized).append("\n");
        status.append("  Client Initialized: ").append(clientInitialized).append("\n");
        
        if (serverInitialized) {
            try {
                int productCount = ServerProductManager.getInstance().getAllProducts().size();
                status.append("  Server Products: ").append(productCount).append("\n");
            } catch (Exception e) {
                status.append("  Server Products: Error - ").append(e.getMessage()).append("\n");
            }
        }
        
        try {
            int clientProductCount = ClientProductManager.getInstance().getAllProducts().size();
            status.append("  Client Products: ").append(clientProductCount).append("\n");
        } catch (Exception e) {
            status.append("  Client Products: Error - ").append(e.getMessage()).append("\n");
        }
        
        return status.toString();
    }
    
    /**
     * Resets the initialization state (for testing purposes).
     */
    public static void resetInitialization() {
        serverInitialized = false;
        clientInitialized = false;
        Pokecobbleclaim.LOGGER.info("Phone system initialization state reset");
    }
}
