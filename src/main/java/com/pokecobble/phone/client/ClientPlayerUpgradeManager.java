package com.pokecobble.phone.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.data.PlayerUpgradeData;
import com.pokecobble.phone.network.PlayerUpgradeNetworkHandler;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;

import java.util.HashSet;
import java.util.Set;

/**
 * Client-side manager for player farming upgrades and unlocks.
 * Handles local upgrade data and provides interface for UI components.
 */
@Environment(EnvType.CLIENT)
public class ClientPlayerUpgradeManager {
    private static final ClientPlayerUpgradeManager INSTANCE = new ClientPlayerUpgradeManager();
    
    private PlayerUpgradeData currentUpgradeData;
    private long lastSyncTime = 0;
    private static final long SYNC_COOLDOWN = 5000; // 5 seconds between sync requests
    
    private ClientPlayerUpgradeManager() {
        // Private constructor for singleton
    }
    
    public static ClientPlayerUpgradeManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * Updates the current upgrade data from server sync.
     */
    public void updateUpgradeData(PlayerUpgradeData upgradeData) {
        this.currentUpgradeData = upgradeData;
        this.lastSyncTime = System.currentTimeMillis();
        
        // Notify any open farmer app screens about the update
        notifyFarmerScreensOfUpdate();
        
        Pokecobbleclaim.LOGGER.debug("Updated client upgrade data: {} upgrades, {} unlocks", 
            upgradeData.getUpgradeLevels().size(), upgradeData.getUnlockedFeatures().size());
    }
    
    /**
     * Gets the current upgrade level for a specific upgrade.
     */
    public int getUpgradeLevel(String upgradeId) {
        if (currentUpgradeData == null) {
            requestUpgradeDataIfNeeded();
            return 0;
        }
        
        return currentUpgradeData.getUpgradeLevel(upgradeId);
    }
    
    /**
     * Checks if a feature is unlocked.
     */
    public boolean isFeatureUnlocked(String featureId) {
        if (currentUpgradeData == null) {
            requestUpgradeDataIfNeeded();
            return false;
        }
        
        return currentUpgradeData.isFeatureUnlocked(featureId);
    }
    
    /**
     * Gets the effect percentage for an upgrade at its current level.
     */
    public int getUpgradeEffect(String upgradeId) {
        if (currentUpgradeData == null) {
            requestUpgradeDataIfNeeded();
            return 0;
        }
        
        return currentUpgradeData.getUpgradeEffect(upgradeId);
    }
    
    /**
     * Gets all unlocked tools.
     */
    public Set<String> getUnlockedTools() {
        if (currentUpgradeData == null) {
            requestUpgradeDataIfNeeded();
            return new HashSet<>();
        }
        
        Set<String> unlockedTools = new HashSet<>();
        for (String feature : currentUpgradeData.getUnlockedFeatures()) {
            if (feature.startsWith("unlock_")) {
                unlockedTools.add(feature);
            }
        }
        
        return unlockedTools;
    }
    
    /**
     * Calculates discounted price based on current upgrades.
     */
    public int calculateDiscountedPrice(int originalPrice, String discountType) {
        if (currentUpgradeData == null) {
            requestUpgradeDataIfNeeded();
            return originalPrice;
        }
        
        return currentUpgradeData.calculateDiscountedPrice(originalPrice, discountType);
    }
    
    /**
     * Calculates bonus price based on current upgrades.
     */
    public int calculateBonusPrice(int originalPrice, String bonusType) {
        if (currentUpgradeData == null) {
            requestUpgradeDataIfNeeded();
            return originalPrice;
        }
        
        return currentUpgradeData.calculateBonusPrice(originalPrice, bonusType);
    }
    
    /**
     * Gets the maximum level for an upgrade.
     */
    public int getMaxUpgradeLevel(String upgradeId) {
        return PlayerUpgradeData.getMaxUpgradeLevel(upgradeId);
    }
    
    /**
     * Gets the effect for a specific upgrade level.
     */
    public int getUpgradeEffectAtLevel(String upgradeId, int level) {
        return PlayerUpgradeData.getUpgradeEffectAtLevel(upgradeId, level);
    }
    
    /**
     * Checks if an upgrade can be leveled up.
     */
    public boolean canUpgrade(String upgradeId) {
        int currentLevel = getUpgradeLevel(upgradeId);
        int maxLevel = getMaxUpgradeLevel(upgradeId);
        return currentLevel < maxLevel;
    }
    
    /**
     * Checks if a tool is already unlocked.
     */
    public boolean isToolUnlocked(String toolId) {
        return isFeatureUnlocked(toolId);
    }
    
    /**
     * Gets the next upgrade effect for display purposes.
     */
    public String getNextUpgradeEffect(String upgradeId) {
        int currentLevel = getUpgradeLevel(upgradeId);
        int maxLevel = getMaxUpgradeLevel(upgradeId);
        
        if (currentLevel >= maxLevel) {
            return "MAX LEVEL";
        }
        
        int nextLevel = currentLevel + 1;
        int nextEffect = getUpgradeEffectAtLevel(upgradeId, nextLevel);
        
        switch (upgradeId) {
            case "seed_discount":
                return "Next: -" + nextEffect + "% seed costs";
            case "sell_bonus":
                return "Next: +" + nextEffect + "% sell prices";
            case "tool_discount":
                return "Next: -" + nextEffect + "% tool costs";
            default:
                return "Next: +" + nextEffect + "% effect";
        }
    }
    
    /**
     * Gets the current upgrade effect description.
     */
    public String getCurrentUpgradeEffect(String upgradeId) {
        int currentLevel = getUpgradeLevel(upgradeId);
        
        if (currentLevel <= 0) {
            return "Not purchased";
        }
        
        int currentEffect = getUpgradeEffect(upgradeId);
        
        switch (upgradeId) {
            case "seed_discount":
                return "Current: -" + currentEffect + "% seed costs";
            case "sell_bonus":
                return "Current: +" + currentEffect + "% sell prices";
            case "tool_discount":
                return "Current: -" + currentEffect + "% tool costs";
            default:
                return "Current: +" + currentEffect + "% effect";
        }
    }
    
    /**
     * Requests upgrade data from server if needed.
     */
    public void requestUpgradeDataIfNeeded() {
        long currentTime = System.currentTimeMillis();
        
        if (currentUpgradeData == null || (currentTime - lastSyncTime) > SYNC_COOLDOWN) {
            PlayerUpgradeNetworkHandler.requestUpgradeData();
            lastSyncTime = currentTime;
        }
    }
    
    /**
     * Forces a refresh of upgrade data from server.
     */
    public void forceRefreshUpgradeData() {
        PlayerUpgradeNetworkHandler.requestUpgradeData();
        lastSyncTime = System.currentTimeMillis();
    }
    
    /**
     * Clears current upgrade data (called on disconnect).
     */
    public void clearUpgradeData() {
        this.currentUpgradeData = null;
        this.lastSyncTime = 0;
        
        Pokecobbleclaim.LOGGER.debug("Cleared client upgrade data");
    }
    
    /**
     * Checks if upgrade data is available.
     */
    public boolean hasUpgradeData() {
        return currentUpgradeData != null;
    }
    
    /**
     * Gets the current upgrade data (for debugging).
     */
    public PlayerUpgradeData getCurrentUpgradeData() {
        return currentUpgradeData;
    }
    
    /**
     * Notifies farmer app screens about upgrade data updates.
     */
    private void notifyFarmerScreensOfUpdate() {
        try {
            // Get the current screen
            net.minecraft.client.MinecraftClient client = net.minecraft.client.MinecraftClient.getInstance();
            if (client == null || client.currentScreen == null) {
                return;
            }
            
            // Check if it's a FarmerAppScreen and refresh it
            if (client.currentScreen instanceof com.pokecobble.phone.gui.FarmerAppScreen) {
                com.pokecobble.phone.gui.FarmerAppScreen farmerScreen = 
                    (com.pokecobble.phone.gui.FarmerAppScreen) client.currentScreen;
                
                // Refresh the screen to show updated upgrade data
                farmerScreen.refreshUpgradeData();
                
                Pokecobbleclaim.LOGGER.debug("Notified FarmerAppScreen of upgrade data update");
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to notify farmer screens of upgrade update", e);
        }
    }
}
