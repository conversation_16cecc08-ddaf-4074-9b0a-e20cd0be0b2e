package com.pokecobble.phone.storage;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.data.PlayerUpgradeData;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Handles persistent storage of player upgrade data.
 * Similar to PlayerDataStorage but specifically for farming upgrades and unlocks.
 */
public class PlayerUpgradeStorage {
    private static final Gson GSON = new GsonBuilder()
            .setPrettyPrinting()
            .create();
    
    private final Path storageDirectory;
    private final Path backupDirectory;
    private final ReadWriteLock storageLock = new ReentrantReadWriteLock();
    
    // Cache for frequently accessed data
    private final Map<UUID, PlayerUpgradeData> cache = new ConcurrentHashMap<>();
    private static final long CACHE_EXPIRY_TIME = 300000; // 5 minutes
    private final Map<UUID, Long> cacheTimestamps = new ConcurrentHashMap<>();
    
    public PlayerUpgradeStorage() {
        // Initialize storage directories
        Path configDir = Paths.get("config", "pokecobbleclaim", "phone");
        this.storageDirectory = configDir.resolve("upgrades");
        this.backupDirectory = configDir.resolve("upgrades_backup");
        
        try {
            Files.createDirectories(storageDirectory);
            Files.createDirectories(backupDirectory);
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to create upgrade storage directories", e);
        }
    }
    
    /**
     * Saves player upgrade data to disk.
     */
    public void savePlayerData(PlayerUpgradeData upgradeData) {
        if (upgradeData == null) {
            return;
        }
        
        UUID playerId = upgradeData.getPlayerId();
        storageLock.writeLock().lock();
        
        try {
            Path playerFile = storageDirectory.resolve(playerId.toString() + ".json");
            Path tempFile = storageDirectory.resolve(playerId.toString() + ".tmp");
            Path backupFile = backupDirectory.resolve(playerId.toString() + ".json");
            
            // Create backup of existing file
            if (Files.exists(playerFile)) {
                try {
                    Files.copy(playerFile, backupFile, StandardCopyOption.REPLACE_EXISTING);
                } catch (IOException e) {
                    Pokecobbleclaim.LOGGER.warn("Failed to create backup for player {}: {}", playerId, e.getMessage());
                }
            }
            
            // Write to temporary file first
            try (FileWriter writer = new FileWriter(tempFile.toFile())) {
                GSON.toJson(upgradeData, writer);
            }
            
            // Atomic move to final location
            Files.move(tempFile, playerFile, StandardCopyOption.REPLACE_EXISTING);
            
            // Update cache
            cache.put(playerId, upgradeData.copy());
            cacheTimestamps.put(playerId, System.currentTimeMillis());
            
            Pokecobbleclaim.LOGGER.debug("Saved upgrade data for player {}", playerId);
            
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to save upgrade data for player " + playerId, e);
        } finally {
            storageLock.writeLock().unlock();
        }
    }
    
    /**
     * Loads player upgrade data from disk.
     */
    public PlayerUpgradeData loadPlayerData(UUID playerId) {
        // Check cache first
        PlayerUpgradeData cachedData = getCachedData(playerId);
        if (cachedData != null) {
            return cachedData;
        }
        
        storageLock.readLock().lock();
        
        try {
            Path playerFile = storageDirectory.resolve(playerId.toString() + ".json");
            
            if (!Files.exists(playerFile)) {
                // Create new player data
                PlayerUpgradeData newData = new PlayerUpgradeData(playerId);
                
                // Cache the new data
                cache.put(playerId, newData.copy());
                cacheTimestamps.put(playerId, System.currentTimeMillis());
                
                return newData;
            }
            
            // Load from file
            try (FileReader reader = new FileReader(playerFile.toFile())) {
                PlayerUpgradeData data = GSON.fromJson(reader, PlayerUpgradeData.class);
                
                if (data == null) {
                    Pokecobbleclaim.LOGGER.warn("Loaded null upgrade data for player {}, creating new", playerId);
                    data = new PlayerUpgradeData(playerId);
                }
                
                // Update cache
                cache.put(playerId, data.copy());
                cacheTimestamps.put(playerId, System.currentTimeMillis());
                
                return data;
                
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to parse upgrade data for player " + playerId + ", attempting backup restore", e);
                return loadFromBackup(playerId);
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load upgrade data for player " + playerId, e);
            return new PlayerUpgradeData(playerId); // Return empty data as fallback
        } finally {
            storageLock.readLock().unlock();
        }
    }
    
    /**
     * Attempts to load player data from backup.
     */
    private PlayerUpgradeData loadFromBackup(UUID playerId) {
        try {
            Path backupFile = backupDirectory.resolve(playerId.toString() + ".json");
            
            if (!Files.exists(backupFile)) {
                Pokecobbleclaim.LOGGER.warn("No backup found for player {}, creating new data", playerId);
                return new PlayerUpgradeData(playerId);
            }
            
            try (FileReader reader = new FileReader(backupFile.toFile())) {
                PlayerUpgradeData data = GSON.fromJson(reader, PlayerUpgradeData.class);
                
                if (data != null) {
                    Pokecobbleclaim.LOGGER.info("Successfully restored upgrade data from backup for player {}", playerId);
                    
                    // Save the restored data to main file
                    savePlayerData(data);
                    
                    return data;
                } else {
                    Pokecobbleclaim.LOGGER.warn("Backup data was null for player {}, creating new", playerId);
                    return new PlayerUpgradeData(playerId);
                }
                
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to restore from backup for player " + playerId, e);
                return new PlayerUpgradeData(playerId);
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during backup restore for player " + playerId, e);
            return new PlayerUpgradeData(playerId);
        }
    }
    
    /**
     * Gets cached data if it's still valid.
     */
    private PlayerUpgradeData getCachedData(UUID playerId) {
        PlayerUpgradeData cachedData = cache.get(playerId);
        Long cacheTime = cacheTimestamps.get(playerId);
        
        if (cachedData != null && cacheTime != null) {
            long age = System.currentTimeMillis() - cacheTime;
            if (age < CACHE_EXPIRY_TIME) {
                return cachedData.copy(); // Return a copy to prevent external modification
            } else {
                // Cache expired, remove it
                cache.remove(playerId);
                cacheTimestamps.remove(playerId);
            }
        }
        
        return null;
    }
    
    /**
     * Clears cached data for a player.
     */
    public void clearCache(UUID playerId) {
        cache.remove(playerId);
        cacheTimestamps.remove(playerId);
    }
    
    /**
     * Clears all cached data.
     */
    public void clearAllCache() {
        cache.clear();
        cacheTimestamps.clear();
    }
    
    /**
     * Checks if player data exists on disk.
     */
    public boolean playerDataExists(UUID playerId) {
        Path playerFile = storageDirectory.resolve(playerId.toString() + ".json");
        return Files.exists(playerFile);
    }
    
    /**
     * Deletes player upgrade data.
     */
    public boolean deletePlayerData(UUID playerId) {
        storageLock.writeLock().lock();
        
        try {
            Path playerFile = storageDirectory.resolve(playerId.toString() + ".json");
            
            if (Files.exists(playerFile)) {
                // Create backup before deletion
                Path backupFile = backupDirectory.resolve(playerId.toString() + "_deleted.json");
                Files.copy(playerFile, backupFile, StandardCopyOption.REPLACE_EXISTING);
                
                // Delete the file
                Files.delete(playerFile);
                
                // Clear from cache
                cache.remove(playerId);
                cacheTimestamps.remove(playerId);
                
                Pokecobbleclaim.LOGGER.info("Deleted upgrade data for player {}", playerId);
                return true;
            }
            
            return false;
            
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to delete upgrade data for player " + playerId, e);
            return false;
        } finally {
            storageLock.writeLock().unlock();
        }
    }
    
    /**
     * Gets the number of cached players.
     */
    public int getCacheSize() {
        return cache.size();
    }
}
