package com.pokecobble.phone;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerEntity;

/**
 * Client-side manager for the farmer app edit mode.
 * Edit mode allows operators to modify product details in the farmer app.
 * This includes server-side permission validation for security.
 */
public class FarmerEditModeManager {
    private static final FarmerEditModeManager INSTANCE = new FarmerEditModeManager();

    // Client-side edit mode state
    private boolean editModeEnabled = false;

    private FarmerEditModeManager() {
        // Private constructor for singleton
    }

    /**
     * Gets the singleton instance of the FarmerEditModeManager.
     *
     * @return The FarmerEditModeManager instance
     */
    public static FarmerEditModeManager getInstance() {
        return INSTANCE;
    }

    /**
     * Toggles edit mode for the current client with permission validation.
     *
     * @return True if edit mode is now enabled, false if it's now disabled or permission denied
     */
    public boolean toggleEditMode() {
        // Check if player has permission to use edit mode
        if (!canUseEditMode()) {
            Pokecobbleclaim.LOGGER.warn("Player attempted to toggle farmer edit mode without permission");
            return false;
        }

        editModeEnabled = !editModeEnabled;
        Pokecobbleclaim.LOGGER.info("Farmer edit mode " + (editModeEnabled ? "enabled" : "disabled"));
        return editModeEnabled;
    }

    /**
     * Checks if edit mode is enabled for the current client.
     *
     * @return True if edit mode is enabled
     */
    public boolean isEditModeEnabled() {
        // Double-check permissions when checking edit mode status
        if (editModeEnabled && !canUseEditMode()) {
            // Disable edit mode if player no longer has permission
            editModeEnabled = false;
            Pokecobbleclaim.LOGGER.info("Farmer edit mode disabled - player lost permission");
        }
        return editModeEnabled;
    }

    /**
     * Sets edit mode state for the current client with permission validation.
     *
     * @param enabled Whether edit mode should be enabled
     */
    public void setEditModeEnabled(boolean enabled) {
        // Check permissions before enabling
        if (enabled && !canUseEditMode()) {
            Pokecobbleclaim.LOGGER.warn("Attempted to enable farmer edit mode without permission");
            return;
        }

        this.editModeEnabled = enabled;
        Pokecobbleclaim.LOGGER.info("Farmer edit mode " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * Checks if the current player has permission to use edit mode.
     * Only server operators (permission level 2+) can use edit mode.
     *
     * @return True if the player can use edit mode
     */
    private boolean canUseEditMode() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client == null || client.player == null) {
            return false;
        }

        ClientPlayerEntity player = client.player;

        // Check if player has operator permissions
        // In single player, the player is always an operator
        if (client.isInSingleplayer()) {
            return true;
        }

        // For multiplayer, check if the player has permission level 2+ (operator)
        // This is a client-side check, but the server should also validate
        return player.hasPermissionLevel(2);
    }

    /**
     * Forces edit mode to be disabled (used when player loses permissions).
     */
    public void forceDisableEditMode() {
        if (editModeEnabled) {
            editModeEnabled = false;
            Pokecobbleclaim.LOGGER.info("Farmer edit mode force disabled");
        }
    }
}
