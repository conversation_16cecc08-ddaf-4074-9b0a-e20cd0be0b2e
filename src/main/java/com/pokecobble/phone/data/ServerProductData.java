package com.pokecobble.phone.data;

import com.google.gson.annotations.SerializedName;
import com.pokecobble.phone.gui.FarmerAppScreen;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;

import java.util.HashMap;
import java.util.Map;

/**
 * Server-side product data with category-specific properties.
 * This class represents the authoritative product data stored on the server.
 */
public class ServerProductData {
    @SerializedName("id")
    private String id;
    
    @SerializedName("name")
    private String name;
    
    @SerializedName("description")
    private String description;
    
    @SerializedName("price")
    private int price;
    
    @SerializedName("type")
    private FarmerAppScreen.ProductType type;
    
    @SerializedName("required_level")
    private int requiredLevel;
    
    @SerializedName("icon_path")
    private String iconPath;
    
    @SerializedName("item_id")
    private String itemId; // For ItemStack-based products
    
    @SerializedName("on_sale")
    private boolean onSale = false;
    
    @SerializedName("discount_percent")
    private int discountPercent = 0;
    
    // Category-specific properties
    @SerializedName("category_data")
    private Map<String, Object> categoryData = new HashMap<>();
    
    // Metadata
    @SerializedName("last_modified")
    private long lastModified;
    
    @SerializedName("modified_by")
    private String modifiedBy;
    
    @SerializedName("data_version")
    private long dataVersion;
    
    public ServerProductData() {
        this.lastModified = System.currentTimeMillis();
        this.dataVersion = System.currentTimeMillis();
    }
    
    public ServerProductData(String id, String name, String description, int price, 
                           FarmerAppScreen.ProductType type, int requiredLevel) {
        this();
        this.id = id;
        this.name = name;
        this.description = description;
        this.price = price;
        this.type = type;
        this.requiredLevel = requiredLevel;
    }
    
    // Getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; updateVersion(); }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; updateVersion(); }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; updateVersion(); }
    
    public int getPrice() { return price; }
    public void setPrice(int price) { this.price = price; updateVersion(); }
    
    public FarmerAppScreen.ProductType getType() { return type; }
    public void setType(FarmerAppScreen.ProductType type) { this.type = type; updateVersion(); }
    
    public int getRequiredLevel() { return requiredLevel; }
    public void setRequiredLevel(int requiredLevel) { this.requiredLevel = requiredLevel; updateVersion(); }
    
    public String getIconPath() { return iconPath; }
    public void setIconPath(String iconPath) { this.iconPath = iconPath; updateVersion(); }
    
    public String getItemId() { return itemId; }
    public void setItemId(String itemId) { this.itemId = itemId; updateVersion(); }
    
    public boolean isOnSale() { return onSale; }
    public void setOnSale(boolean onSale) { this.onSale = onSale; updateVersion(); }
    
    public int getDiscountPercent() { return discountPercent; }
    public void setDiscountPercent(int discountPercent) { this.discountPercent = discountPercent; updateVersion(); }
    
    public Map<String, Object> getCategoryData() { return categoryData; }
    public void setCategoryData(Map<String, Object> categoryData) { this.categoryData = categoryData; updateVersion(); }
    
    public long getLastModified() { return lastModified; }
    public String getModifiedBy() { return modifiedBy; }
    public void setModifiedBy(String modifiedBy) { this.modifiedBy = modifiedBy; updateVersion(); }
    
    public long getDataVersion() { return dataVersion; }
    
    private void updateVersion() {
        this.lastModified = System.currentTimeMillis();
        this.dataVersion = System.currentTimeMillis();
    }
    
    // Category-specific data helpers
    public void setCategoryProperty(String key, Object value) {
        categoryData.put(key, value);
        updateVersion();
    }
    
    public Object getCategoryProperty(String key) {
        return categoryData.get(key);
    }
    
    public Integer getCategoryPropertyAsInt(String key, int defaultValue) {
        Object value = categoryData.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }
    
    public String getCategoryPropertyAsString(String key, String defaultValue) {
        Object value = categoryData.get(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    // Convenience methods for category-specific properties
    public int getGrowthTime() {
        return getCategoryPropertyAsInt("growth_time", 120);
    }
    
    public void setGrowthTime(int growthTime) {
        setCategoryProperty("growth_time", growthTime);
    }
    
    public int getNutritionValue() {
        return getCategoryPropertyAsInt("nutrition_value", 5);
    }
    
    public void setNutritionValue(int nutritionValue) {
        setCategoryProperty("nutrition_value", nutritionValue);
    }
    
    public int getDurability() {
        return getCategoryPropertyAsInt("durability", 100);
    }
    
    public void setDurability(int durability) {
        setCategoryProperty("durability", durability);
    }
    
    public int getUpgradeDiscountPercent() {
        return getCategoryPropertyAsInt("upgrade_discount_percent", 15);
    }
    
    public void setUpgradeDiscountPercent(int discountPercent) {
        setCategoryProperty("upgrade_discount_percent", discountPercent);
    }
    
    public int getUnlockCost() {
        return getCategoryPropertyAsInt("unlock_cost", price);
    }
    
    public void setUnlockCost(int unlockCost) {
        setCategoryProperty("unlock_cost", unlockCost);
    }
    
    // Utility methods
    public int getDiscountedPrice() {
        if (onSale) {
            return price - (price * discountPercent / 100);
        }
        return price;
    }
    
    public boolean hasItemStack() {
        return itemId != null && !itemId.isEmpty();
    }
    
    public ItemStack createItemStack() {
        if (itemId == null || itemId.isEmpty()) {
            return ItemStack.EMPTY;
        }

        // Check if there's a selected item ID in category data (for unlock products)
        Object selectedItemId = getCategoryProperty("selected_item_id");
        if (selectedItemId != null && !selectedItemId.toString().isEmpty()) {
            return createItemStackFromId(selectedItemId.toString());
        }

        // Convert item ID to ItemStack using predefined mappings
        switch (itemId) {
            case "wheat_seeds": return new ItemStack(Items.WHEAT_SEEDS);
            case "carrot": return new ItemStack(Items.CARROT);
            case "potato": return new ItemStack(Items.POTATO);
            case "beetroot_seeds": return new ItemStack(Items.BEETROOT_SEEDS);
            case "wheat": return new ItemStack(Items.WHEAT);
            case "beetroot": return new ItemStack(Items.BEETROOT);
            default: return createItemStackFromId(itemId);
        }
    }

    /**
     * Creates an ItemStack from a full item ID (e.g., "minecraft:diamond_sword").
     */
    private ItemStack createItemStackFromId(String fullItemId) {
        try {
            net.minecraft.util.Identifier identifier = new net.minecraft.util.Identifier(fullItemId);
            net.minecraft.item.Item item = net.minecraft.registry.Registries.ITEM.get(identifier);

            if (item != null && item != Items.AIR) {
                return new ItemStack(item);
            }
        } catch (Exception e) {
            // Invalid item ID, return empty stack
        }

        return ItemStack.EMPTY;
    }
    
    /**
     * Converts this server product data to a client-side Product object.
     */
    public FarmerAppScreen.Product toClientProduct() {
        if (hasItemStack()) {
            return new FarmerAppScreen.Product(id, name, description, createItemStack(), price, type, requiredLevel);
        } else {
            return new FarmerAppScreen.Product(id, name, description, iconPath, price, type, requiredLevel);
        }
    }
    
    /**
     * Creates a copy of this product data.
     */
    public ServerProductData copy() {
        ServerProductData copy = new ServerProductData(id, name, description, price, type, requiredLevel);
        copy.iconPath = this.iconPath;
        copy.itemId = this.itemId;
        copy.onSale = this.onSale;
        copy.discountPercent = this.discountPercent;
        copy.categoryData = new HashMap<>(this.categoryData);
        copy.lastModified = this.lastModified;
        copy.modifiedBy = this.modifiedBy;
        copy.dataVersion = this.dataVersion;
        return copy;
    }
    
    @Override
    public String toString() {
        return "ServerProductData{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", type=" + type +
                ", price=" + price +
                ", dataVersion=" + dataVersion +
                '}';
    }
}
