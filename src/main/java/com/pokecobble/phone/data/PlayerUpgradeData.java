package com.pokecobble.phone.data;

import com.google.gson.annotations.SerializedName;
import com.pokecobble.Pokecobbleclaim;

import java.util.*;

/**
 * Represents a player's farming upgrades and unlocked features.
 * This data is stored per-player on the server and synchronized to the client.
 */
public class PlayerUpgradeData {
    @SerializedName("player_id")
    private String playerId;
    
    @SerializedName("upgrade_levels")
    private Map<String, Integer> upgradeLevels;
    
    @SerializedName("unlocked_features")
    private Set<String> unlockedFeatures;
    
    @SerializedName("last_modified")
    private long lastModified;
    
    @SerializedName("data_version")
    private int dataVersion;
    
    // Maximum levels for each upgrade type
    private static final Map<String, Integer> MAX_UPGRADE_LEVELS = new HashMap<>();
    static {
        MAX_UPGRADE_LEVELS.put("seed_discount", 5);
        MAX_UPGRADE_LEVELS.put("sell_bonus", 5);
        MAX_UPGRADE_LEVELS.put("tool_discount", 5);
    }
    
    // Upgrade effects per level (percentage)
    private static final Map<String, List<Integer>> UPGRADE_EFFECTS = new HashMap<>();
    static {
        UPGRADE_EFFECTS.put("seed_discount", Arrays.asList(15, 25, 35, 45, 50)); // Diminishing returns
        UPGRADE_EFFECTS.put("sell_bonus", Arrays.asList(20, 35, 50, 65, 75));
        UPGRADE_EFFECTS.put("tool_discount", Arrays.asList(25, 40, 55, 70, 80));
    }
    
    /**
     * Default constructor for GSON.
     */
    public PlayerUpgradeData() {
        this.upgradeLevels = new HashMap<>();
        this.unlockedFeatures = new HashSet<>();
        this.lastModified = System.currentTimeMillis();
        this.dataVersion = 1;
    }
    
    /**
     * Creates new player upgrade data.
     */
    public PlayerUpgradeData(UUID playerId) {
        this();
        this.playerId = playerId.toString();
    }
    
    /**
     * Gets the current level of an upgrade.
     */
    public int getUpgradeLevel(String upgradeId) {
        return upgradeLevels.getOrDefault(upgradeId, 0);
    }
    
    /**
     * Sets the level of an upgrade.
     */
    public void setUpgradeLevel(String upgradeId, int level) {
        int maxLevel = getMaxUpgradeLevel(upgradeId);
        if (level < 0) level = 0;
        if (level > maxLevel) level = maxLevel;
        
        if (level == 0) {
            upgradeLevels.remove(upgradeId);
        } else {
            upgradeLevels.put(upgradeId, level);
        }
        
        updateModificationTime();
    }
    
    /**
     * Increases the level of an upgrade by 1.
     */
    public boolean upgradeLevel(String upgradeId) {
        int currentLevel = getUpgradeLevel(upgradeId);
        int maxLevel = getMaxUpgradeLevel(upgradeId);
        
        if (currentLevel >= maxLevel) {
            return false; // Already at max level
        }
        
        setUpgradeLevel(upgradeId, currentLevel + 1);
        return true;
    }
    
    /**
     * Checks if a feature is unlocked.
     */
    public boolean isFeatureUnlocked(String featureId) {
        return unlockedFeatures.contains(featureId);
    }
    
    /**
     * Unlocks a feature.
     */
    public void unlockFeature(String featureId) {
        if (unlockedFeatures.add(featureId)) {
            updateModificationTime();
        }
    }
    
    /**
     * Locks a feature (for admin purposes).
     */
    public void lockFeature(String featureId) {
        if (unlockedFeatures.remove(featureId)) {
            updateModificationTime();
        }
    }
    
    /**
     * Gets the effect percentage for an upgrade at its current level.
     */
    public int getUpgradeEffect(String upgradeId) {
        int level = getUpgradeLevel(upgradeId);
        if (level <= 0) return 0;
        
        List<Integer> effects = UPGRADE_EFFECTS.get(upgradeId);
        if (effects == null || level > effects.size()) {
            Pokecobbleclaim.LOGGER.warn("No effect data for upgrade {} level {}", upgradeId, level);
            return 0;
        }
        
        return effects.get(level - 1); // Convert to 0-based index
    }
    
    /**
     * Gets the maximum level for an upgrade.
     */
    public static int getMaxUpgradeLevel(String upgradeId) {
        return MAX_UPGRADE_LEVELS.getOrDefault(upgradeId, 1);
    }
    
    /**
     * Gets the effect for a specific upgrade level.
     */
    public static int getUpgradeEffectAtLevel(String upgradeId, int level) {
        if (level <= 0) return 0;
        
        List<Integer> effects = UPGRADE_EFFECTS.get(upgradeId);
        if (effects == null || level > effects.size()) {
            return 0;
        }
        
        return effects.get(level - 1);
    }
    
    /**
     * Calculates discounted price based on upgrade effects.
     */
    public int calculateDiscountedPrice(int originalPrice, String discountType) {
        int discountPercent = getUpgradeEffect(discountType);
        if (discountPercent <= 0) return originalPrice;
        
        int discount = (originalPrice * discountPercent) / 100;
        return Math.max(1, originalPrice - discount); // Minimum price of 1
    }
    
    /**
     * Calculates bonus price based on upgrade effects.
     */
    public int calculateBonusPrice(int originalPrice, String bonusType) {
        int bonusPercent = getUpgradeEffect(bonusType);
        if (bonusPercent <= 0) return originalPrice;
        
        int bonus = (originalPrice * bonusPercent) / 100;
        return originalPrice + bonus;
    }
    
    private void updateModificationTime() {
        this.lastModified = System.currentTimeMillis();
        this.dataVersion++;
    }
    
    // Getters and setters
    public UUID getPlayerId() {
        return UUID.fromString(playerId);
    }
    
    public Map<String, Integer> getUpgradeLevels() {
        return new HashMap<>(upgradeLevels);
    }
    
    public Set<String> getUnlockedFeatures() {
        return new HashSet<>(unlockedFeatures);
    }
    
    public long getLastModified() {
        return lastModified;
    }
    
    public int getDataVersion() {
        return dataVersion;
    }
    
    public void setDataVersion(int dataVersion) {
        this.dataVersion = dataVersion;
    }
    
    /**
     * Creates a copy of this upgrade data.
     */
    public PlayerUpgradeData copy() {
        PlayerUpgradeData copy = new PlayerUpgradeData();
        copy.playerId = this.playerId;
        copy.upgradeLevels = new HashMap<>(this.upgradeLevels);
        copy.unlockedFeatures = new HashSet<>(this.unlockedFeatures);
        copy.lastModified = this.lastModified;
        copy.dataVersion = this.dataVersion;
        return copy;
    }
    
    @Override
    public String toString() {
        return String.format("PlayerUpgradeData{playerId=%s, upgrades=%d, unlocks=%d, version=%d}", 
            playerId, upgradeLevels.size(), unlockedFeatures.size(), dataVersion);
    }
}
