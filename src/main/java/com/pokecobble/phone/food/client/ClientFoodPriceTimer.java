package com.pokecobble.phone.food.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.food.data.FoodPriceTimerData;
import com.pokecobble.phone.network.FoodPriceTimerSyncHandler;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;

/**
 * Client-side manager for food price timer data.
 * Receives and caches timer data from the server.
 */
@Environment(EnvType.CLIENT)
public class ClientFoodPriceTimer {
    private static ClientFoodPriceTimer instance;
    
    // Cached timer data from server
    private FoodPriceTimerData timerData;
    private long lastSyncTime = 0;
    private long lastRequestTime = 0;
    private static final long REQUEST_COOLDOWN = 5000; // 5 seconds between requests
    
    private ClientFoodPriceTimer() {}
    
    public static ClientFoodPriceTimer getInstance() {
        if (instance == null) {
            instance = new ClientFoodPriceTimer();
        }
        return instance;
    }
    
    /**
     * Updates timer data from server sync.
     */
    public void updateTimerData(FoodPriceTimerData newTimerData) {
        this.timerData = newTimerData;
        this.lastSyncTime = System.currentTimeMillis();
        
        Pokecobbleclaim.LOGGER.debug("Updated client timer data - next update in: {}", 
            newTimerData.getFormattedTimeUntilNextUpdate());
    }
    
    /**
     * Gets the current timer data.
     */
    public FoodPriceTimerData getTimerData() {
        return timerData;
    }
    
    /**
     * Checks if timer data is available.
     */
    public boolean hasTimerData() {
        return timerData != null;
    }
    
    /**
     * Gets time remaining until next price update in milliseconds.
     */
    public long getTimeUntilNextUpdate() {
        if (timerData == null) {
            return 0;
        }
        return timerData.getTimeUntilNextUpdate();
    }
    
    /**
     * Gets formatted time string until next update.
     */
    public String getFormattedTimeUntilNextUpdate() {
        if (timerData == null) {
            return "Loading...";
        }
        return timerData.getFormattedTimeUntilNextUpdate();
    }
    
    /**
     * Checks if a price update is due.
     */
    public boolean isUpdateDue() {
        if (timerData == null) {
            return false;
        }
        return timerData.isUpdateDue();
    }
    
    /**
     * Gets the timestamp of the last sync from server.
     */
    public long getLastSyncTime() {
        return lastSyncTime;
    }
    
    /**
     * Checks if timer data is stale and needs refresh.
     */
    public boolean isDataStale() {
        if (timerData == null) {
            return true;
        }
        
        // Consider data stale if it's older than 5 minutes
        long timeSinceSync = System.currentTimeMillis() - lastSyncTime;
        return timeSinceSync > 5 * 60 * 1000;
    }
    
    /**
     * Requests fresh timer data from the server if needed.
     */
    public void requestTimerDataIfNeeded() {
        long currentTime = System.currentTimeMillis();
        
        // Check if we need to request data
        boolean shouldRequest = false;
        
        if (timerData == null) {
            shouldRequest = true;
        } else if (isDataStale()) {
            shouldRequest = true;
        }
        
        // Respect request cooldown
        if (shouldRequest && (currentTime - lastRequestTime) > REQUEST_COOLDOWN) {
            requestTimerData();
            lastRequestTime = currentTime;
        }
    }
    
    /**
     * Requests timer data from the server.
     */
    public void requestTimerData() {
        FoodPriceTimerSyncHandler.requestTimerData();
        Pokecobbleclaim.LOGGER.debug("Requested timer data from server");
    }
    
    /**
     * Gets timer status information for debugging.
     */
    public String getDebugInfo() {
        if (timerData == null) {
            return "No timer data available";
        }
        
        return String.format(
            "Timer Debug: hasData=%s, timeUntilNext=%s, lastSync=%dms ago, isStale=%s",
            hasTimerData(),
            getFormattedTimeUntilNextUpdate(),
            System.currentTimeMillis() - lastSyncTime,
            isDataStale()
        );
    }
    
    /**
     * Clears all cached timer data.
     */
    public void clearCache() {
        timerData = null;
        lastSyncTime = 0;
        lastRequestTime = 0;
        
        Pokecobbleclaim.LOGGER.debug("Cleared client timer cache");
    }
    
    /**
     * Gets a fallback timer text when server data is unavailable.
     */
    public String getFallbackTimerText() {
        if (timerData == null) {
            // Request data if we don't have it
            requestTimerDataIfNeeded();
            return "§6Next Price Update: §fConnecting...";
        }
        
        if (isDataStale()) {
            // Request fresh data if current data is stale
            requestTimerDataIfNeeded();
            return "§6Next Price Update: §fRefreshing...";
        }
        
        // Use cached data
        String timeText = getFormattedTimeUntilNextUpdate();
        if (isUpdateDue()) {
            return "§6Next Price Update: §fUpdating soon...";
        }
        
        return "§6Next Price Update: §f" + timeText;
    }
}
