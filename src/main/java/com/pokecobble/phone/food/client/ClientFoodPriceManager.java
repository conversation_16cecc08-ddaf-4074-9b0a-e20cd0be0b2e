package com.pokecobble.phone.food.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.food.data.FoodPriceData;
import com.pokecobble.phone.network.FoodPriceSyncHandler;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Client-side manager for food price data.
 * Receives and caches price data from the server.
 */
@Environment(EnvType.CLIENT)
public class ClientFoodPriceManager {
    private static ClientFoodPriceManager instance;
    
    // Cached price data from server
    private final Map<String, Integer> currentPrices = new ConcurrentHashMap<>();
    private final Map<String, List<FoodPriceData.PriceHistoryEntry>> priceHistory = new ConcurrentHashMap<>();
    private long lastUpdateTime = 0;
    private long lastSyncTime = 0;
    
    // Price update listeners
    private final List<PriceUpdateListener> listeners = new ArrayList<>();
    
    private ClientFoodPriceManager() {}
    
    public static ClientFoodPriceManager getInstance() {
        if (instance == null) {
            instance = new ClientFoodPriceManager();
        }
        return instance;
    }
    
    /**
     * Updates current prices and price history from server sync.
     */
    public void updatePrices(Map<String, Integer> newCurrentPrices,
                           Map<String, List<FoodPriceData.PriceHistoryEntry>> newPriceHistory,
                           long timestamp) {
        // Update cached data
        currentPrices.clear();
        currentPrices.putAll(newCurrentPrices);
        
        priceHistory.clear();
        priceHistory.putAll(newPriceHistory);
        
        lastSyncTime = timestamp;
        lastUpdateTime = System.currentTimeMillis();
        
        // Notify listeners
        notifyPriceUpdate(newCurrentPrices);
        
        Pokecobbleclaim.LOGGER.debug("Updated client price cache with {} items", newCurrentPrices.size());
    }
    
    /**
     * Updates only current prices (for price update notifications).
     */
    public void updateCurrentPrices(Map<String, Integer> newCurrentPrices, long timestamp) {
        // Update current prices
        currentPrices.putAll(newCurrentPrices);
        lastSyncTime = timestamp;
        lastUpdateTime = System.currentTimeMillis();
        
        // Add new price entries to history
        for (Map.Entry<String, Integer> entry : newCurrentPrices.entrySet()) {
            String foodItem = entry.getKey();
            int newPrice = entry.getValue();
            
            List<FoodPriceData.PriceHistoryEntry> history = priceHistory.computeIfAbsent(
                foodItem, k -> new ArrayList<>());
            
            // Add new price entry
            history.add(new FoodPriceData.PriceHistoryEntry(timestamp, newPrice));
            
            // Keep only last 10 entries
            while (history.size() > 10) {
                history.remove(0);
            }
        }
        
        // Notify listeners
        notifyPriceUpdate(newCurrentPrices);
        
        Pokecobbleclaim.LOGGER.debug("Updated current prices for {} items", newCurrentPrices.size());
    }
    
    /**
     * Gets the current price for a food item.
     */
    public int getCurrentPrice(String foodItem) {
        return currentPrices.getOrDefault(foodItem, 0);
    }
    
    /**
     * Gets all current prices.
     */
    public Map<String, Integer> getCurrentPrices() {
        return new HashMap<>(currentPrices);
    }
    
    /**
     * Gets price history for a food item.
     */
    public List<FoodPriceData.PriceHistoryEntry> getPriceHistory(String foodItem) {
        List<FoodPriceData.PriceHistoryEntry> history = priceHistory.get(foodItem);
        return history != null ? new ArrayList<>(history) : new ArrayList<>();
    }
    
    /**
     * Gets all price history data.
     */
    public Map<String, List<FoodPriceData.PriceHistoryEntry>> getAllPriceHistory() {
        Map<String, List<FoodPriceData.PriceHistoryEntry>> result = new HashMap<>();
        for (Map.Entry<String, List<FoodPriceData.PriceHistoryEntry>> entry : priceHistory.entrySet()) {
            result.put(entry.getKey(), new ArrayList<>(entry.getValue()));
        }
        return result;
    }
    
    /**
     * Checks if price data is available.
     */
    public boolean hasPriceData() {
        return !currentPrices.isEmpty();
    }
    
    /**
     * Gets the timestamp of the last price sync from server.
     */
    public long getLastSyncTime() {
        return lastSyncTime;
    }
    
    /**
     * Gets the timestamp of the last local update.
     */
    public long getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    /**
     * Requests fresh price data from the server.
     */
    public void requestPriceUpdate() {
        FoodPriceSyncHandler.requestPriceData();
    }
    
    /**
     * Adds a price update listener.
     */
    public void addPriceUpdateListener(PriceUpdateListener listener) {
        listeners.add(listener);
    }
    
    /**
     * Removes a price update listener.
     */
    public void removePriceUpdateListener(PriceUpdateListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * Notifies all listeners of price updates.
     */
    private void notifyPriceUpdate(Map<String, Integer> updatedPrices) {
        for (PriceUpdateListener listener : listeners) {
            try {
                listener.onPriceUpdate(updatedPrices);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error notifying price update listener: " + e.getMessage());
            }
        }
    }
    
    /**
     * Calculates price statistics for a food item.
     */
    public PriceStatistics getPriceStatistics(String foodItem) {
        List<FoodPriceData.PriceHistoryEntry> history = getPriceHistory(foodItem);
        
        if (history.isEmpty()) {
            return new PriceStatistics(0, 0, 0, 0, false);
        }
        
        // Calculate average price
        double averagePrice = history.stream()
            .mapToInt(FoodPriceData.PriceHistoryEntry::getPrice)
            .average()
            .orElse(0.0);
        
        // Calculate min and max prices
        int minPrice = history.stream()
            .mapToInt(FoodPriceData.PriceHistoryEntry::getPrice)
            .min()
            .orElse(0);
        
        int maxPrice = history.stream()
            .mapToInt(FoodPriceData.PriceHistoryEntry::getPrice)
            .max()
            .orElse(0);
        
        // Calculate price change percentage
        double priceChangePercent = 0.0;
        boolean isPriceRising = false;
        
        if (history.size() >= 2) {
            int oldestPrice = history.get(0).getPrice();
            int newestPrice = history.get(history.size() - 1).getPrice();
            priceChangePercent = ((double)(newestPrice - oldestPrice) / oldestPrice) * 100;
            isPriceRising = priceChangePercent > 0;
        }
        
        return new PriceStatistics(averagePrice, minPrice, maxPrice, priceChangePercent, isPriceRising);
    }
    
    /**
     * Clears all cached price data.
     */
    public void clearCache() {
        currentPrices.clear();
        priceHistory.clear();
        lastUpdateTime = 0;
        lastSyncTime = 0;
        
        Pokecobbleclaim.LOGGER.debug("Cleared client price cache");
    }
    
    /**
     * Interface for price update listeners.
     */
    public interface PriceUpdateListener {
        void onPriceUpdate(Map<String, Integer> updatedPrices);
    }
    
    /**
     * Price statistics data class.
     */
    public static class PriceStatistics {
        private final double averagePrice;
        private final int minPrice;
        private final int maxPrice;
        private final double priceChangePercent;
        private final boolean isPriceRising;
        
        public PriceStatistics(double averagePrice, int minPrice, int maxPrice, 
                             double priceChangePercent, boolean isPriceRising) {
            this.averagePrice = averagePrice;
            this.minPrice = minPrice;
            this.maxPrice = maxPrice;
            this.priceChangePercent = priceChangePercent;
            this.isPriceRising = isPriceRising;
        }
        
        public double getAveragePrice() { return averagePrice; }
        public int getMinPrice() { return minPrice; }
        public int getMaxPrice() { return maxPrice; }
        public double getPriceChangePercent() { return priceChangePercent; }
        public boolean isPriceRising() { return isPriceRising; }
    }
}
