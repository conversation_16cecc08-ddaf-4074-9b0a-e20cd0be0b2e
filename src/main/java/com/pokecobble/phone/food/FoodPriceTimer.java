package com.pokecobble.phone.food;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.server.MinecraftServer;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;

/**
 * Manages the 24-hour timer for food price updates.
 * Integrates with the server tick system to check for price updates.
 */
public class FoodPriceTimer {
    private static FoodPriceTimer instance;
    
    // Timer configuration
    private static final long PRICE_UPDATE_INTERVAL_MS = 24L * 60L * 60L * 1000L; // 24 hours in milliseconds
    private static final long PRICE_UPDATE_INTERVAL_TICKS = 24L * 60L * 60L * 20L; // 24 hours in ticks (20 ticks per second)
    private static final long SYNC_INTERVAL_MS = 30L * 60L * 1000L; // 30 minutes in milliseconds
    private static final long SAVE_INTERVAL_MS = 60L * 1000L; // Save every minute

    // Timer state
    private MinecraftServer server;
    private long nextPriceUpdateTime = 0; // Server-side timestamp when next update should occur
    private long lastSaveTime = 0;
    private long lastSyncTime = 0;
    private boolean isInitialized = false;
    
    private FoodPriceTimer() {}
    
    public static FoodPriceTimer getInstance() {
        if (instance == null) {
            instance = new FoodPriceTimer();
        }
        return instance;
    }
    
    /**
     * Initializes the timer with the server instance.
     */
    public void initialize(MinecraftServer server) {
        this.server = server;
        this.isInitialized = true;

        // Load existing timer data
        loadTimerData();

        // If no timer data exists, create initial data
        if (nextPriceUpdateTime == 0) {
            FoodPriceManager priceManager = FoodPriceManager.getInstance();
            long lastUpdateTime = priceManager.getLastUpdateTime();

            if (lastUpdateTime > 0) {
                // Calculate next update based on last price update
                nextPriceUpdateTime = lastUpdateTime + PRICE_UPDATE_INTERVAL_MS;
            } else {
                // No previous update, schedule for 24 hours from now
                nextPriceUpdateTime = System.currentTimeMillis() + PRICE_UPDATE_INTERVAL_MS;
            }

            saveTimerData();
        }

        lastSaveTime = System.currentTimeMillis();
        lastSyncTime = System.currentTimeMillis();

        Pokecobbleclaim.LOGGER.info("FoodPriceTimer initialized - next update in {}",
            getFormattedTimeUntilNextUpdate());
    }
    
    /**
     * Called every server tick to check for price updates and perform maintenance.
     * Should be called from ServerInitializer's server tick event.
     */
    public void onServerTick(MinecraftServer server) {
        if (!isInitialized || server != this.server) {
            return;
        }

        long currentTime = System.currentTimeMillis();

        // Check if price update is due
        if (currentTime >= nextPriceUpdateTime) {
            performPriceUpdate();
        }

        // Save timer data every minute
        if (currentTime - lastSaveTime >= SAVE_INTERVAL_MS) {
            saveTimerData();
            lastSaveTime = currentTime;
        }

        // Sync timer data to all players every 30 minutes
        if (currentTime - lastSyncTime >= SYNC_INTERVAL_MS) {
            syncTimerToAllPlayers();
            lastSyncTime = currentTime;
        }
    }
    
    /**
     * Performs the price update and resets the timer.
     */
    private void performPriceUpdate() {
        try {
            long currentTime = System.currentTimeMillis();
            Pokecobbleclaim.LOGGER.info("Performing scheduled 24-hour price update");

            // Update prices through the price manager
            FoodPriceManager priceManager = FoodPriceManager.getInstance();
            priceManager.forceUpdatePrices();

            // Schedule next update
            nextPriceUpdateTime = currentTime + PRICE_UPDATE_INTERVAL_MS;

            // Save updated timer data
            saveTimerData();

            // Sync to all players immediately
            syncTimerToAllPlayers();

            Pokecobbleclaim.LOGGER.info("Price update completed - next update at {}",
                java.time.LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(nextPriceUpdateTime),
                    java.time.ZoneId.systemDefault()));

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during scheduled price update: " + e.getMessage());
            e.printStackTrace();

            // Still schedule next update to prevent system from stopping
            nextPriceUpdateTime = System.currentTimeMillis() + PRICE_UPDATE_INTERVAL_MS;
            saveTimerData();
        }
    }
    
    /**
     * Forces an immediate price update (for admin commands or testing).
     */
    public void forceUpdate() {
        if (!isInitialized || server == null) {
            Pokecobbleclaim.LOGGER.warn("Cannot force price update - timer not initialized");
            return;
        }

        performPriceUpdate();
        Pokecobbleclaim.LOGGER.info("Forced price update completed");
    }
    
    /**
     * Gets the number of milliseconds until the next price update.
     */
    public long getMillisecondsUntilNextUpdate() {
        if (!isInitialized || nextPriceUpdateTime == 0) {
            return 0;
        }

        long currentTime = System.currentTimeMillis();
        return Math.max(0, nextPriceUpdateTime - currentTime);
    }
    
    /**
     * Gets the number of seconds until the next price update.
     */
    public long getSecondsUntilNextUpdate() {
        return getMillisecondsUntilNextUpdate() / 1000;
    }
    
    /**
     * Gets the number of minutes until the next price update.
     */
    public long getMinutesUntilNextUpdate() {
        return getSecondsUntilNextUpdate() / 60;
    }
    
    /**
     * Gets the number of hours until the next price update.
     */
    public double getHoursUntilNextUpdate() {
        return getMinutesUntilNextUpdate() / 60.0;
    }
    
    /**
     * Gets a formatted string showing time until next update.
     */
    public String getFormattedTimeUntilNextUpdate() {
        long totalSeconds = getSecondsUntilNextUpdate();
        
        if (totalSeconds <= 0) {
            return "Updating soon...";
        }
        
        long hours = totalSeconds / 3600;
        long minutes = (totalSeconds % 3600) / 60;
        long seconds = totalSeconds % 60;
        
        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
        } else {
            return String.format("%02d:%02d", minutes, seconds);
        }
    }
    
    /**
     * Gets the timestamp of the next scheduled update.
     */
    public long getNextUpdateTimestamp() {
        return System.currentTimeMillis() + getMillisecondsUntilNextUpdate();
    }
    
    /**
     * Gets the LocalDateTime of the next scheduled update.
     */
    public LocalDateTime getNextUpdateDateTime() {
        return LocalDateTime.ofInstant(
            Instant.ofEpochMilli(getNextUpdateTimestamp()), 
            ZoneId.systemDefault()
        );
    }
    
    /**
     * Checks if a price update is due (within the next minute).
     */
    public boolean isUpdateDue() {
        return getSecondsUntilNextUpdate() <= 60;
    }
    
    /**
     * Gets debug information about the timer state.
     */
    public String getDebugInfo() {
        if (!isInitialized) {
            return "Timer not initialized";
        }

        return String.format(
            "Timer Debug: initialized=%s, nextUpdate=%d, timeUntilNext=%s, lastSave=%dms ago",
            isInitialized,
            nextPriceUpdateTime,
            getFormattedTimeUntilNextUpdate(),
            System.currentTimeMillis() - lastSaveTime
        );
    }

    /**
     * Resets the timer (for testing purposes).
     */
    public void resetTimer() {
        if (isInitialized) {
            nextPriceUpdateTime = System.currentTimeMillis() + PRICE_UPDATE_INTERVAL_MS;
            saveTimerData();
            syncTimerToAllPlayers();
            Pokecobbleclaim.LOGGER.info("Price update timer reset - next update in 24 hours");
        }
    }

    /**
     * Sets a custom interval for testing (in milliseconds).
     * WARNING: This is for testing only and will reset on server restart.
     */
    public void setTestInterval(long intervalMs) {
        if (isInitialized) {
            nextPriceUpdateTime = System.currentTimeMillis() + intervalMs;
            saveTimerData();
            syncTimerToAllPlayers();
            Pokecobbleclaim.LOGGER.warn("Set test interval: {}ms until next update", intervalMs);
        }
    }

    /**
     * Loads timer data from storage.
     */
    private void loadTimerData() {
        try {
            com.pokecobble.phone.food.data.FoodPriceTimerData data =
                com.pokecobble.phone.food.data.FoodPriceStorage.loadTimerData();

            if (data != null) {
                nextPriceUpdateTime = data.getNextPriceUpdateTime();
                Pokecobbleclaim.LOGGER.info("Loaded timer data - next update at {}",
                    java.time.LocalDateTime.ofInstant(
                        java.time.Instant.ofEpochMilli(nextPriceUpdateTime),
                        java.time.ZoneId.systemDefault()));
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load timer data: " + e.getMessage());
        }
    }

    /**
     * Saves current timer data to storage.
     */
    private void saveTimerData() {
        try {
            FoodPriceManager priceManager = FoodPriceManager.getInstance();
            long lastUpdateTime = priceManager.getLastUpdateTime();

            com.pokecobble.phone.food.data.FoodPriceTimerData data =
                new com.pokecobble.phone.food.data.FoodPriceTimerData(nextPriceUpdateTime, lastUpdateTime);

            com.pokecobble.phone.food.data.FoodPriceStorage.saveTimerData(data);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to save timer data: " + e.getMessage());
        }
    }

    /**
     * Synchronizes timer data to all connected players.
     */
    private void syncTimerToAllPlayers() {
        if (server != null) {
            try {
                FoodPriceManager priceManager = FoodPriceManager.getInstance();
                long lastUpdateTime = priceManager.getLastUpdateTime();

                com.pokecobble.phone.food.data.FoodPriceTimerData data =
                    new com.pokecobble.phone.food.data.FoodPriceTimerData(nextPriceUpdateTime, lastUpdateTime);

                com.pokecobble.phone.network.FoodPriceTimerSyncHandler.syncTimerToAllPlayers(server, data);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to sync timer to players: " + e.getMessage());
            }
        }
    }

    /**
     * Gets the current timer data for synchronization.
     */
    public com.pokecobble.phone.food.data.FoodPriceTimerData getTimerData() {
        if (!isInitialized) {
            return null;
        }

        FoodPriceManager priceManager = FoodPriceManager.getInstance();
        long lastUpdateTime = priceManager.getLastUpdateTime();

        return new com.pokecobble.phone.food.data.FoodPriceTimerData(nextPriceUpdateTime, lastUpdateTime);
    }

    /**
     * Synchronizes timer data to a specific player (called when player joins).
     */
    public void syncTimerToPlayer(net.minecraft.server.network.ServerPlayerEntity player) {
        if (isInitialized && server != null) {
            try {
                com.pokecobble.phone.food.data.FoodPriceTimerData data = getTimerData();
                if (data != null) {
                    com.pokecobble.phone.network.FoodPriceTimerSyncHandler.syncTimerToPlayer(player, data);
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to sync timer to player {}: {}",
                    player.getName().getString(), e.getMessage());
            }
        }
    }
    
    /**
     * Shuts down the timer.
     */
    public void shutdown() {
        isInitialized = false;
        server = null;
        Pokecobbleclaim.LOGGER.info("FoodPriceTimer shut down");
    }
}
