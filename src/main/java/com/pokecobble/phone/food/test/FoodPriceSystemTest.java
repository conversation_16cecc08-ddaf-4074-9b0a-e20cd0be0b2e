package com.pokecobble.phone.food.test;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.food.FoodPriceManager;
import com.pokecobble.phone.food.FoodPriceTimer;
import com.pokecobble.phone.food.data.FoodPriceData;
import com.pokecobble.phone.food.data.FoodPriceStorage;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test class for validating the food price system functionality.
 */
public class FoodPriceSystemTest {
    
    /**
     * Runs all food price system tests.
     */
    public static boolean runAllTests() {
        Pokecobbleclaim.LOGGER.info("Starting food price system tests...");
        
        boolean allTestsPassed = true;
        
        try {
            // Test 1: Price Manager Initialization
            if (!testPriceManagerInitialization()) {
                allTestsPassed = false;
                Pokecobbleclaim.LOGGER.error("Price manager initialization test FAILED");
            } else {
                Pokecobbleclaim.LOGGER.info("Price manager initialization test PASSED");
            }
            
            // Test 2: Price Generation
            if (!testPriceGeneration()) {
                allTestsPassed = false;
                Pokecobbleclaim.LOGGER.error("Price generation test FAILED");
            } else {
                Pokecobbleclaim.LOGGER.info("Price generation test PASSED");
            }
            
            // Test 3: Price History
            if (!testPriceHistory()) {
                allTestsPassed = false;
                Pokecobbleclaim.LOGGER.error("Price history test FAILED");
            } else {
                Pokecobbleclaim.LOGGER.info("Price history test PASSED");
            }
            
            // Test 4: Timer Functionality
            if (!testTimerFunctionality()) {
                allTestsPassed = false;
                Pokecobbleclaim.LOGGER.error("Timer functionality test FAILED");
            } else {
                Pokecobbleclaim.LOGGER.info("Timer functionality test PASSED");
            }
            
            // Test 5: Data Persistence (if possible)
            if (!testDataPersistence()) {
                allTestsPassed = false;
                Pokecobbleclaim.LOGGER.error("Data persistence test FAILED");
            } else {
                Pokecobbleclaim.LOGGER.info("Data persistence test PASSED");
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Exception during food price system tests: " + e.getMessage());
            e.printStackTrace();
            allTestsPassed = false;
        }
        
        if (allTestsPassed) {
            Pokecobbleclaim.LOGGER.info("All food price system tests PASSED");
        } else {
            Pokecobbleclaim.LOGGER.error("Some food price system tests FAILED");
        }
        
        return allTestsPassed;
    }
    
    /**
     * Tests price manager initialization.
     */
    private static boolean testPriceManagerInitialization() {
        try {
            FoodPriceManager manager = FoodPriceManager.getInstance();
            
            // Check if manager is not null
            if (manager == null) {
                Pokecobbleclaim.LOGGER.error("Price manager instance is null");
                return false;
            }
            
            // Check if food items are available
            List<String> foodItems = FoodPriceManager.getFoodItems();
            if (foodItems.isEmpty()) {
                Pokecobbleclaim.LOGGER.error("No food items configured");
                return false;
            }
            
            Pokecobbleclaim.LOGGER.debug("Price manager initialized with {} food items", foodItems.size());
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error testing price manager initialization: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Tests price generation functionality.
     */
    private static boolean testPriceGeneration() {
        try {
            FoodPriceManager manager = FoodPriceManager.getInstance();
            
            // Get current prices
            Map<String, Integer> prices = manager.getCurrentPrices();
            
            if (prices.isEmpty()) {
                Pokecobbleclaim.LOGGER.error("No current prices available");
                return false;
            }
            
            // Check if all food items have prices
            for (String foodItem : FoodPriceManager.getFoodItems()) {
                Integer price = prices.get(foodItem);
                if (price == null || price <= 0) {
                    Pokecobbleclaim.LOGGER.error("Invalid price for food item: " + foodItem + " (price: " + price + ")");
                    return false;
                }
            }
            
            Pokecobbleclaim.LOGGER.debug("Price generation test passed with {} prices", prices.size());
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error testing price generation: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Tests price history functionality.
     */
    private static boolean testPriceHistory() {
        try {
            FoodPriceManager manager = FoodPriceManager.getInstance();
            
            // Get price history for all items
            Map<String, List<FoodPriceData.PriceHistoryEntry>> allHistory = manager.getAllPriceHistory();
            
            if (allHistory.isEmpty()) {
                Pokecobbleclaim.LOGGER.error("No price history available");
                return false;
            }
            
            // Check if all food items have history
            for (String foodItem : FoodPriceManager.getFoodItems()) {
                List<FoodPriceData.PriceHistoryEntry> history = allHistory.get(foodItem);
                if (history == null || history.isEmpty()) {
                    Pokecobbleclaim.LOGGER.error("No price history for food item: " + foodItem);
                    return false;
                }
                
                // Check if history entries are valid
                for (FoodPriceData.PriceHistoryEntry entry : history) {
                    if (entry.getTimestamp() <= 0 || entry.getPrice() <= 0) {
                        Pokecobbleclaim.LOGGER.error("Invalid price history entry for " + foodItem + 
                            ": timestamp=" + entry.getTimestamp() + ", price=" + entry.getPrice());
                        return false;
                    }
                }
            }
            
            Pokecobbleclaim.LOGGER.debug("Price history test passed");
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error testing price history: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Tests timer functionality.
     */
    private static boolean testTimerFunctionality() {
        try {
            FoodPriceTimer timer = FoodPriceTimer.getInstance();
            
            // Check if timer provides valid time information
            long timeUntilNext = timer.getMillisecondsUntilNextUpdate();
            if (timeUntilNext < 0) {
                Pokecobbleclaim.LOGGER.error("Invalid time until next update: " + timeUntilNext);
                return false;
            }
            
            // Check formatted time string
            String formattedTime = timer.getFormattedTimeUntilNextUpdate();
            if (formattedTime == null || formattedTime.isEmpty()) {
                Pokecobbleclaim.LOGGER.error("Invalid formatted time string");
                return false;
            }
            
            // Check debug info
            String debugInfo = timer.getDebugInfo();
            if (debugInfo == null || debugInfo.isEmpty()) {
                Pokecobbleclaim.LOGGER.error("Invalid debug info");
                return false;
            }
            
            Pokecobbleclaim.LOGGER.debug("Timer functionality test passed - next update in: " + formattedTime);
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error testing timer functionality: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Tests data persistence functionality.
     */
    private static boolean testDataPersistence() {
        try {
            FoodPriceManager manager = FoodPriceManager.getInstance();
            
            // Get current data
            Map<String, Integer> currentPrices = manager.getCurrentPrices();
            Map<String, List<FoodPriceData.PriceHistoryEntry>> priceHistory = manager.getAllPriceHistory();
            
            if (currentPrices.isEmpty() || priceHistory.isEmpty()) {
                Pokecobbleclaim.LOGGER.warn("No data available for persistence test");
                return true; // Not a failure, just no data to test
            }
            
            // Create test data object
            FoodPriceData testData = new FoodPriceData(
                currentPrices, 
                priceHistory, 
                new java.util.HashMap<>(), // Empty trends for test
                System.currentTimeMillis()
            );
            
            // Test data validation (this would normally be done during save/load)
            if (testData.getCurrentPrices() == null || testData.getPriceHistory() == null) {
                Pokecobbleclaim.LOGGER.error("Test data object is invalid");
                return false;
            }
            
            Pokecobbleclaim.LOGGER.debug("Data persistence test passed");
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error testing data persistence: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Runs a quick validation test for the food price system.
     */
    public static void runQuickValidation() {
        Pokecobbleclaim.LOGGER.info("Running quick food price system validation...");

        try {
            FoodPriceManager manager = FoodPriceManager.getInstance();
            FoodPriceTimer timer = FoodPriceTimer.getInstance();

            // Log current state
            Map<String, Integer> prices = manager.getCurrentPrices();
            Pokecobbleclaim.LOGGER.info("Current food prices: " + prices);

            String timeInfo = timer.getFormattedTimeUntilNextUpdate();
            Pokecobbleclaim.LOGGER.info("Time until next update: " + timeInfo);

            // Test price generation to ensure it works
            testPriceChangeGeneration();

            Pokecobbleclaim.LOGGER.info("Quick validation completed successfully");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during quick validation: " + e.getMessage());
        }
    }

    /**
     * Tests that price generation actually produces different prices.
     */
    private static void testPriceChangeGeneration() {
        try {
            FoodPriceManager manager = FoodPriceManager.getInstance();

            // Get current prices
            Map<String, Integer> originalPrices = new HashMap<>(manager.getCurrentPrices());

            // Force 3 price updates and check for changes
            boolean pricesChanged = false;
            for (int i = 0; i < 3; i++) {
                manager.forceUpdatePrices();

                Map<String, Integer> newPrices = manager.getCurrentPrices();

                // Check if any price changed
                for (String foodItem : FoodPriceManager.getFoodItems()) {
                    int originalPrice = originalPrices.get(foodItem);
                    int newPrice = newPrices.get(foodItem);

                    if (originalPrice != newPrice) {
                        pricesChanged = true;
                        Pokecobbleclaim.LOGGER.info("Price change detected for {}: {} → {} coins",
                            foodItem, originalPrice, newPrice);
                    }
                }

                if (pricesChanged) break;
            }

            if (pricesChanged) {
                Pokecobbleclaim.LOGGER.info("✓ Price generation test PASSED - prices are changing correctly");
            } else {
                Pokecobbleclaim.LOGGER.warn("⚠ Price generation test WARNING - no price changes detected after 3 updates");
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error testing price change generation: " + e.getMessage());
        }
    }
}
