package com.pokecobble.phone.food;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.food.data.FoodPriceData;
import com.pokecobble.phone.food.data.FoodPriceStorage;
import com.pokecobble.phone.network.FoodPriceSyncHandler;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Server-side manager for dynamic food pricing system.
 * Handles price generation, updates, persistence, and synchronization.
 */
public class FoodPriceManager {
    private static FoodPriceManager instance;
    
    // Food items that have dynamic pricing (using mutable set for efficient lookups and additions)
    private static final Set<String> FOOD_ITEMS = new HashSet<>(Arrays.asList(
        "wheat", "carrots", "potatoes", "beetroot"
    ));
    
    // Price configuration
    private static final Map<String, Integer> BASE_PRICES = new HashMap<>();
    private static final Map<String, Double> PRICE_VOLATILITY = new HashMap<>();
    private static final Map<String, Integer> MIN_PRICES = new HashMap<>();
    private static final Map<String, Integer> MAX_PRICES = new HashMap<>();

    // Economic limits
    private static final double MAX_TREND_STRENGTH = 0.15; // Maximum trend effect
    private static final double TREND_DECAY = 0.95; // Trends decay over time
    
    static {
        // Base prices for each food item
        BASE_PRICES.put("wheat", 8);
        BASE_PRICES.put("carrots", 6);
        BASE_PRICES.put("potatoes", 7);
        BASE_PRICES.put("beetroot", 10);
        
        // Price volatility (0.0 to 1.0) - how much prices can fluctuate (balanced for economy)
        PRICE_VOLATILITY.put("wheat", 0.15);      // Stable crop, low volatility
        PRICE_VOLATILITY.put("carrots", 0.20);    // Moderate volatility
        PRICE_VOLATILITY.put("potatoes", 0.12);   // Most stable
        PRICE_VOLATILITY.put("beetroot", 0.25);   // Highest volatility, but still reasonable

        // Minimum prices (50% of base price - prevents crashes)
        MIN_PRICES.put("wheat", 4);      // 50% of 8
        MIN_PRICES.put("carrots", 3);    // 50% of 6
        MIN_PRICES.put("potatoes", 3);   // 50% of 7 (rounded down)
        MIN_PRICES.put("beetroot", 5);   // 50% of 10

        // Maximum prices (200% of base price - prevents economy breaking)
        MAX_PRICES.put("wheat", 16);     // 200% of 8
        MAX_PRICES.put("carrots", 12);   // 200% of 6
        MAX_PRICES.put("potatoes", 14);  // 200% of 7
        MAX_PRICES.put("beetroot", 20);  // 200% of 10
    }
    
    // Current prices and price history
    private final Map<String, Integer> currentPrices = new ConcurrentHashMap<>();
    private final Map<String, List<FoodPriceData.PriceHistoryEntry>> priceHistory = new ConcurrentHashMap<>();
    
    // Timer management
    private MinecraftServer server;
    private long lastPriceUpdate = 0;
    private static final long PRICE_UPDATE_INTERVAL = 24L * 60L * 60L * 1000L; // 24 hours in milliseconds
    
    // Price generation state
    private final Map<String, Double> currentTrends = new ConcurrentHashMap<>();
    private final Random random = new Random();
    
    private FoodPriceManager() {
        initializePrices();
    }
    
    public static FoodPriceManager getInstance() {
        if (instance == null) {
            instance = new FoodPriceManager();
        }
        return instance;
    }
    
    /**
     * Initializes the price manager with server instance.
     */
    public void initialize(MinecraftServer server) {
        this.server = server;
        
        // Load existing price data from storage
        loadPriceData();
        
        // Check if we need to generate initial prices
        if (currentPrices.isEmpty()) {
            generateInitialPrices();
            savePriceData();
        }
        
        // Check if we need to update prices on startup
        checkForPriceUpdate();
        
        // Validate and fix any extreme prices
        validateAndFixPrices();

        // Log final state after initialization
        logCurrentState();

        Pokecobbleclaim.LOGGER.info("FoodPriceManager initialized with {} food items", FOOD_ITEMS.size());
    }
    
    /**
     * Initializes default prices for all food items.
     */
    private void initializePrices() {
        for (String foodItem : FOOD_ITEMS) {
            currentPrices.put(foodItem, BASE_PRICES.get(foodItem));
            priceHistory.put(foodItem, new ArrayList<>());
            currentTrends.put(foodItem, 0.0); // Neutral trend initially
        }
    }
    
    /**
     * Generates initial prices and price history.
     */
    private void generateInitialPrices() {
        long currentTime = System.currentTimeMillis();
        
        for (String foodItem : FOOD_ITEMS) {
            // Generate 10 days of historical prices with more variation
            List<FoodPriceData.PriceHistoryEntry> history = new ArrayList<>();
            int basePrice = BASE_PRICES.get(foodItem);
            double volatility = PRICE_VOLATILITY.get(foodItem);
            int minPrice = MIN_PRICES.get(foodItem);
            int maxPrice = MAX_PRICES.get(foodItem);

            // Generate trend for this food item
            double trend = (random.nextDouble() - 0.5) * 0.3; // Smaller initial trend
            currentTrends.put(foodItem, trend);

            int previousPrice = basePrice;
            for (int day = 9; day >= 0; day--) {
                long timestamp = currentTime - (day * 24L * 60L * 60L * 1000L);

                // For historical data, use more varied pricing
                if (day == 9) {
                    // Start close to base price with small variation
                    double variation = (random.nextGaussian() * volatility * 0.15);
                    previousPrice = Math.max(minPrice, Math.min(maxPrice,
                        (int) Math.round(basePrice * (1 + variation))));
                } else {
                    // Generate price using sophisticated model
                    previousPrice = generateNextPrice(previousPrice, basePrice, volatility, trend, minPrice, maxPrice);
                }

                history.add(new FoodPriceData.PriceHistoryEntry(timestamp, previousPrice));
            }
            
            priceHistory.put(foodItem, history);
            
            // Set current price to the most recent price
            if (!history.isEmpty()) {
                currentPrices.put(foodItem, history.get(history.size() - 1).getPrice());
            }
        }
        
        lastPriceUpdate = currentTime;
        Pokecobbleclaim.LOGGER.info("Generated initial prices for all food items");
    }

    /**
     * Logs the current state of the price manager for debugging.
     */
    private void logCurrentState() {
        Pokecobbleclaim.LOGGER.info("=== FoodPriceManager Current State ===");
        for (String foodItem : FOOD_ITEMS) {
            int price = currentPrices.getOrDefault(foodItem, 0);
            int historySize = priceHistory.getOrDefault(foodItem, new ArrayList<>()).size();
            double trend = currentTrends.getOrDefault(foodItem, 0.0);

            Pokecobbleclaim.LOGGER.info("  {}: price={} coins, history={} entries, trend={:.3f}",
                foodItem, price, historySize, trend);

            // Log recent price history for debugging
            List<FoodPriceData.PriceHistoryEntry> history = priceHistory.get(foodItem);
            if (history != null && !history.isEmpty()) {
                Pokecobbleclaim.LOGGER.debug("    Recent prices: {}",
                    history.stream()
                        .skip(Math.max(0, history.size() - 3))
                        .map(entry -> entry.getPrice() + " coins")
                        .reduce((a, b) -> a + ", " + b)
                        .orElse("none"));
            }
        }
        Pokecobbleclaim.LOGGER.info("=== End Current State ===");
    }



    /**
     * Generates a new price with sophisticated market simulation.
     * Uses gravitational pull toward base price with rare high spikes.
     */
    private int generateNewPriceWithGuaranteedChange(int oldPrice, int basePrice, double volatility,
                                                   double trend, boolean marketCrash, boolean marketBoom, String foodItem) {

        // Get price bounds for this item
        int minPrice = MIN_PRICES.get(foodItem);
        int maxPrice = MAX_PRICES.get(foodItem);

        // Use the sophisticated pricing model
        int newPrice = generateNextPrice(oldPrice, basePrice, volatility, trend, minPrice, maxPrice);

        // Apply market events (override normal pricing for major events)
        if (marketCrash) {
            // Market crash: 10-25% decrease
            double crashEffect = -0.10 - (random.nextDouble() * 0.15);
            newPrice = (int) Math.round(oldPrice * (1 + crashEffect));
            newPrice = Math.max(minPrice, newPrice); // Don't go below minimum

            Pokecobbleclaim.LOGGER.info("MARKET CRASH: {} price: {} -> {} ({:.1f}%)",
                foodItem, oldPrice, newPrice, crashEffect * 100);

        } else if (marketBoom) {
            // Market boom: 10-25% increase
            double boomEffect = 0.10 + (random.nextDouble() * 0.15);
            newPrice = (int) Math.round(oldPrice * (1 + boomEffect));
            newPrice = Math.min(maxPrice, newPrice); // Don't exceed maximum

            Pokecobbleclaim.LOGGER.info("MARKET BOOM: {} price: {} -> {} (+{:.1f}%)",
                foodItem, oldPrice, newPrice, boomEffect * 100);
        }

        // Ensure price actually changes (but allow small changes)
        if (newPrice == oldPrice) {
            // Force a minimal change within bounds
            if (oldPrice < maxPrice && random.nextBoolean()) {
                newPrice = oldPrice + 1;
            } else if (oldPrice > minPrice) {
                newPrice = oldPrice - 1;
            }
        }

        return newPrice;
    }

    /**
     * Validates current prices and fixes any that are outside economic bounds.
     */
    private void validateAndFixPrices() {
        boolean pricesFixed = false;

        for (String foodItem : FOOD_ITEMS) {
            int currentPrice = currentPrices.getOrDefault(foodItem, BASE_PRICES.get(foodItem));
            int minPrice = MIN_PRICES.get(foodItem);
            int maxPrice = MAX_PRICES.get(foodItem);

            if (currentPrice < minPrice) {
                currentPrices.put(foodItem, minPrice);
                pricesFixed = true;
                Pokecobbleclaim.LOGGER.warn("Fixed too-low price for {}: {} → {} coins",
                    foodItem, currentPrice, minPrice);
            } else if (currentPrice > maxPrice) {
                currentPrices.put(foodItem, maxPrice);
                pricesFixed = true;
                Pokecobbleclaim.LOGGER.warn("Fixed too-high price for {}: {} → {} coins",
                    foodItem, currentPrice, maxPrice);
            }
        }

        if (pricesFixed) {
            Pokecobbleclaim.LOGGER.info("Price validation completed - some prices were adjusted to economic bounds");
            savePriceData(); // Save the corrected prices
        }
    }

    /**
     * Updates prices if 24 hours have passed since last update.
     * Should be called periodically from server tick events.
     */
    public void checkForPriceUpdate() {
        long currentTime = System.currentTimeMillis();
        
        if (currentTime - lastPriceUpdate >= PRICE_UPDATE_INTERVAL) {
            updatePrices();
        }
    }
    
    /**
     * Forces a price update (for testing or admin commands).
     */
    public void forceUpdatePrices() {
        Pokecobbleclaim.LOGGER.info("=== FORCING PRICE UPDATE ===");

        // Log all food items
        Pokecobbleclaim.LOGGER.info("Food items in FOOD_ITEMS set: {}", FOOD_ITEMS);

        // Log current prices before update
        Pokecobbleclaim.LOGGER.info("Current prices before update:");
        for (Map.Entry<String, Integer> entry : currentPrices.entrySet()) {
            Pokecobbleclaim.LOGGER.info("  {}: {} coins", entry.getKey(), entry.getValue());
        }

        updatePrices();

        // Log new prices after update
        Pokecobbleclaim.LOGGER.info("New prices after update:");
        for (Map.Entry<String, Integer> entry : currentPrices.entrySet()) {
            Pokecobbleclaim.LOGGER.info("  {}: {} coins", entry.getKey(), entry.getValue());
        }

        Pokecobbleclaim.LOGGER.info("=== PRICE UPDATE COMPLETE ===");
    }

    /**
     * Public method to validate and fix prices (for admin commands).
     */
    public void validatePrices() {
        validateAndFixPrices();
    }
    
    /**
     * Updates all food prices and notifies clients.
     */
    private void updatePrices() {
        long currentTime = System.currentTimeMillis();

        // Check for market events (reduced frequency for economic stability)
        boolean marketCrash = random.nextDouble() < 0.02; // 2% chance for market crash
        boolean marketBoom = random.nextDouble() < 0.02; // 2% chance for market boom

        if (marketCrash) {
            Pokecobbleclaim.LOGGER.info("MARKET EVENT: Market crash! All prices dropping significantly!");
        } else if (marketBoom) {
            Pokecobbleclaim.LOGGER.info("MARKET EVENT: Market boom! All prices rising significantly!");
        }

        for (String foodItem : FOOD_ITEMS) {
            Pokecobbleclaim.LOGGER.debug("Updating prices for food item: {}", foodItem);

            // Check if all required data exists for this food item
            if (!BASE_PRICES.containsKey(foodItem)) {
                Pokecobbleclaim.LOGGER.warn("Skipping food item {} - no base price found", foodItem);
                continue;
            }
            if (!PRICE_VOLATILITY.containsKey(foodItem)) {
                Pokecobbleclaim.LOGGER.warn("Skipping food item {} - no volatility found", foodItem);
                continue;
            }
            if (!currentTrends.containsKey(foodItem)) {
                Pokecobbleclaim.LOGGER.warn("Skipping food item {} - no trend found", foodItem);
                continue;
            }

            int oldPrice = currentPrices.getOrDefault(foodItem, BASE_PRICES.get(foodItem));

            // Get current trend and evolve it with controlled changes
            double currentTrend = currentTrends.get(foodItem);

            // Apply trend decay and small random evolution
            double trendChange = (random.nextGaussian() * 0.05); // Smaller trend changes
            double newTrend = (currentTrend * TREND_DECAY) + trendChange;

            // Limit trend strength
            newTrend = Math.max(-MAX_TREND_STRENGTH, Math.min(MAX_TREND_STRENGTH, newTrend));
            currentTrends.put(foodItem, newTrend);

            // Generate new price with guaranteed change
            int basePrice = BASE_PRICES.get(foodItem);
            double volatility = PRICE_VOLATILITY.get(foodItem);
            int newPrice = generateNewPriceWithGuaranteedChange(oldPrice, basePrice, volatility, newTrend, marketCrash, marketBoom, foodItem);

            // Update current price
            currentPrices.put(foodItem, newPrice);

            // Add to price history
            List<FoodPriceData.PriceHistoryEntry> history = priceHistory.get(foodItem);
            history.add(new FoodPriceData.PriceHistoryEntry(currentTime, newPrice));

            // Keep only last 10 days of history
            while (history.size() > 10) {
                history.remove(0);
            }

            // Calculate price change percentage
            double changePercent = ((double)(newPrice - oldPrice) / oldPrice) * 100;
            String changeDirection = changePercent > 0 ? "↗" : "↘";

            Pokecobbleclaim.LOGGER.info("Updated price for {}: {} → {} coins ({}{:.1f}%) [trend: {:.2f}]",
                foodItem, oldPrice, newPrice, changeDirection, Math.abs(changePercent), newTrend);
        }
        
        lastPriceUpdate = currentTime;
        
        // Save updated prices
        savePriceData();

        // Notify all connected players with both sync and update packets
        if (server != null) {
            // Send full sync to all players
            FoodPriceSyncHandler.syncPricesToAllPlayers(server, currentPrices, priceHistory);

            // Also send price update notification
            FoodPriceSyncHandler.notifyPriceUpdate(server, currentPrices);

            Pokecobbleclaim.LOGGER.info("Synchronized price updates to {} players",
                server.getPlayerManager().getPlayerList().size());
        }
        
        Pokecobbleclaim.LOGGER.info("Updated all food prices at {}", 
            LocalDateTime.ofInstant(Instant.ofEpochMilli(currentTime), ZoneId.systemDefault()));
    }
    
    /**
     * Gets the current price for a food item.
     */
    public int getCurrentPrice(String foodItem) {
        return currentPrices.getOrDefault(foodItem, BASE_PRICES.getOrDefault(foodItem, 1));
    }
    
    /**
     * Gets all current prices.
     */
    public Map<String, Integer> getCurrentPrices() {
        return new HashMap<>(currentPrices);
    }
    
    /**
     * Gets price history for a food item.
     */
    public List<FoodPriceData.PriceHistoryEntry> getPriceHistory(String foodItem) {
        return new ArrayList<>(priceHistory.getOrDefault(foodItem, new ArrayList<>()));
    }
    
    /**
     * Gets all price history data.
     */
    public Map<String, List<FoodPriceData.PriceHistoryEntry>> getAllPriceHistory() {
        Map<String, List<FoodPriceData.PriceHistoryEntry>> result = new HashMap<>();
        for (Map.Entry<String, List<FoodPriceData.PriceHistoryEntry>> entry : priceHistory.entrySet()) {
            result.put(entry.getKey(), new ArrayList<>(entry.getValue()));
        }
        return result;
    }
    
    /**
     * Gets time remaining until next price update in milliseconds.
     */
    public long getTimeUntilNextUpdate() {
        long timeSinceLastUpdate = System.currentTimeMillis() - lastPriceUpdate;
        return Math.max(0, PRICE_UPDATE_INTERVAL - timeSinceLastUpdate);
    }
    
    /**
     * Gets the timestamp of the last price update.
     */
    public long getLastUpdateTime() {
        return lastPriceUpdate;
    }
    
    /**
     * Loads price data from storage.
     */
    private void loadPriceData() {
        try {
            FoodPriceData data = FoodPriceStorage.loadPriceData();
            if (data != null) {
                // Clear existing data before loading
                currentPrices.clear();
                priceHistory.clear();
                currentTrends.clear();

                // Load data from storage
                if (data.getCurrentPrices() != null) {
                    currentPrices.putAll(data.getCurrentPrices());
                }

                if (data.getPriceHistory() != null) {
                    priceHistory.putAll(data.getPriceHistory());
                }

                if (data.getCurrentTrends() != null) {
                    currentTrends.putAll(data.getCurrentTrends());
                }

                lastPriceUpdate = data.getLastUpdateTime();

                // Restore dynamically added food items to FOOD_ITEMS set
                // Any item in currentPrices that's not in the default set should be added
                for (String foodId : currentPrices.keySet()) {
                    if (!FOOD_ITEMS.contains(foodId)) {
                        FOOD_ITEMS.add(foodId);
                        Pokecobbleclaim.LOGGER.info("Restored dynamically added food item: {}", foodId);

                        // Reconstruct missing data for this food item
                        // Use current price as base price estimate
                        int currentPrice = currentPrices.get(foodId);
                        if (!BASE_PRICES.containsKey(foodId)) {
                            BASE_PRICES.put(foodId, currentPrice);
                        }

                        // Calculate volatility based on price tier
                        if (!PRICE_VOLATILITY.containsKey(foodId)) {
                            double volatility = calculateVolatilityForPrice(currentPrice);
                            PRICE_VOLATILITY.put(foodId, volatility);
                        }

                        // Calculate price bounds
                        int[] bounds = calculatePriceBounds(currentPrice);
                        if (!MIN_PRICES.containsKey(foodId) || !MAX_PRICES.containsKey(foodId)) {
                            MIN_PRICES.put(foodId, bounds[0]);
                            MAX_PRICES.put(foodId, bounds[1]);
                        }

                        Pokecobbleclaim.LOGGER.info("Reconstructed pricing data for {}: base={}, volatility={:.2f}, bounds=[{}, {}]",
                            foodId, currentPrice, PRICE_VOLATILITY.get(foodId), bounds[0], bounds[1]);
                    }
                }

                // Log detailed loading information
                Pokecobbleclaim.LOGGER.info("Loaded price data from storage:");
                for (String foodItem : FOOD_ITEMS) {
                    int price = currentPrices.getOrDefault(foodItem, BASE_PRICES.get(foodItem));
                    int historySize = priceHistory.getOrDefault(foodItem, new ArrayList<>()).size();
                    Pokecobbleclaim.LOGGER.info("  {}: {} coins, {} history entries",
                        foodItem, price, historySize);
                }

                // Ensure all food items have entries (fill missing ones)
                ensureAllFoodItemsInitialized();

            } else {
                Pokecobbleclaim.LOGGER.info("No existing price data found, will generate initial data");
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load price data: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Ensures all food items have proper entries in the maps.
     */
    private void ensureAllFoodItemsInitialized() {
        for (String foodItem : FOOD_ITEMS) {
            // Ensure current price exists
            if (!currentPrices.containsKey(foodItem)) {
                currentPrices.put(foodItem, BASE_PRICES.get(foodItem));
                Pokecobbleclaim.LOGGER.warn("Missing current price for {}, using base price", foodItem);
            }

            // Ensure price history exists
            if (!priceHistory.containsKey(foodItem)) {
                priceHistory.put(foodItem, new ArrayList<>());
                Pokecobbleclaim.LOGGER.warn("Missing price history for {}, initialized empty list", foodItem);
            }

            // Ensure trend exists
            if (!currentTrends.containsKey(foodItem)) {
                currentTrends.put(foodItem, 0.0);
                Pokecobbleclaim.LOGGER.warn("Missing trend for {}, initialized to neutral", foodItem);
            }
        }
    }

    /**
     * Saves current price data to storage.
     */
    private void savePriceData() {
        try {
            FoodPriceData data = new FoodPriceData(currentPrices, priceHistory, currentTrends, lastPriceUpdate);
            FoodPriceStorage.savePriceData(data);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to save price data: " + e.getMessage());
        }
    }
    
    /**
     * Synchronizes prices to a specific player.
     */
    public void syncPricesToPlayer(ServerPlayerEntity player) {
        if (server != null) {
            Pokecobbleclaim.LOGGER.debug("Syncing prices to player {}: {} current prices, {} history entries total",
                player.getName().getString(),
                currentPrices.size(),
                priceHistory.values().stream().mapToInt(List::size).sum());

            FoodPriceSyncHandler.syncPricesToPlayer(player, currentPrices, priceHistory);
        }
    }

    /**
     * Forces a full sync of all price data to all connected players.
     */
    public void syncToAllPlayers() {
        if (server != null) {
            for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
                syncPricesToPlayer(player);
            }
            Pokecobbleclaim.LOGGER.info("Synced price data to all {} connected players",
                server.getPlayerManager().getPlayerList().size());
        }
    }

    /**
     * Gets list of all food items with dynamic pricing.
     */
    public static List<String> getFoodItems() {
        return new ArrayList<>(FOOD_ITEMS);
    }

    /**
     * Adds a new food item to the dynamic pricing system.
     * This is called when new food products are created.
     */
    public void addNewFoodItem(String foodId, int basePrice) {
        try {
            // Add to the food items set
            FOOD_ITEMS.add(foodId);

            // Set base price and pricing parameters
            BASE_PRICES.put(foodId, basePrice);

            // Calculate volatility based on price tier - higher prices = more volatile
            double volatility = calculateVolatilityForPrice(basePrice);
            PRICE_VOLATILITY.put(foodId, volatility);

            // Set min/max prices with smart bounds based on price tier
            int[] bounds = calculatePriceBounds(basePrice);
            MIN_PRICES.put(foodId, bounds[0]);
            MAX_PRICES.put(foodId, bounds[1]);

            // Initialize current price and history
            currentPrices.put(foodId, basePrice);
            priceHistory.put(foodId, new ArrayList<>());
            currentTrends.put(foodId, 0.0);

            // Generate some initial price history (last 3 days)
            generateInitialHistoryForNewItem(foodId, basePrice);

            // Save the updated data
            savePriceData();

            // Sync to all players
            if (server != null) {
                syncToAllPlayers();
            }

            Pokecobbleclaim.LOGGER.info("Added new food item to dynamic pricing: {} (base price: {}, volatility: {:.2f})",
                foodId, basePrice, volatility);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to add new food item to pricing system: " + foodId, e);
        }
    }

    /**
     * Calculates volatility based on price tier.
     * Higher-priced items have more volatility but with diminishing returns.
     */
    private double calculateVolatilityForPrice(int basePrice) {
        if (basePrice <= 5) {
            // Low-tier items: minimal volatility (5-10%)
            return 0.05 + (random.nextDouble() * 0.05);
        } else if (basePrice <= 15) {
            // Mid-tier items: moderate volatility (10-20%)
            return 0.10 + (random.nextDouble() * 0.10);
        } else if (basePrice <= 30) {
            // High-tier items: higher volatility (15-30%)
            return 0.15 + (random.nextDouble() * 0.15);
        } else if (basePrice <= 50) {
            // Premium items: significant volatility (20-40%)
            return 0.20 + (random.nextDouble() * 0.20);
        } else {
            // Luxury items: high volatility (25-50%) but capped
            return 0.25 + (random.nextDouble() * 0.25);
        }
    }

    /**
     * Calculates price bounds based on base price.
     * Higher prices get wider bounds but with smart limits.
     */
    private int[] calculatePriceBounds(int basePrice) {
        int minPrice, maxPrice;

        if (basePrice <= 5) {
            // Low-tier: 70%-150% range (stay close to base)
            minPrice = Math.max(1, (int)(basePrice * 0.7));
            maxPrice = (int)(basePrice * 1.5);
        } else if (basePrice <= 15) {
            // Mid-tier: 60%-180% range
            minPrice = Math.max(1, (int)(basePrice * 0.6));
            maxPrice = (int)(basePrice * 1.8);
        } else if (basePrice <= 30) {
            // High-tier: 50%-220% range
            minPrice = Math.max(1, (int)(basePrice * 0.5));
            maxPrice = (int)(basePrice * 2.2);
        } else if (basePrice <= 50) {
            // Premium: 40%-250% range
            minPrice = Math.max(1, (int)(basePrice * 0.4));
            maxPrice = (int)(basePrice * 2.5);
        } else {
            // Luxury: 30%-300% range (can spike high but rarely)
            minPrice = Math.max(1, (int)(basePrice * 0.3));
            maxPrice = (int)(basePrice * 3.0);
        }

        return new int[]{minPrice, maxPrice};
    }

    /**
     * Generates initial price history for a newly added food item.
     * Uses sophisticated pricing model with rare high spikes.
     */
    private void generateInitialHistoryForNewItem(String foodId, int basePrice) {
        long currentTime = System.currentTimeMillis();
        List<FoodPriceData.PriceHistoryEntry> history = new ArrayList<>();

        double volatility = PRICE_VOLATILITY.get(foodId);
        double trend = (random.nextDouble() - 0.5) * 0.3; // Smaller initial trend
        currentTrends.put(foodId, trend);

        int previousPrice = basePrice;
        int minPrice = MIN_PRICES.get(foodId);
        int maxPrice = MAX_PRICES.get(foodId);

        // Generate 3 days of history with sophisticated pricing
        for (int day = 2; day >= 0; day--) {
            long timestamp = currentTime - (day * 24L * 60L * 60L * 1000L);

            if (day == 2) {
                // Start close to base price with small variation
                double variation = (random.nextGaussian() * volatility * 0.15);
                previousPrice = Math.max(minPrice, Math.min(maxPrice,
                    (int) Math.round(basePrice * (1 + variation))));
            } else {
                // Generate realistic price progression with rare spikes
                previousPrice = generateNextPrice(previousPrice, basePrice, volatility, trend, minPrice, maxPrice);
            }

            history.add(new FoodPriceData.PriceHistoryEntry(timestamp, previousPrice));
        }

        priceHistory.put(foodId, history);

        // Set current price to the most recent price
        if (!history.isEmpty()) {
            currentPrices.put(foodId, history.get(history.size() - 1).getPrice());
        }
    }

    /**
     * Generates the next price using sophisticated market simulation.
     * Implements "gravitational pull" toward base price with rare high spikes.
     */
    private int generateNextPrice(int currentPrice, int basePrice, double volatility,
                                 double trend, int minPrice, int maxPrice) {

        // Calculate distance from base price (normalized)
        double distanceFromBase = (double)(currentPrice - basePrice) / basePrice;

        // Gravitational pull toward base price (stronger when further away)
        double gravityPull = -distanceFromBase * 0.3;

        // Base market movement (normal volatility)
        double marketMovement = random.nextGaussian() * volatility * 0.4;

        // Trend component (smaller influence)
        double trendComponent = trend * 0.02;

        // Rare spike chance (higher for expensive items, but still rare)
        double spikeChance = Math.min(0.05, basePrice * 0.001); // Max 5% chance
        boolean hasSpike = random.nextDouble() < spikeChance;

        double totalChange;

        if (hasSpike) {
            // Rare spike: can go significantly higher but usually not to max
            boolean isPositiveSpike = random.nextDouble() < 0.7; // 70% chance positive spike

            if (isPositiveSpike) {
                // Positive spike: 20-60% increase (rare)
                double spikeIntensity = 0.2 + (random.nextDouble() * 0.4);
                totalChange = spikeIntensity;

                Pokecobbleclaim.LOGGER.debug("Price spike for item (base: {}): +{:.1f}%",
                    basePrice, spikeIntensity * 100);
            } else {
                // Negative spike: 15-40% decrease (market crash)
                double crashIntensity = -(0.15 + (random.nextDouble() * 0.25));
                totalChange = crashIntensity;

                Pokecobbleclaim.LOGGER.debug("Price crash for item (base: {}): {:.1f}%",
                    basePrice, crashIntensity * 100);
            }
        } else {
            // Normal price movement with gravity toward base
            totalChange = marketMovement + trendComponent + gravityPull;

            // Dampen extreme movements for normal changes
            totalChange = Math.max(-0.15, Math.min(0.15, totalChange));
        }

        // Apply the change
        int newPrice = (int) Math.round(currentPrice * (1 + totalChange));

        // Enforce bounds but allow occasional brief excursions
        if (hasSpike) {
            // Spikes can briefly exceed normal bounds but not by too much
            int absoluteMin = Math.max(1, minPrice / 2);
            int absoluteMax = Math.min(maxPrice * 2, basePrice * 4); // Cap extreme spikes
            newPrice = Math.max(absoluteMin, Math.min(absoluteMax, newPrice));
        } else {
            // Normal movements stay within regular bounds
            newPrice = Math.max(minPrice, Math.min(maxPrice, newPrice));
        }

        return newPrice;
    }
    
    /**
     * Shuts down the price manager and saves data.
     */
    public void shutdown() {
        savePriceData();
        Pokecobbleclaim.LOGGER.info("FoodPriceManager shut down successfully");
    }
}
