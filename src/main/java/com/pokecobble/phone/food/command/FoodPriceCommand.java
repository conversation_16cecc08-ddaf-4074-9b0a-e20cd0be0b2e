package com.pokecobble.phone.food.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.food.FoodPriceManager;
import com.pokecobble.phone.food.FoodPriceTimer;
import com.pokecobble.phone.food.test.FoodPriceSystemTest;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.text.Text;

import java.util.Map;

/**
 * Command for managing and testing the food price system.
 */
public class FoodPriceCommand {
    
    /**
     * Registers the food price command.
     */
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher, CommandRegistryAccess registryAccess) {
        dispatcher.register(CommandManager.literal("foodprice")
            .requires(source -> source.hasPermissionLevel(2)) // Require OP level
            .then(CommandManager.literal("status")
                .executes(FoodPriceCommand::showStatus))
            .then(CommandManager.literal("update")
                .executes(FoodPriceCommand::forceUpdate))
            .then(CommandManager.literal("test")
                .executes(FoodPriceCommand::runTests))
            .then(CommandManager.literal("timer")
                .executes(FoodPriceCommand::showTimer))
            .then(CommandManager.literal("price")
                .then(CommandManager.argument("item", StringArgumentType.string())
                    .executes(FoodPriceCommand::showItemPrice)))
            .then(CommandManager.literal("reset")
                .executes(FoodPriceCommand::resetTimer))
            .then(CommandManager.literal("testgen")
                .executes(FoodPriceCommand::testPriceGeneration))
            .then(CommandManager.literal("sync")
                .executes(FoodPriceCommand::forceSyncTimer))
            .then(CommandManager.literal("validate")
                .executes(FoodPriceCommand::validatePrices))
            .executes(FoodPriceCommand::showHelp));
    }
    
    /**
     * Shows help information for the command.
     */
    private static int showHelp(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        
        source.sendFeedback(() -> Text.literal("§6=== Food Price System Commands ==="), false);
        source.sendFeedback(() -> Text.literal("§e/foodprice status §7- Show current system status"), false);
        source.sendFeedback(() -> Text.literal("§e/foodprice update §7- Force price update"), false);
        source.sendFeedback(() -> Text.literal("§e/foodprice test §7- Run system tests"), false);
        source.sendFeedback(() -> Text.literal("§e/foodprice timer §7- Show timer information"), false);
        source.sendFeedback(() -> Text.literal("§e/foodprice price <item> §7- Show price for specific item"), false);
        source.sendFeedback(() -> Text.literal("§e/foodprice reset §7- Reset timer (for testing)"), false);
        source.sendFeedback(() -> Text.literal("§e/foodprice testgen §7- Test price generation multiple times"), false);
        source.sendFeedback(() -> Text.literal("§e/foodprice sync §7- Force sync timer to all players"), false);
        source.sendFeedback(() -> Text.literal("§e/foodprice validate §7- Check and fix extreme prices"), false);
        
        return 1;
    }
    
    /**
     * Shows the current system status.
     */
    private static int showStatus(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        
        try {
            FoodPriceManager manager = FoodPriceManager.getInstance();
            FoodPriceTimer timer = FoodPriceTimer.getInstance();
            
            source.sendFeedback(() -> Text.literal("§6=== Food Price System Status ==="), false);
            
            // Show current prices
            Map<String, Integer> prices = manager.getCurrentPrices();
            source.sendFeedback(() -> Text.literal("§eCurrent Prices:"), false);
            for (Map.Entry<String, Integer> entry : prices.entrySet()) {
                String item = entry.getKey();
                int price = entry.getValue();
                source.sendFeedback(() -> Text.literal("  §f" + item + ": §a" + price + " coins"), false);
            }
            
            // Show timer info
            String timeUntilNext = timer.getFormattedTimeUntilNextUpdate();
            source.sendFeedback(() -> Text.literal("§eNext Update: §f" + timeUntilNext), false);
            
            // Show last update time
            long lastUpdate = manager.getLastUpdateTime();
            if (lastUpdate > 0) {
                java.time.LocalDateTime lastUpdateTime = java.time.LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(lastUpdate), 
                    java.time.ZoneId.systemDefault()
                );
                source.sendFeedback(() -> Text.literal("§eLast Update: §f" + lastUpdateTime.toString()), false);
            }
            
        } catch (Exception e) {
            source.sendError(Text.literal("§cError getting system status: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in foodprice status command: " + e.getMessage());
        }
        
        return 1;
    }
    
    /**
     * Forces a price update.
     */
    private static int forceUpdate(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        
        try {
            source.sendFeedback(() -> Text.literal("§eForcing price update..."), false);
            
            FoodPriceManager manager = FoodPriceManager.getInstance();
            manager.forceUpdatePrices();
            
            source.sendFeedback(() -> Text.literal("§aPrice update completed successfully!"), false);
            
        } catch (Exception e) {
            source.sendError(Text.literal("§cError forcing price update: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in foodprice update command: " + e.getMessage());
        }
        
        return 1;
    }
    
    /**
     * Runs system tests.
     */
    private static int runTests(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        
        try {
            source.sendFeedback(() -> Text.literal("§eRunning food price system tests..."), false);
            
            boolean testsPassed = FoodPriceSystemTest.runAllTests();
            
            if (testsPassed) {
                source.sendFeedback(() -> Text.literal("§aAll tests passed successfully!"), false);
            } else {
                source.sendError(Text.literal("§cSome tests failed. Check server logs for details."));
            }
            
        } catch (Exception e) {
            source.sendError(Text.literal("§cError running tests: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in foodprice test command: " + e.getMessage());
        }
        
        return 1;
    }
    
    /**
     * Shows timer information.
     */
    private static int showTimer(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        
        try {
            FoodPriceTimer timer = FoodPriceTimer.getInstance();
            
            source.sendFeedback(() -> Text.literal("§6=== Price Update Timer ==="), false);
            
            String timeUntilNext = timer.getFormattedTimeUntilNextUpdate();
            source.sendFeedback(() -> Text.literal("§eTime Until Next Update: §f" + timeUntilNext), false);
            
            long seconds = timer.getSecondsUntilNextUpdate();
            source.sendFeedback(() -> Text.literal("§eSeconds Remaining: §f" + seconds), false);
            
            java.time.LocalDateTime nextUpdate = timer.getNextUpdateDateTime();
            source.sendFeedback(() -> Text.literal("§eNext Update Time: §f" + nextUpdate.toString()), false);
            
            String debugInfo = timer.getDebugInfo();
            source.sendFeedback(() -> Text.literal("§7Debug: " + debugInfo), false);
            
        } catch (Exception e) {
            source.sendError(Text.literal("§cError getting timer info: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in foodprice timer command: " + e.getMessage());
        }
        
        return 1;
    }
    
    /**
     * Shows price for a specific item.
     */
    private static int showItemPrice(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        String itemName = StringArgumentType.getString(context, "item");
        
        try {
            FoodPriceManager manager = FoodPriceManager.getInstance();
            
            int currentPrice = manager.getCurrentPrice(itemName);
            if (currentPrice <= 0) {
                source.sendError(Text.literal("§cItem '" + itemName + "' not found or has no price"));
                return 0;
            }
            
            source.sendFeedback(() -> Text.literal("§6=== Price Info for " + itemName + " ==="), false);
            source.sendFeedback(() -> Text.literal("§eCurrent Price: §a" + currentPrice + " coins"), false);
            
            // Show price history
            var history = manager.getPriceHistory(itemName);
            if (!history.isEmpty()) {
                source.sendFeedback(() -> Text.literal("§ePrice History (last " + history.size() + " days):"), false);
                for (int i = Math.max(0, history.size() - 5); i < history.size(); i++) {
                    var entry = history.get(i);
                    long daysAgo = (System.currentTimeMillis() - entry.getTimestamp()) / (24L * 60L * 60L * 1000L);
                    source.sendFeedback(() -> Text.literal("  §f" + daysAgo + " days ago: §a" + entry.getPrice() + " coins"), false);
                }
            }
            
        } catch (Exception e) {
            source.sendError(Text.literal("§cError getting item price: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in foodprice price command: " + e.getMessage());
        }
        
        return 1;
    }
    
    /**
     * Resets the timer (for testing).
     */
    private static int resetTimer(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        
        try {
            source.sendFeedback(() -> Text.literal("§eResetting price update timer..."), false);
            
            FoodPriceTimer timer = FoodPriceTimer.getInstance();
            timer.resetTimer();
            
            source.sendFeedback(() -> Text.literal("§aTimer reset successfully! Next update in 24 hours."), false);
            
        } catch (Exception e) {
            source.sendError(Text.literal("§cError resetting timer: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in foodprice reset command: " + e.getMessage());
        }
        
        return 1;
    }

    /**
     * Tests price generation multiple times to show variation.
     */
    private static int testPriceGeneration(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        try {
            source.sendFeedback(() -> Text.literal("§eRunning price generation test (5 iterations)..."), false);

            FoodPriceManager manager = FoodPriceManager.getInstance();

            for (int i = 1; i <= 5; i++) {
                final int iteration = i;
                source.sendFeedback(() -> Text.literal("§6=== Iteration " + iteration + " ==="), false);

                // Show current prices
                Map<String, Integer> currentPrices = manager.getCurrentPrices();
                source.sendFeedback(() -> Text.literal("§eBefore update:"), false);
                for (Map.Entry<String, Integer> entry : currentPrices.entrySet()) {
                    source.sendFeedback(() -> Text.literal("  §f" + entry.getKey() + ": §a" + entry.getValue() + " coins"), false);
                }

                // Force update
                manager.forceUpdatePrices();

                // Show new prices
                Map<String, Integer> newPrices = manager.getCurrentPrices();
                source.sendFeedback(() -> Text.literal("§eAfter update:"), false);
                for (Map.Entry<String, Integer> entry : newPrices.entrySet()) {
                    String item = entry.getKey();
                    int oldPrice = currentPrices.get(item);
                    int newPrice = entry.getValue();
                    double changePercent = ((double)(newPrice - oldPrice) / oldPrice) * 100;
                    String changeColor = changePercent > 0 ? "§a" : "§c";
                    String changeSymbol = changePercent > 0 ? "↗" : "↘";

                    source.sendFeedback(() -> Text.literal("  §f" + item + ": " + changeColor + newPrice +
                        " coins " + changeSymbol + " (" + String.format("%.1f", changePercent) + "%)"), false);
                }

                // Small delay between iterations
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            source.sendFeedback(() -> Text.literal("§aPrice generation test completed!"), false);

        } catch (Exception e) {
            source.sendError(Text.literal("§cError testing price generation: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in foodprice testgen command: " + e.getMessage());
        }

        return 1;
    }

    /**
     * Forces timer sync to all players.
     */
    private static int forceSyncTimer(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        try {
            source.sendFeedback(() -> Text.literal("§eForcing timer sync to all players..."), false);

            FoodPriceTimer timer = FoodPriceTimer.getInstance();
            var timerData = timer.getTimerData();

            if (timerData != null) {
                com.pokecobble.phone.network.FoodPriceTimerSyncHandler.syncTimerToAllPlayers(
                    source.getServer(), timerData);

                source.sendFeedback(() -> Text.literal("§aTimer sync completed successfully!"), false);
                source.sendFeedback(() -> Text.literal("§eNext update: §f" + timerData.getFormattedTimeUntilNextUpdate()), false);
            } else {
                source.sendError(Text.literal("§cNo timer data available"));
            }

        } catch (Exception e) {
            source.sendError(Text.literal("§cError forcing timer sync: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in foodprice sync command: " + e.getMessage());
        }

        return 1;
    }

    /**
     * Validates and fixes extreme prices.
     */
    private static int validatePrices(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        try {
            source.sendFeedback(() -> Text.literal("§eValidating food prices..."), false);

            FoodPriceManager manager = FoodPriceManager.getInstance();

            // Show current prices before validation
            Map<String, Integer> pricesBefore = manager.getCurrentPrices();
            source.sendFeedback(() -> Text.literal("§ePrices before validation:"), false);
            for (Map.Entry<String, Integer> entry : pricesBefore.entrySet()) {
                source.sendFeedback(() -> Text.literal("  §f" + entry.getKey() + ": §a" + entry.getValue() + " coins"), false);
            }

            // Validate prices
            manager.validatePrices();

            // Show prices after validation
            Map<String, Integer> pricesAfter = manager.getCurrentPrices();
            source.sendFeedback(() -> Text.literal("§ePrices after validation:"), false);

            boolean anyChanges = false;
            for (Map.Entry<String, Integer> entry : pricesAfter.entrySet()) {
                String item = entry.getKey();
                int oldPrice = pricesBefore.get(item);
                int newPrice = entry.getValue();

                if (oldPrice != newPrice) {
                    anyChanges = true;
                    source.sendFeedback(() -> Text.literal("  §f" + item + ": §c" + oldPrice + " §7→ §a" + newPrice + " coins §e(FIXED)"), false);
                } else {
                    source.sendFeedback(() -> Text.literal("  §f" + item + ": §a" + newPrice + " coins §7(OK)"), false);
                }
            }

            if (anyChanges) {
                source.sendFeedback(() -> Text.literal("§aPrice validation completed - some prices were adjusted!"), false);
            } else {
                source.sendFeedback(() -> Text.literal("§aPrice validation completed - all prices are within bounds!"), false);
            }

        } catch (Exception e) {
            source.sendError(Text.literal("§cError validating prices: " + e.getMessage()));
            Pokecobbleclaim.LOGGER.error("Error in foodprice validate command: " + e.getMessage());
        }

        return 1;
    }
}
