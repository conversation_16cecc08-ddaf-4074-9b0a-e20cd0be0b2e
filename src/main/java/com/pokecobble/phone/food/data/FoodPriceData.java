package com.pokecobble.phone.food.data;

import java.util.List;
import java.util.Map;

/**
 * Data class for serializing and deserializing food price information.
 * Contains current prices, price history, trends, and metadata.
 */
public class FoodPriceData {
    private Map<String, Integer> currentPrices;
    private Map<String, List<PriceHistoryEntry>> priceHistory;
    private Map<String, Double> currentTrends;
    private long lastUpdateTime;
    private long dataVersion;
    
    // Default constructor for JSON deserialization
    public FoodPriceData() {}
    
    public FoodPriceData(Map<String, Integer> currentPrices, 
                        Map<String, List<PriceHistoryEntry>> priceHistory,
                        Map<String, Double> currentTrends,
                        long lastUpdateTime) {
        this.currentPrices = currentPrices;
        this.priceHistory = priceHistory;
        this.currentTrends = currentTrends;
        this.lastUpdateTime = lastUpdateTime;
        this.dataVersion = System.currentTimeMillis();
    }
    
    // Getters
    public Map<String, Integer> getCurrentPrices() {
        return currentPrices;
    }
    
    public Map<String, List<PriceHistoryEntry>> getPriceHistory() {
        return priceHistory;
    }
    
    public Map<String, Double> getCurrentTrends() {
        return currentTrends;
    }
    
    public long getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    public long getDataVersion() {
        return dataVersion;
    }
    
    // Setters
    public void setCurrentPrices(Map<String, Integer> currentPrices) {
        this.currentPrices = currentPrices;
    }
    
    public void setPriceHistory(Map<String, List<PriceHistoryEntry>> priceHistory) {
        this.priceHistory = priceHistory;
    }
    
    public void setCurrentTrends(Map<String, Double> currentTrends) {
        this.currentTrends = currentTrends;
    }
    
    public void setLastUpdateTime(long lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
    
    public void setDataVersion(long dataVersion) {
        this.dataVersion = dataVersion;
    }
    
    /**
     * Represents a single price history entry.
     */
    public static class PriceHistoryEntry {
        private long timestamp;
        private int price;
        
        // Default constructor for JSON deserialization
        public PriceHistoryEntry() {}
        
        public PriceHistoryEntry(long timestamp, int price) {
            this.timestamp = timestamp;
            this.price = price;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
        
        public int getPrice() {
            return price;
        }
        
        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }
        
        public void setPrice(int price) {
            this.price = price;
        }
        
        @Override
        public String toString() {
            return "PriceHistoryEntry{timestamp=" + timestamp + ", price=" + price + "}";
        }
    }
    
    @Override
    public String toString() {
        return "FoodPriceData{" +
                "currentPrices=" + currentPrices +
                ", priceHistorySize=" + (priceHistory != null ? priceHistory.size() : 0) +
                ", currentTrends=" + currentTrends +
                ", lastUpdateTime=" + lastUpdateTime +
                ", dataVersion=" + dataVersion +
                '}';
    }
}
