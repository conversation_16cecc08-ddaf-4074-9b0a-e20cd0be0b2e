package com.pokecobble.phone.food.data;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.loader.api.FabricLoader;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * Handles persistent storage of food price data with backup and compression support.
 */
public class FoodPriceStorage {
    private static final String FOOD_PRICE_DIR = "food_prices";
    private static final String PRICE_DATA_FILE = "price_data.json.gz";
    private static final String TIMER_DATA_FILE = "timer_data.json.gz";
    private static final String BACKUP_DIR = "backups";
    private static final String BACKUP_PREFIX = "price_data_backup_";
    private static final String TIMER_BACKUP_PREFIX = "timer_data_backup_";
    private static final String BACKUP_EXTENSION = ".json.gz";
    
    private static final Gson GSON = new GsonBuilder()
            .setPrettyPrinting()
            .create();
    
    /**
     * Gets the food price data directory.
     */
    private static Path getFoodPriceDirectory() {
        Path gameDir = FabricLoader.getInstance().getGameDir();
        return gameDir.resolve("pokecobbleclaim").resolve(FOOD_PRICE_DIR);
    }
    
    /**
     * Gets the backup directory.
     */
    private static Path getBackupDirectory() {
        return getFoodPriceDirectory().resolve(BACKUP_DIR);
    }
    
    /**
     * Ensures the food price directory exists.
     */
    private static void ensureDirectoryExists(Path directory) throws IOException {
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
        }
    }
    
    /**
     * Saves food price data to disk with compression.
     */
    public static void savePriceData(FoodPriceData priceData) throws IOException {
        Path priceDir = getFoodPriceDirectory();
        ensureDirectoryExists(priceDir);
        
        Path priceFile = priceDir.resolve(PRICE_DATA_FILE);
        Path tempFile = priceDir.resolve(PRICE_DATA_FILE + ".tmp");
        
        try {
            // Write to temporary file first for atomic operation
            try (FileOutputStream fos = new FileOutputStream(tempFile.toFile());
                 GZIPOutputStream gzos = new GZIPOutputStream(fos);
                 OutputStreamWriter writer = new OutputStreamWriter(gzos, "UTF-8")) {
                
                GSON.toJson(priceData, writer);
            }
            
            // Atomic move from temp to final location
            Files.move(tempFile, priceFile, StandardCopyOption.REPLACE_EXISTING);
            
            Pokecobbleclaim.LOGGER.debug("Saved food price data to {}", priceFile);
            
        } catch (IOException e) {
            // Clean up temp file if it exists
            try {
                Files.deleteIfExists(tempFile);
            } catch (IOException cleanupException) {
                Pokecobbleclaim.LOGGER.warn("Failed to clean up temp file: " + cleanupException.getMessage());
            }
            throw e;
        }
    }
    
    /**
     * Loads food price data from disk.
     */
    public static FoodPriceData loadPriceData() throws IOException {
        Path priceDir = getFoodPriceDirectory();
        Path priceFile = priceDir.resolve(PRICE_DATA_FILE);
        
        if (!Files.exists(priceFile)) {
            Pokecobbleclaim.LOGGER.debug("No existing price data file found");
            return null;
        }
        
        try (FileInputStream fis = new FileInputStream(priceFile.toFile());
             GZIPInputStream gzis = new GZIPInputStream(fis);
             InputStreamReader reader = new InputStreamReader(gzis, "UTF-8")) {
            
            FoodPriceData data = GSON.fromJson(reader, FoodPriceData.class);
            
            if (data == null) {
                Pokecobbleclaim.LOGGER.warn("Loaded null price data from file");
                return null;
            }
            
            // Validate loaded data
            validatePriceData(data);
            
            Pokecobbleclaim.LOGGER.debug("Loaded food price data from {}", priceFile);
            return data;
            
        } catch (JsonSyntaxException e) {
            Pokecobbleclaim.LOGGER.error("Corrupted price data file, attempting to restore from backup: " + e.getMessage());
            return restoreFromBackup();
        }
    }
    
    /**
     * Validates loaded price data for consistency.
     */
    private static void validatePriceData(FoodPriceData data) {
        if (data.getCurrentPrices() == null) {
            throw new IllegalStateException("Current prices data is null");
        }
        
        if (data.getPriceHistory() == null) {
            throw new IllegalStateException("Price history data is null");
        }
        
        if (data.getCurrentTrends() == null) {
            throw new IllegalStateException("Current trends data is null");
        }
        
        if (data.getLastUpdateTime() <= 0) {
            throw new IllegalStateException("Invalid last update time: " + data.getLastUpdateTime());
        }
        
        // Validate price history entries
        for (var entry : data.getPriceHistory().entrySet()) {
            String foodItem = entry.getKey();
            var history = entry.getValue();
            
            if (history == null) {
                throw new IllegalStateException("Price history for " + foodItem + " is null");
            }
            
            for (var historyEntry : history) {
                if (historyEntry.getTimestamp() <= 0) {
                    throw new IllegalStateException("Invalid timestamp in price history for " + foodItem);
                }
                if (historyEntry.getPrice() <= 0) {
                    throw new IllegalStateException("Invalid price in price history for " + foodItem);
                }
            }
        }
    }
    
    /**
     * Creates a backup of the current price data.
     */
    public static void createBackup() throws IOException {
        Path priceDir = getFoodPriceDirectory();
        Path priceFile = priceDir.resolve(PRICE_DATA_FILE);
        
        if (!Files.exists(priceFile)) {
            Pokecobbleclaim.LOGGER.debug("No price data file to backup");
            return;
        }
        
        Path backupDir = getBackupDirectory();
        ensureDirectoryExists(backupDir);
        
        // Create backup filename with timestamp
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
        String backupFileName = BACKUP_PREFIX + timestamp + BACKUP_EXTENSION;
        Path backupFile = backupDir.resolve(backupFileName);
        
        // Copy current data file to backup
        Files.copy(priceFile, backupFile, StandardCopyOption.REPLACE_EXISTING);
        
        Pokecobbleclaim.LOGGER.info("Created price data backup: {}", backupFileName);
        
        // Clean up old backups (keep only last 10)
        cleanupOldBackups(10);
    }
    
    /**
     * Restores price data from the most recent backup.
     */
    private static FoodPriceData restoreFromBackup() throws IOException {
        Path backupDir = getBackupDirectory();
        
        if (!Files.exists(backupDir)) {
            Pokecobbleclaim.LOGGER.warn("No backup directory found");
            return null;
        }
        
        // Find the most recent backup file
        Path mostRecentBackup = Files.list(backupDir)
                .filter(path -> path.getFileName().toString().startsWith(BACKUP_PREFIX))
                .filter(path -> path.getFileName().toString().endsWith(BACKUP_EXTENSION))
                .max((p1, p2) -> p1.getFileName().toString().compareTo(p2.getFileName().toString()))
                .orElse(null);
        
        if (mostRecentBackup == null) {
            Pokecobbleclaim.LOGGER.warn("No backup files found");
            return null;
        }
        
        try (FileInputStream fis = new FileInputStream(mostRecentBackup.toFile());
             GZIPInputStream gzis = new GZIPInputStream(fis);
             InputStreamReader reader = new InputStreamReader(gzis, "UTF-8")) {
            
            FoodPriceData data = GSON.fromJson(reader, FoodPriceData.class);
            validatePriceData(data);
            
            Pokecobbleclaim.LOGGER.info("Restored price data from backup: {}", mostRecentBackup.getFileName());
            return data;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to restore from backup {}: {}", 
                mostRecentBackup.getFileName(), e.getMessage());
            return null;
        }
    }
    
    /**
     * Cleans up old backup files, keeping only the specified number of most recent backups.
     */
    private static void cleanupOldBackups(int keepCount) throws IOException {
        Path backupDir = getBackupDirectory();
        
        if (!Files.exists(backupDir)) {
            return;
        }
        
        var backupFiles = Files.list(backupDir)
                .filter(path -> path.getFileName().toString().startsWith(BACKUP_PREFIX))
                .filter(path -> path.getFileName().toString().endsWith(BACKUP_EXTENSION))
                .sorted((p1, p2) -> p2.getFileName().toString().compareTo(p1.getFileName().toString())) // Newest first
                .toList();
        
        if (backupFiles.size() <= keepCount) {
            return; // Nothing to clean up
        }
        
        // Delete old backups beyond the keep count
        for (int i = keepCount; i < backupFiles.size(); i++) {
            Path oldBackup = backupFiles.get(i);
            try {
                Files.delete(oldBackup);
                Pokecobbleclaim.LOGGER.debug("Deleted old backup: {}", oldBackup.getFileName());
            } catch (IOException e) {
                Pokecobbleclaim.LOGGER.warn("Failed to delete old backup {}: {}", 
                    oldBackup.getFileName(), e.getMessage());
            }
        }
        
        int deletedCount = backupFiles.size() - keepCount;
        if (deletedCount > 0) {
            Pokecobbleclaim.LOGGER.info("Cleaned up {} old price data backups", deletedCount);
        }
    }
    
    /**
     * Saves timer data to disk with compression.
     */
    public static void saveTimerData(FoodPriceTimerData timerData) throws IOException {
        Path priceDir = getFoodPriceDirectory();
        ensureDirectoryExists(priceDir);

        Path timerFile = priceDir.resolve(TIMER_DATA_FILE);
        Path tempFile = priceDir.resolve(TIMER_DATA_FILE + ".tmp");

        try {
            // Write to temporary file first for atomic operation
            try (FileOutputStream fos = new FileOutputStream(tempFile.toFile());
                 GZIPOutputStream gzos = new GZIPOutputStream(fos);
                 OutputStreamWriter writer = new OutputStreamWriter(gzos, "UTF-8")) {

                GSON.toJson(timerData, writer);
            }

            // Atomic move from temp to final location
            Files.move(tempFile, timerFile, StandardCopyOption.REPLACE_EXISTING);

            Pokecobbleclaim.LOGGER.debug("Saved timer data to {}", timerFile);

        } catch (IOException e) {
            // Clean up temp file if it exists
            try {
                Files.deleteIfExists(tempFile);
            } catch (IOException cleanupException) {
                Pokecobbleclaim.LOGGER.warn("Failed to clean up temp file: " + cleanupException.getMessage());
            }
            throw e;
        }
    }

    /**
     * Loads timer data from disk.
     */
    public static FoodPriceTimerData loadTimerData() throws IOException {
        Path priceDir = getFoodPriceDirectory();
        Path timerFile = priceDir.resolve(TIMER_DATA_FILE);

        if (!Files.exists(timerFile)) {
            Pokecobbleclaim.LOGGER.debug("No existing timer data file found");
            return null;
        }

        try (FileInputStream fis = new FileInputStream(timerFile.toFile());
             GZIPInputStream gzis = new GZIPInputStream(fis);
             InputStreamReader reader = new InputStreamReader(gzis, "UTF-8")) {

            FoodPriceTimerData data = GSON.fromJson(reader, FoodPriceTimerData.class);

            if (data == null) {
                Pokecobbleclaim.LOGGER.warn("Loaded null timer data from file");
                return null;
            }

            // Validate loaded data
            if (data.getNextPriceUpdateTime() <= 0) {
                throw new IllegalStateException("Invalid next update time: " + data.getNextPriceUpdateTime());
            }

            Pokecobbleclaim.LOGGER.debug("Loaded timer data from {}", timerFile);
            return data;

        } catch (JsonSyntaxException e) {
            Pokecobbleclaim.LOGGER.error("Corrupted timer data file: " + e.getMessage());
            return null;
        }
    }

    /**
     * Creates a backup of the current timer data.
     */
    public static void createTimerBackup() throws IOException {
        Path priceDir = getFoodPriceDirectory();
        Path timerFile = priceDir.resolve(TIMER_DATA_FILE);

        if (!Files.exists(timerFile)) {
            Pokecobbleclaim.LOGGER.debug("No timer data file to backup");
            return;
        }

        Path backupDir = getBackupDirectory();
        ensureDirectoryExists(backupDir);

        // Create backup filename with timestamp
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
        String backupFileName = TIMER_BACKUP_PREFIX + timestamp + BACKUP_EXTENSION;
        Path backupFile = backupDir.resolve(backupFileName);

        // Copy current data file to backup
        Files.copy(timerFile, backupFile, StandardCopyOption.REPLACE_EXISTING);

        Pokecobbleclaim.LOGGER.debug("Created timer data backup: {}", backupFileName);
    }

    /**
     * Performs automatic backup if conditions are met.
     */
    public static void performAutoBackup() {
        try {
            createBackup();
            createTimerBackup();
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to create automatic backup: " + e.getMessage());
        }
    }
}
