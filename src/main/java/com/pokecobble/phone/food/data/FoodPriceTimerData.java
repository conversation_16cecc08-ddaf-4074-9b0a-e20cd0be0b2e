package com.pokecobble.phone.food.data;

/**
 * Data class for storing and persisting food price timer information.
 */
public class FoodPriceTimerData {
    private long nextPriceUpdateTime;
    private long lastPriceUpdateTime;
    private long dataVersion;
    private long serverStartTime;
    
    // Default constructor for JSON deserialization
    public FoodPriceTimerData() {}
    
    public FoodPriceTimerData(long nextPriceUpdateTime, long lastPriceUpdateTime) {
        this.nextPriceUpdateTime = nextPriceUpdateTime;
        this.lastPriceUpdateTime = lastPriceUpdateTime;
        this.dataVersion = System.currentTimeMillis();
        this.serverStartTime = System.currentTimeMillis();
    }
    
    // Getters
    public long getNextPriceUpdateTime() {
        return nextPriceUpdateTime;
    }
    
    public long getLastPriceUpdateTime() {
        return lastPriceUpdateTime;
    }
    
    public long getDataVersion() {
        return dataVersion;
    }
    
    public long getServerStartTime() {
        return serverStartTime;
    }
    
    // Setters
    public void setNextPriceUpdateTime(long nextPriceUpdateTime) {
        this.nextPriceUpdateTime = nextPriceUpdateTime;
    }
    
    public void setLastPriceUpdateTime(long lastPriceUpdateTime) {
        this.lastPriceUpdateTime = lastPriceUpdateTime;
    }
    
    public void setDataVersion(long dataVersion) {
        this.dataVersion = dataVersion;
    }
    
    public void setServerStartTime(long serverStartTime) {
        this.serverStartTime = serverStartTime;
    }
    
    /**
     * Gets time remaining until next price update in milliseconds.
     */
    public long getTimeUntilNextUpdate() {
        long currentTime = System.currentTimeMillis();
        return Math.max(0, nextPriceUpdateTime - currentTime);
    }
    
    /**
     * Checks if a price update is due.
     */
    public boolean isUpdateDue() {
        return getTimeUntilNextUpdate() <= 0;
    }
    
    /**
     * Gets formatted time string until next update.
     */
    public String getFormattedTimeUntilNextUpdate() {
        long totalSeconds = getTimeUntilNextUpdate() / 1000;
        
        if (totalSeconds <= 0) {
            return "Updating soon...";
        }
        
        long hours = totalSeconds / 3600;
        long minutes = (totalSeconds % 3600) / 60;
        long seconds = totalSeconds % 60;
        
        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
        } else if (minutes > 0) {
            return String.format("%02d:%02d", minutes, seconds);
        } else {
            return String.format("00:%02d", seconds);
        }
    }
    
    @Override
    public String toString() {
        return "FoodPriceTimerData{" +
                "nextPriceUpdateTime=" + nextPriceUpdateTime +
                ", lastPriceUpdateTime=" + lastPriceUpdateTime +
                ", dataVersion=" + dataVersion +
                ", serverStartTime=" + serverStartTime +
                ", timeUntilNext=" + getFormattedTimeUntilNextUpdate() +
                '}';
    }
}
