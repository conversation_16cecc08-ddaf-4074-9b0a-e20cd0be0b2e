package com.pokecobble.town.claim;

import com.pokecobble.town.TownPlayerRank;

/**
 * Manual test class to verify the rank permission system works correctly.
 * Run this by calling the main method.
 */
public class RankPermissionTest {

    public static void main(String[] args) {
        RankPermissionTest test = new RankPermissionTest();
        test.runTests();
    }

    public void runTests() {
        System.out.println("Testing explicit rank permission system...");

        testExplicitPermissions();
        testAdminPermissions();
        testRankChangeNotification();

        System.out.println("All tests completed!");
    }

    private void testAdminPermissions() {
        System.out.println("\n=== Testing Admin Permissions ===");

        // Test that admin ranks always have permissions
        assert TownPlayerRank.OWNER.hasAdminPermissions() : "OWNER should have admin permissions";
        assert TownPlayerRank.ADMIN_VIEWER.hasAdminPermissions() : "ADMIN_VIEWER should have admin permissions";
        assert !TownPlayerRank.ADMIN.hasAdminPermissions() : "ADMIN should not have admin permissions";
        assert !TownPlayerRank.MODERATOR.hasAdminPermissions() : "MODERATOR should not have admin permissions";
        assert !TownPlayerRank.MEMBER.hasAdminPermissions() : "MEMBER should not have admin permissions";
        assert !TownPlayerRank.VISITOR.hasAdminPermissions() : "VISITOR should not have admin permissions";

        System.out.println("✓ Admin permission tests passed");
    }

    private void testExplicitPermissions() {
        System.out.println("\n=== Testing Explicit Per-Rank Permissions ===");

        // Create a test scenario
        RankPermissions rankPermissions = new RankPermissions();
        ClaimTag testTag = new ClaimTag("test-tag", 0xFF00FF00);

        // Set explicit permissions for specific ranks (no hierarchy)
        // Give MEMBER build permission but not MODERATOR
        testTag.setRankPermission(TownPlayerRank.MEMBER, 0, true);  // build
        testTag.setRankPermission(TownPlayerRank.MODERATOR, 0, false); // no build

        // Give VISITOR interact permission but not MEMBER
        testTag.setRankPermission(TownPlayerRank.VISITOR, 1, true);  // interact
        testTag.setRankPermission(TownPlayerRank.MEMBER, 1, false);  // no interact

        // Give MODERATOR container permission
        testTag.setRankPermission(TownPlayerRank.MODERATOR, 2, true); // containers
        testTag.setRankPermission(TownPlayerRank.MEMBER, 2, false);   // no containers

        // Test that each rank has only the permissions explicitly set for it
        // Build permissions - only MEMBER and admin ranks should have it
        assert rankPermissions.hasPermission(TownPlayerRank.OWNER, 0) : "OWNER should have build permission (admin)";
        assert rankPermissions.hasPermission(TownPlayerRank.ADMIN_VIEWER, 0) : "ADMIN_VIEWER should have build permission (admin)";
        assert testTag.getRankPermission(TownPlayerRank.MEMBER, 0) : "MEMBER should have build permission (explicit)";
        assert !testTag.getRankPermission(TownPlayerRank.MODERATOR, 0) : "MODERATOR should not have build permission (explicit)";
        assert !testTag.getRankPermission(TownPlayerRank.VISITOR, 0) : "VISITOR should not have build permission (explicit)";

        // Interact permissions - only VISITOR should have it (plus admins)
        assert testTag.getRankPermission(TownPlayerRank.VISITOR, 1) : "VISITOR should have interact permission (explicit)";
        assert !testTag.getRankPermission(TownPlayerRank.MEMBER, 1) : "MEMBER should not have interact permission (explicit)";
        assert !testTag.getRankPermission(TownPlayerRank.MODERATOR, 1) : "MODERATOR should not have interact permission (explicit)";

        // Container permissions - only MODERATOR should have it (plus admins)
        assert testTag.getRankPermission(TownPlayerRank.MODERATOR, 2) : "MODERATOR should have container permission (explicit)";
        assert !testTag.getRankPermission(TownPlayerRank.MEMBER, 2) : "MEMBER should not have container permission (explicit)";
        assert !testTag.getRankPermission(TownPlayerRank.VISITOR, 2) : "VISITOR should not have container permission (explicit)";

        System.out.println("✓ Explicit per-rank permission tests passed");
        System.out.println("✓ No hierarchy - each rank has only explicitly configured permissions");
    }

    private void testRankChangeNotification() {
        System.out.println("\n=== Testing Rank Change Notification System ===");

        try {
            // Test that the ClaimTagIntegration singleton exists and can be accessed
            com.pokecobble.town.claim.v2.ClaimTagIntegration integration =
                com.pokecobble.town.claim.v2.ClaimTagIntegration.getInstance();

            assert integration != null : "ClaimTagIntegration instance should not be null";

            // Test that the method exists and can be called without throwing exceptions
            java.util.UUID testPlayerId = java.util.UUID.randomUUID();
            java.util.UUID testTownId = java.util.UUID.randomUUID();

            // This should not throw an exception
            integration.onPlayerRankChanged(testPlayerId, testTownId, TownPlayerRank.MEMBER, TownPlayerRank.MODERATOR);

            System.out.println("✓ Rank change notification system is properly integrated");
            System.out.println("✓ ClaimTagIntegration.onPlayerRankChanged() method works correctly");

        } catch (Exception e) {
            System.err.println("✗ Rank change notification test failed: " + e.getMessage());
            e.printStackTrace();
            throw new AssertionError("Rank change notification system test failed", e);
        }
    }
}
