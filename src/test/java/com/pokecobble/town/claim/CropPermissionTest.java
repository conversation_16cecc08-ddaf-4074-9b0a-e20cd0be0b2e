package com.pokecobble.town.claim;

import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.claim.v2.ChunkPermissionEngine;
import net.minecraft.block.*;
import net.minecraft.item.HoeItem;
import net.minecraft.item.Items;

/**
 * Test class to verify that crop permissions work correctly for:
 * 1. Breaking crop blocks
 * 2. Hoe interactions with grass blocks
 * 3. Farming-related block interactions
 */
public class CropPermissionTest {
    
    public static void main(String[] args) {
        CropPermissionTest test = new CropPermissionTest();
        test.runAllTests();
    }
    
    public void runAllTests() {
        System.out.println("=== CROP PERMISSION SYSTEM TEST ===");
        
        testCropBlockIdentification();
        testCropPermissionConstants();
        testClaimTagCropPermissions();
        testHoeItemIdentification();
        testCropSeedIdentification();
        
        System.out.println("\n=== ALL CROP PERMISSION TESTS COMPLETED ===");
        System.out.println("✓ Crop permission system is working correctly");
    }
    
    /**
     * Test that crop blocks are correctly identified.
     */
    private void testCropBlockIdentification() {
        System.out.println("\n--- Testing Crop Block Identification ---");
        
        // Test that common crop blocks are identified correctly
        Block wheatBlock = Blocks.WHEAT;
        Block carrotBlock = Blocks.CARROTS;
        Block potatoBlock = Blocks.POTATOES;
        Block farmlandBlock = Blocks.FARMLAND;
        Block grassBlock = Blocks.GRASS_BLOCK;
        Block dirtBlock = Blocks.DIRT;
        Block stoneBlock = Blocks.STONE;
        
        // These should be identified as crop blocks
        assert isCropBlock(wheatBlock) : "Wheat should be identified as a crop block";
        assert isCropBlock(carrotBlock) : "Carrots should be identified as a crop block";
        assert isCropBlock(potatoBlock) : "Potatoes should be identified as a crop block";
        
        // These should NOT be identified as crop blocks (farmland breaking requires build permission)
        assert !isCropBlock(farmlandBlock) : "Farmland should not be identified as a crop block (requires build permission to break)";
        assert !isCropBlock(grassBlock) : "Grass should not be identified as a crop block";
        assert !isCropBlock(dirtBlock) : "Dirt should not be identified as a crop block";
        assert !isCropBlock(stoneBlock) : "Stone should not be identified as a crop block";
        
        System.out.println("✓ Crop block identification works correctly");
    }
    
    /**
     * Test that permission constants are correct.
     */
    private void testCropPermissionConstants() {
        System.out.println("\n--- Testing Permission Constants ---");
        
        // Verify that the crops permission constant is correct
        assert ChunkPermissionEngine.PERMISSION_CROPS == 5 : "PERMISSION_CROPS should be 5";
        assert ChunkPermissionEngine.PERMISSION_BUILD == 0 : "PERMISSION_BUILD should be 0";
        
        // Verify permission names
        String cropPermissionName = ChunkPermissionEngine.getPermissionName(ChunkPermissionEngine.PERMISSION_CROPS);
        assert "Crops".equals(cropPermissionName) : "Crops permission name should be 'Crops'";
        
        System.out.println("✓ Permission constants are correct");
    }
    
    /**
     * Test that ClaimTag crop permissions work correctly.
     */
    private void testClaimTagCropPermissions() {
        System.out.println("\n--- Testing ClaimTag Crop Permissions ---");
        
        ClaimTag testTag = new ClaimTag("crop-test", 0xFF00FF00);
        
        // Test setting crop permissions for different ranks
        testTag.setRankPermission(TownPlayerRank.MEMBER, ChunkPermissionEngine.PERMISSION_CROPS, true);
        testTag.setRankPermission(TownPlayerRank.VISITOR, ChunkPermissionEngine.PERMISSION_CROPS, false);
        
        // Test that permissions are set correctly
        boolean memberHasCropPermission = testTag.getRankPermission(TownPlayerRank.MEMBER, ChunkPermissionEngine.PERMISSION_CROPS);
        boolean visitorHasCropPermission = testTag.getRankPermission(TownPlayerRank.VISITOR, ChunkPermissionEngine.PERMISSION_CROPS);
        
        assert memberHasCropPermission : "Member should have crop permission when explicitly set";
        assert !visitorHasCropPermission : "Visitor should not have crop permission when explicitly denied";
        
        // Test that other permissions are not affected
        boolean memberHasBuildPermission = testTag.getRankPermission(TownPlayerRank.MEMBER, ChunkPermissionEngine.PERMISSION_BUILD);
        assert !memberHasBuildPermission : "Member should not have build permission unless explicitly set";
        
        System.out.println("✓ ClaimTag crop permissions work correctly");
    }
    
    /**
     * Test that hoe items are correctly identified.
     */
    private void testHoeItemIdentification() {
        System.out.println("\n--- Testing Hoe Item Identification ---");
        
        // Test that hoe items are correctly identified
        assert Items.WOODEN_HOE instanceof HoeItem : "Wooden hoe should be a HoeItem";
        assert Items.STONE_HOE instanceof HoeItem : "Stone hoe should be a HoeItem";
        assert Items.IRON_HOE instanceof HoeItem : "Iron hoe should be a HoeItem";
        assert Items.GOLDEN_HOE instanceof HoeItem : "Golden hoe should be a HoeItem";
        assert Items.DIAMOND_HOE instanceof HoeItem : "Diamond hoe should be a HoeItem";
        assert Items.NETHERITE_HOE instanceof HoeItem : "Netherite hoe should be a HoeItem";
        
        // Test that non-hoe items are not identified as hoes
        assert !(Items.WOODEN_SWORD instanceof HoeItem) : "Wooden sword should not be a HoeItem";
        assert !(Items.DIRT instanceof HoeItem) : "Dirt should not be a HoeItem";
        
        System.out.println("✓ Hoe item identification works correctly");
    }

    /**
     * Test that crop seed items are correctly identified.
     */
    private void testCropSeedIdentification() {
        System.out.println("\n--- Testing Crop Seed Item Identification ---");

        // Test that crop seed items are correctly identified
        assert isCropSeedItem(Items.WHEAT_SEEDS) : "Wheat seeds should be identified as crop seeds";
        assert isCropSeedItem(Items.CARROT) : "Carrot should be identified as crop seeds";
        assert isCropSeedItem(Items.POTATO) : "Potato should be identified as crop seeds";
        assert isCropSeedItem(Items.BEETROOT_SEEDS) : "Beetroot seeds should be identified as crop seeds";
        assert isCropSeedItem(Items.PUMPKIN_SEEDS) : "Pumpkin seeds should be identified as crop seeds";
        assert isCropSeedItem(Items.MELON_SEEDS) : "Melon seeds should be identified as crop seeds";
        assert isCropSeedItem(Items.NETHER_WART) : "Nether wart should be identified as crop seeds";
        assert isCropSeedItem(Items.SWEET_BERRIES) : "Sweet berries should be identified as crop seeds";
        assert isCropSeedItem(Items.COCOA_BEANS) : "Cocoa beans should be identified as crop seeds";

        // Test that non-seed items are not identified as crop seeds
        assert !isCropSeedItem(Items.DIRT) : "Dirt should not be identified as crop seeds";
        assert !isCropSeedItem(Items.STONE) : "Stone should not be identified as crop seeds";
        assert !isCropSeedItem(Items.WOODEN_HOE) : "Wooden hoe should not be identified as crop seeds";

        System.out.println("✓ Crop seed item identification works correctly");
    }

    /**
     * Helper method to check if a block is crop-related (for breaking - excludes farmland).
     * This mirrors the logic in the permission handlers.
     */
    private boolean isCropBlock(Block block) {
        return block instanceof CropBlock ||
               block instanceof StemBlock ||
               block instanceof AttachedStemBlock ||
               block instanceof ComposterBlock ||
               block instanceof SweetBerryBushBlock ||
               block instanceof CocoaBlock ||
               block instanceof NetherWartBlock ||
               block instanceof BambooBlock ||
               block instanceof SugarCaneBlock ||
               block instanceof CactusBlock ||
               block instanceof KelpBlock ||
               block instanceof SeaPickleBlock;
    }

    /**
     * Helper method to check if an item is a crop seed.
     * This mirrors the logic in the permission handlers.
     */
    private boolean isCropSeedItem(net.minecraft.item.Item item) {
        return item == Items.WHEAT_SEEDS ||
               item == Items.CARROT ||
               item == Items.POTATO ||
               item == Items.BEETROOT_SEEDS ||
               item == Items.PUMPKIN_SEEDS ||
               item == Items.MELON_SEEDS ||
               item == Items.NETHER_WART ||
               item == Items.SWEET_BERRIES ||
               item == Items.COCOA_BEANS;
    }
}
