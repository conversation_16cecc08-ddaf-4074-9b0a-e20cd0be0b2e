package com.pokecobble.town.claim;

import com.pokecobble.town.TownPlayerRank;
import net.minecraft.util.math.ChunkPos;

/**
 * Integration test to verify the protection system is working correctly.
 * This test simulates real-world scenarios to ensure protection works.
 */
public class ProtectionSystemIntegrationTest {
    
    public static void main(String[] args) {
        ProtectionSystemIntegrationTest test = new ProtectionSystemIntegrationTest();
        test.runAllTests();
    }
    
    public void runAllTests() {
        System.out.println("=== PROTECTION SYSTEM INTEGRATION TEST ===");
        
        testExplicitPermissions();
        testRankPermissions();
        testNonMemberPermissions();
        testDefaultBehavior();
        
        System.out.println("\n=== ALL INTEGRATION TESTS COMPLETED ===");
        System.out.println("✓ Protection system is working correctly");
    }
    
    private void testExplicitPermissions() {
        System.out.println("\n--- Testing Explicit Per-Rank Permissions ---");
        
        // Create a test tag with explicit permissions
        ClaimTag testTag = new ClaimTag("Test Protection", 0xFF0000FF);
        
        // Set explicit permissions for each rank independently
        testTag.setRankPermission(TownPlayerRank.VISITOR, 0, false);    // No build
        testTag.setRankPermission(TownPlayerRank.MEMBER, 0, true);      // Can build
        testTag.setRankPermission(TownPlayerRank.MODERATOR, 0, false);  // No build (explicit)
        testTag.setRankPermission(TownPlayerRank.ADMIN, 0, true);       // Can build
        testTag.setRankPermission(TownPlayerRank.OWNER, 0, true);       // Can build (admin)
        
        // Test that each rank has only the permissions explicitly set
        assert !testTag.getRankPermission(TownPlayerRank.VISITOR, 0) : "VISITOR should not have build permission";
        assert testTag.getRankPermission(TownPlayerRank.MEMBER, 0) : "MEMBER should have build permission";
        assert !testTag.getRankPermission(TownPlayerRank.MODERATOR, 0) : "MODERATOR should not have build permission (explicit)";
        assert testTag.getRankPermission(TownPlayerRank.ADMIN, 0) : "ADMIN should have build permission";
        assert testTag.getRankPermission(TownPlayerRank.OWNER, 0) : "OWNER should have build permission";
        
        System.out.println("✓ Explicit per-rank permissions work correctly");
        System.out.println("✓ No hierarchy inheritance - each rank is independent");
    }
    
    private void testRankPermissions() {
        System.out.println("\n--- Testing Rank Permission System ---");
        
        RankPermissions permissions = new RankPermissions();
        
        // Test default permissions (only admin ranks should have permissions)
        assert permissions.hasPermission(TownPlayerRank.OWNER, 0) : "OWNER should have default permissions";
        assert permissions.hasPermission(TownPlayerRank.ADMIN_VIEWER, 0) : "ADMIN_VIEWER should have default permissions";
        assert !permissions.hasPermission(TownPlayerRank.MEMBER, 0) : "MEMBER should not have default permissions";
        assert !permissions.hasPermission(TownPlayerRank.VISITOR, 0) : "VISITOR should not have default permissions";
        
        // Test explicit permission setting
        permissions.setPermission(TownPlayerRank.MEMBER, 1, true);
        assert permissions.hasPermission(TownPlayerRank.MEMBER, 1) : "MEMBER should have permission after setting";
        assert !permissions.hasPermission(TownPlayerRank.VISITOR, 1) : "VISITOR should not have permission";
        assert !permissions.hasPermission(TownPlayerRank.MODERATOR, 1) : "MODERATOR should not have permission";
        
        // Test non-member permissions
        permissions.setPermission(null, 2, true);
        assert permissions.hasPermission(null, 2) : "Non-members should have permission after setting";
        
        System.out.println("✓ Rank permission system works correctly");
        System.out.println("✓ Default permissions are secure (admin only)");
        System.out.println("✓ Explicit permissions override defaults");
    }
    
    private void testNonMemberPermissions() {
        System.out.println("\n--- Testing Non-Member Permissions ---");
        
        ClaimTag publicTag = new ClaimTag("Public Area", 0x00FF00FF);
        ClaimTag privateTag = new ClaimTag("Private Area", 0xFF0000FF);
        
        // Set public tag to allow non-members
        for (int action = 0; action < 8; action++) {
            publicTag.setRankPermission(null, action, true);
        }
        
        // Set private tag to deny non-members
        for (int action = 0; action < 8; action++) {
            privateTag.setRankPermission(null, action, false);
        }
        
        // Test public tag allows non-members
        for (int action = 0; action < 8; action++) {
            assert publicTag.getRankPermission(null, action) : "Public tag should allow non-member action " + action;
        }
        
        // Test private tag denies non-members
        for (int action = 0; action < 8; action++) {
            assert !privateTag.getRankPermission(null, action) : "Private tag should deny non-member action " + action;
        }
        
        System.out.println("✓ Non-member permissions work correctly");
        System.out.println("✓ Public and private areas can be configured");
    }
    
    private void testDefaultBehavior() {
        System.out.println("\n--- Testing Default Behavior ---");
        
        // Test that new tags have secure defaults
        ClaimTag newTag = new ClaimTag("New Tag", 0x808080FF);
        
        // Check that non-members have no permissions by default
        for (int action = 0; action < 8; action++) {
            boolean hasPermission = newTag.getRankPermission(null, action);
            if (hasPermission) {
                System.out.println("Warning: Non-members have default permission for action " + action);
            }
        }
        
        // Check that non-admin ranks have no permissions by default
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            if (!rank.hasAdminPermissions()) {
                for (int action = 0; action < 8; action++) {
                    boolean hasPermission = newTag.getRankPermission(rank, action);
                    if (hasPermission) {
                        System.out.println("Warning: Rank " + rank + " has default permission for action " + action);
                    }
                }
            }
        }
        
        // Check that admin ranks have permissions by default
        assert newTag.getRankPermission(TownPlayerRank.OWNER, 0) : "OWNER should have default permissions";
        assert newTag.getRankPermission(TownPlayerRank.ADMIN_VIEWER, 0) : "ADMIN_VIEWER should have default permissions";
        
        System.out.println("✓ Default behavior is secure");
        System.out.println("✓ New tags deny by default for non-admins");
        System.out.println("✓ Admin ranks have appropriate default permissions");
    }
    
    /**
     * Test helper to simulate permission validation
     */
    private boolean simulatePermissionCheck(TownPlayerRank playerRank, ClaimTag chunkTag, int action) {
        if (playerRank == null) {
            // Non-member
            return chunkTag != null ? chunkTag.getRankPermission(null, action) : false;
        }
        
        // Admin ranks always have permissions
        if (playerRank.hasAdminPermissions()) {
            return true;
        }
        
        // Check tag permissions
        if (chunkTag == null) {
            // No tag - default behavior (allow town members)
            return true;
        }
        
        return chunkTag.getRankPermission(playerRank, action);
    }
    
    /**
     * Prints action name for debugging
     */
    private String getActionName(int action) {
        switch (action) {
            case 0: return "BUILD";
            case 1: return "INTERACT";
            case 2: return "CONTAINERS";
            case 3: return "REDSTONE";
            case 4: return "DOORS";
            case 5: return "CROPS";
            case 6: return "ANIMALS";
            case 7: return "VILLAGERS";
            default: return "UNKNOWN";
        }
    }
}
