---- Minecraft Crash Report ----
// Who set us up the TNT?

Time: 2025-08-04 22:57:52
Description: Rendering screen

java.lang.NoClassDefFoundError: com/pokecobble/town/gui/chat/ChatDisplayArea
	at knot//com.pokecobble.town.gui.MyTownScreen.initializeChatComponents(MyTownScreen.java:7641)
	at knot//com.pokecobble.town.gui.MyTownScreen.renderChatSubcategory(MyTownScreen.java:3504)
	at knot//com.pokecobble.town.gui.MyTownScreen.render(MyTownScreen.java:730)
	at knot//net.minecraft.client.gui.screen.Screen.renderWithTooltip(Screen.java:110)
	at knot//net.minecraft.client.render.GameRenderer.render(GameRenderer.java:945)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1219)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:802)
	at knot//net.minecraft.client.main.Main.main(Main.java:250)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)
Caused by: java.lang.ClassNotFoundException: com.pokecobble.town.gui.chat.ChatDisplayArea
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	at net.fabricmc.loader.impl.launch.knot.KnotClassDelegate.loadClass(KnotClassDelegate.java:226)
	at net.fabricmc.loader.impl.launch.knot.KnotClassLoader.loadClass(KnotClassLoader.java:119)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	... 12 more


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- Head --
Thread: Render thread
Stacktrace:
	at knot//com.pokecobble.town.gui.MyTownScreen.initializeChatComponents(MyTownScreen.java:7641)
	at knot//com.pokecobble.town.gui.MyTownScreen.renderChatSubcategory(MyTownScreen.java:3504)
	at knot//com.pokecobble.town.gui.MyTownScreen.render(MyTownScreen.java:730)
	at knot//net.minecraft.client.gui.screen.Screen.renderWithTooltip(Screen.java:110)

-- Screen render details --
Details:
	Screen name: com.pokecobble.town.gui.MyTownScreen
	Mouse location: Scaled: (65, 175). Absolute: (260.000000, 699.000000)
	Screen size: Scaled: (480, 243). Absolute: (1920, 969). Scale factor of 4.000000
Stacktrace:
	at knot//net.minecraft.client.render.GameRenderer.render(GameRenderer.java:945)
	at knot//net.minecraft.client.MinecraftClient.render(MinecraftClient.java:1219)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:802)
	at knot//net.minecraft.client.main.Main.main(Main.java:250)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Affected level --
Details:
	All players: 2 total; [ClientPlayerEntity['Player1'/44, l='ClientLevel', x=-240.16, y=67.00, z=116.62], OtherClientPlayerEntity['Player2'/43, l='ClientLevel', x=-232.23, y=66.00, z=120.20]]
	Chunk stats: 729, 453
	Level dimension: minecraft:overworld
	Level spawn location: World: (0,89,0), Section: (at 0,9,0 in 0,5,0; chunk contains blocks 0,-64,0 to 15,319,15), Region: (0,0; contains chunks 0,0 to 31,31, blocks 0,-64,0 to 511,319,511)
	Level time: 22033779 game time, 22033779 day time
	Server brand: fabric
	Server type: Non-integrated multiplayer server
Stacktrace:
	at knot//net.minecraft.client.world.ClientWorld.addDetailsToCrashReport(ClientWorld.java:458)
	at knot//net.minecraft.client.MinecraftClient.addDetailsToCrashReport(MinecraftClient.java:2406)
	at knot//net.minecraft.client.MinecraftClient.run(MinecraftClient.java:821)
	at knot//net.minecraft.client.main.Main.main(Main.java:250)
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480)
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74)
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23)
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86)

-- Last reload --
Details:
	Reload number: 1
	Reload reason: initial
	Finished: Yes
	Packs: vanilla, fabric

-- System Details --
Details:
	Minecraft Version: 1.20.1
	Minecraft Version ID: 1.20.1
	Operating System: Linux (amd64) version 6.14.0-27-generic
	Java Version: 17.0.15, Ubuntu
	Java VM Version: OpenJDK 64-Bit Server VM (mixed mode, sharing), Ubuntu
	Memory: 647928424 bytes (617 MiB) / ********** bytes (1028 MiB) up to ********** bytes (7740 MiB)
	CPUs: 16
	Processor Vendor: GenuineIntel
	Processor Name: 11th Gen Intel(R) Core(TM) i7-11700K @ 3.60GHz
	Identifier: Intel64 Family 6 Model 167 Stepping 1
	Microarchitecture: Rocket Lake
	Frequency (GHz): 3.60
	Number of physical packages: 1
	Number of physical CPUs: 8
	Number of logical CPUs: 16
	Graphics card #0 name: GA102 [GeForce RTX 3080]
	Graphics card #0 vendor: NVIDIA Corporation (0x10de)
	Graphics card #0 VRAM (MB): 288.00
	Graphics card #0 deviceId: 0x2206
	Graphics card #0 versionInfo: unknown
	Virtual memory max (MB): 19569.69
	Virtual memory used (MB): 31971.50
	Swap memory total (MB): 8191.99
	Swap memory used (MB): 3991.18
	JVM Flags: 0 total; 
	Fabric Mods: 
		fabric-api: Fabric API 0.92.5*****.1
		fabric-api-base: Fabric API Base 0.4.32+1802ada577
		fabric-api-lookup-api-v1: Fabric API Lookup API (v1) 1.6.37+1802ada577
		fabric-biome-api-v1: Fabric Biome API (v1) 13.0.14+1802ada577
		fabric-block-api-v1: Fabric Block API (v1) 1.0.12+1802ada577
		fabric-block-view-api-v2: Fabric BlockView API (v2) 1.0.3+924f046a77
		fabric-blockrenderlayer-v1: Fabric BlockRenderLayer Registration (v1) 1.1.42+1802ada577
		fabric-client-tags-api-v1: Fabric Client Tags 1.1.3+1802ada577
		fabric-command-api-v1: Fabric Command API (v1) 1.2.35+f71b366f77
		fabric-command-api-v2: Fabric Command API (v2) 2.2.14+1802ada577
		fabric-commands-v0: Fabric Commands (v0) 0.2.52+df3654b377
		fabric-containers-v0: Fabric Containers (v0) 0.1.66+df3654b377
		fabric-content-registries-v0: Fabric Content Registries (v0) 4.0.13+1802ada577
		fabric-convention-tags-v1: Fabric Convention Tags 1.5.6+1802ada577
		fabric-crash-report-info-v1: Fabric Crash Report Info (v1) 0.2.20+1802ada577
		fabric-data-attachment-api-v1: Fabric Data Attachment API (v1) 1.0.2+de0fd6d177
		fabric-data-generation-api-v1: Fabric Data Generation API (v1) 12.3.6+1802ada577
		fabric-dimensions-v1: Fabric Dimensions API (v1) 2.1.55+1802ada577
		fabric-entity-events-v1: Fabric Entity Events (v1) 1.6.1+1c78457f77
		fabric-events-interaction-v0: Fabric Events Interaction (v0) 0.6.4+13a40c6677
		fabric-events-lifecycle-v0: Fabric Events Lifecycle (v0) 0.2.64+df3654b377
		fabric-game-rule-api-v1: Fabric Game Rule API (v1) 1.0.41+1802ada577
		fabric-gametest-api-v1: Fabric Game Test API (v1) 1.2.15+1802ada577
		fabric-item-api-v1: Fabric Item API (v1) 2.1.29+1802ada577
		fabric-item-group-api-v1: Fabric Item Group API (v1) 4.0.14+1802ada577
		fabric-key-binding-api-v1: Fabric Key Binding API (v1) 1.0.38+1802ada577
		fabric-keybindings-v0: Fabric Key Bindings (v0) 0.2.36+df3654b377
		fabric-lifecycle-events-v1: Fabric Lifecycle Events (v1) 2.2.23+1802ada577
		fabric-loot-api-v2: Fabric Loot API (v2) 1.2.3+1802ada577
		fabric-loot-tables-v1: Fabric Loot Tables (v1) 1.1.47+9e7660c677
		fabric-message-api-v1: Fabric Message API (v1) 5.1.10+1802ada577
		fabric-mining-level-api-v1: Fabric Mining Level API (v1) 2.1.52+1802ada577
		fabric-model-loading-api-v1: Fabric Model Loading API (v1) 1.0.4+1802ada577
		fabric-models-v0: Fabric Models (v0) 0.4.3+9386d8a777
		fabric-networking-api-v1: Fabric Networking API (v1) 1.3.13+13a40c6677
		fabric-networking-v0: Fabric Networking (v0) 0.3.53+df3654b377
		fabric-object-builder-api-v1: Fabric Object Builder API (v1) 11.1.5+e35120df77
		fabric-particles-v1: Fabric Particles (v1) 1.1.3+1802ada577
		fabric-recipe-api-v1: Fabric Recipe API (v1) 1.0.23+1802ada577
		fabric-registry-sync-v0: Fabric Registry Sync (v0) 2.3.5+1802ada577
		fabric-renderer-api-v1: Fabric Renderer API (v1) 3.2.2+1802ada577
		fabric-renderer-indigo: Fabric Renderer - Indigo 1.5.3+85287f9f77
		fabric-renderer-registries-v1: Fabric Renderer Registries (v1) 3.2.47+df3654b377
		fabric-rendering-data-attachment-v1: Fabric Rendering Data Attachment (v1) 0.3.39+92a0d36777
		fabric-rendering-fluids-v1: Fabric Rendering Fluids (v1) 3.0.29+1802ada577
		fabric-rendering-v0: Fabric Rendering (v0) 1.1.50+df3654b377
		fabric-rendering-v1: Fabric Rendering (v1) 3.0.9+1802ada577
		fabric-resource-conditions-api-v1: Fabric Resource Conditions API (v1) 2.3.9+1802ada577
		fabric-resource-loader-v0: Fabric Resource Loader (v0) 0.11.12+fb82e9d777
		fabric-screen-api-v1: Fabric Screen API (v1) 2.0.9+1802ada577
		fabric-screen-handler-api-v1: Fabric Screen Handler API (v1) 1.3.32+1802ada577
		fabric-sound-api-v1: Fabric Sound API (v1) 1.0.14+1802ada577
		fabric-transfer-api-v1: Fabric Transfer API (v1) 3.3.6+8dd72ea377
		fabric-transitive-access-wideners-v1: Fabric Transitive Access Wideners (v1) 4.3.2+1802ada577
		fabricloader: Fabric Loader 0.16.13
		java: OpenJDK 64-Bit Server VM 17
		minecraft: Minecraft 1.20.1
		mixinextras: MixinExtras 0.4.1
		pokecobbleclaim: pokecobbleclaim 1.0.0
	Launched Version: Fabric
	Backend library: LWJGL version 3.3.1 SNAPSHOT
	Backend API: NVIDIA GeForce RTX 3080/PCIe/SSE2 GL version 3.2.0 NVIDIA 575.64.03, NVIDIA Corporation
	Window size: 1920x969
	GL Caps: Using framebuffer using OpenGL 3.2
	GL debug messages: 
	Using VBOs: Yes
	Is Modded: Definitely; Client brand changed to 'fabric'
	Type: Client (map_client.txt)
	Graphics mode: fancy
	Resource Packs: fabric
	Current Language: en_us
	CPU: 16x 11th Gen Intel(R) Core(TM) i7-11700K @ 3.60GHz